POST http://localhost:8291/exam/education/materials/upload/entire/book/zip/async
accept: application/json, text/plain, */*
accept-language: zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7
cookie: _ga=GA1.1.1920482370.1734395517; _pk_id.1.7231=b5b9c298b562302a.1734406112.; dongniLoginToken=fff209aca6824543bb7f3e9524fb3841___1; _pk_ref.1.7231=%5B%22%22%2C%22%22%2C1754900045%2C%22%2F%22%5D; _pk_ses.1.7231=1; _ga_VH0YF57JG8=GS2.1.s1754899495$o771$g1$t1754900292$j18$l0$h0; _ga_JRNE0LRCTN=GS2.1.s1754900016$o25$g1$t1754900394$j2$l0$h0
dongni-login: fff209aca6824543bb7f3e9524fb3841___1
function: 02379a43d84810fb218cc9a27c86d85b
origin: https://dalao.dongni100.com
priority: u=1, i
recognitionversion: 2
referer: https://dalao.dongni100.com/v2/homework/list/homework-list
sec-ch-ua: "Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"
sec-ch-ua-mobile: ?0
sec-ch-ua-platform: "Linux"
sec-fetch-dest: empty
sec-fetch-mode: cors
sec-fetch-site: same-origin
timestampss: 1754900407332
user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
Content-Type: application/json;charset=UTF-8

{
  "filePath": "upload/answerCard/zip/homework/2025/8/11/1754900404834_054_ab.zip",
  "schoolId": 3,
  "userId": 9,
  "clientType": 1,
  "teacherId": 1
}

###

