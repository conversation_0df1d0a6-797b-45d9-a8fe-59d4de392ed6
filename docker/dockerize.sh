#!/bin/bash

if [ "${CATALINA_OPTS}" = "" ]; then
  ## 仅在CATALINA_OPTS=""空时进行heap的计算
  if [ "" != "${CATALINA_OPTS_HEAP_PERCENT}" ]; then
    ## heap占比直接计算内存的数值后设置-Xmx -Xms，
    ## 如内存为15617(free -m),设置30，15617*30/100=4685
    ## 则结果为-Xmx4685m -Xms=4685m，即堆大小为 4685m，而实际应用将占用大小约 4685*1.2=5622m
    ## 堆大小默认百分比 该值由实际内存占用为堆占用的1.2倍计算，即 5/6=0.83
    heapPercent=83
    if [ "${CATALINA_OPTS_HEAP_PERCENT}" -gt 100 ]; then
      echo "自动计算heap内存占比${CATALINA_OPTS_HEAP_PERCENT}大于100; 将使用${heapPercent}%"
    else
      heapPercent=${CATALINA_OPTS_HEAP_PERCENT}
    fi
    memTotal=$(free -m | grep Mem | awk '{print $2}')
    memSet=$((memTotal * heapPercent / 100))
    export CATALINA_OPTS_HEAP="-Xmx${memSet}m -Xms${memSet}m"
  elif [ "" != "${CATALINA_OPTS_MEMORY_PERCENT}" ]; then
    ## 内存占比会为整个占用按照heap*1.2的比例进行预留设置heap，
    ## 如内存为15617(free -m)，设置30，15617*30/100=4685, 5685*5/6=3904
    ## 则结果-Xmx3904m -Xms=3904m， 即堆大小为 3904m，而实际应用将占用大小约 4684m
    ## 内存默认百分比 默认按照应用独占服务器设置
    menPercent=100
    if [ "${CATALINA_OPTS_MEMORY_PERCENT}" -gt 100 ]; then
      echo "自动计算总内存占比${CATALINA_OPTS_MEMORY_PERCENT}大于100; 将使用${menPercent}%"
    else
      menPercent=${CATALINA_OPTS_MEMORY_PERCENT}
    fi
    memTotal=$(free -m | grep Mem | awk '{print $2}')
    # memTotal * (menPercent / 100) * ( 5 / 6 )
    memSet=$((memTotal * menPercent * 5 / 100 / 6))
    export CATALINA_OPTS_HEAP="-Xmx${memSet}m -Xms${memSet}m"
  fi
fi

# clean existing/dead jvm hsperfdata files to avoid confusion to jps
rm -rf /tmp/hsperfdata_ops/*

# read wait list
if [ -e /tools/wait.txt ]
then
  readarray -t a</tools/wait.txt
elif [[ ! -z "$WAIT" ]]
then
  a=($WAIT)
fi

# generate wait list parameters for dockerize command
for i in ${!a[@]}; do
   if [[ ${a[$i]} == *:* ]] ; then
     a[$i]="-wait ${a[$i]}"
   else
     a[$i]="-wait http://${a[$i]}:8080/${a[$i]}/version.txt"
   fi
done

# find war or jar
project=$(basename -s .war /tools/tomcat8/webapps/*.war)
if [ "${project}" = "*" ]; then
  project=$(basename -s .jar /tools/projects/*.jar)
  if [ "${project}" != "*" ]; then
    export HQJLPROJECTTYPE="jar"
  fi
fi

# set HQJLPROJECT if not already set
if [ "${HQJLPROJECT}" = "" ] && [ "${project}" != "*" ]; then
  export HQJLPROJECT=${project}
fi

{
CURRENT_CONTAINER_ID=$(cat /proc/self/cgroup | grep "docker" | sed s/\\//\\n/g | tail -1)
CURRENT_CONTAINER_HOSTNAME=$(hostname)

curl -X PUT -d '{"id": "'${CURRENT_CONTAINER_ID}'","name": "'${HQJLPROJECT}'","address": "'${CURRENT_CONTAINER_HOSTNAME}'","port": 12345,"tags": ["service"],"checks": [{"http": "http://'${CURRENT_CONTAINER_HOSTNAME}':12345","interval": "45s"}]}' http://consul:8500/v1/agent/service/register
} || {
  echo "failed to register consul"
}

export CATALINA_OPTS_HQJLPROJECT="-DHQJLPROJECT=${HQJLPROJECT}"

if [ "${CATALINA_OPTS}" == "" ]
then
  export CATALINA_OPTS="$CATALINA_OPTS_HQJLPROJECT $CATALINA_OPTS_EXTRA $CATALINA_OPTS_ASPECTJ $CATALINA_OPTS_SKYWALKING $CATALINA_OPTS_PROMETHEUS   $CATALINA_OPTS_HEAP $CATALINA_OPTS_STACKTRACE $CATALINA_OPTS_DUMP $CATALINA_OPTS_NATIVEMEM $CATALINA_OPTS_GCLOGCONFIG $CATALINA_OPTS_GCCONFIG"
fi

# execute command
if [ "${HQJLPROJECTTYPE}" = "jar" ]; then
  if [ -e "/tools/tomcat8/bin/setenv.sh" ]
  then
      bash /tools/tomcat8/bin/setenv.sh
  fi
  ### exec 用于支持优雅停机 graceful shutdown
  # exec 命令表示接下来执行的命令会取代当前的shell，其会关闭当前的shell process，换到exec后面的命令执行
  # 当前shell dockerize.sh - pid=1
  # 使用exec dockerize xxx 之后，当前进城会关闭，并启动 dockerize xxx - pid=1
  # 将pid=1分配给dockerize是为了将docker stop的信号量传递到真正的业务项目中
  # dockerize接收到信号量后，会自行传递给java -jar xxx.jar
  if [ -d "${HQJL_HOME:-/tools/HQJL}/config/" ]
  then
      exec dockerize -timeout 30s ${a[*]} \
      /usr/bin/java ${CATALINA_OPTS} -jar \
      -Dserver.port=8080 \
      -Dspring.config.additional-location=${HQJL_HOME:-/tools/HQJL}/config/ \
       /tools/projects/"${project}".jar
  else
      exec dockerize -timeout 30s ${a[*]} /usr/bin/java ${CATALINA_OPTS} -jar -Dserver.port=8080 /tools/projects/"${project}".jar
  fi
else
  exec  dockerize -timeout 30s ${a[*]} /tools/tomcat8/bin/catalina.sh ${JPDA} run
fi
