spring.profiles.active=product-siming
server.port=8291
dongni.tomcat.apr.enabled=false


dongni.sensitive.info.secret.key=QkYwN0I2QTI2QUQwQzM1N0I2MThERjhERkUyM0NDMDE=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=false
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=false

spring.datasource.basedata.url=***********************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=******************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni

spring.datasource.tiku.url=*******************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=*************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=**********************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=dnredis
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=1
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=dnmongo
spring.data.mongodb.basedata.port1=27017
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015

# \u8003\u8BD5
spring.data.mongodb.exam.host1=dnmongo
spring.data.mongodb.exam.port1=27017
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015

## \u9898\u5E93
spring.data.mongodb.tiku.host1=dnmongo
spring.data.mongodb.tiku.port1=27017
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=dnmongo
spring.data.mongodb.analysis.port1=27017
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015

## \u4E2D\u95F4\u5E93
spring.data.mongodb.third.host1=dnmongo
spring.data.mongodb.third.port1=27017
spring.data.mongodb.third.database=third_data_transfer
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015

##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F  config/filestorage/common-config.properties
##### \u963F\u91CC\u4E91: oss
##### \u672C\u5730 : seaweedfs
dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=https://smwsyj.xmsmedu.cn:50083/dnfiler/filer
dongni.file-storage.seaweedfs.filer-root-url=http://filer:8080
dongni.file-storage.seaweedfs.admin-header-key=product-siming
dongni.file-storage.seaweedfs.admin-header-value=c23e5957fdad498287962cd963180d41656df921ed5e4f3383a108f673fab7dfc0607d5427234172820922361fe48ad4

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
# SM \u601D\u660E
jyeoo.user.prefix=SM_ZS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://localhost:8491


# \u8BD5\u5377\u89E3\u6790node\u5730\u5740
dongni.node.host.paper=http://dnnginx
dongni.node.host=http://dnnginx

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=siming
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=http://*************:81
dongni.web.auth.server-host.central=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-siming
dongni.web.auth.client.password=77cd2487-4c88-4361-9ea3-be11d58d854c

# \u60A6\u8BAF\u6D4B\u8BD5\u5BF9\u63A5\u53C2\u6570
dongni.third.yuexun.guzhang.host=https://t.yuexunit.com/auth/api/v1.0/
dongni.third.yuexun.guzhang.host-only=https://t.yuexunit.com/
dongni.third.yuexun.guzhang.app-key=365116909711360
dongni.third.yuexun.guzhang.app-secret=596addbce83c4cd4949c766bf62aaea1

dongni.third.yuexun.siming.host=http://*************:81/auth/api/v1.0/
dongni.third.yuexun.siming.host-only=http://*************:81/
dongni.third.yuexun.siming.app-key=729276162682880
dongni.third.yuexun.siming.app-secret=13e7b3cf53f747889f19b24b573d723f
dongni.third.yuexun.siming.redirect-uri=http://zdyj.xmsmedu.cn:81/
dongni.third.yuexun.siming.parent.app-key=738128444592128
dongni.third.yuexun.siming.parent.app-secret=7657e6f9c00344c78f6cb8ce682fe2f2

dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=
