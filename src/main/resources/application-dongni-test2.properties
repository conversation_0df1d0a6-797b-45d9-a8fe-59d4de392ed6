spring.profiles.active=dongni-test2
server.port=8291
dongni.tomcat.apr.enabled=false


dongni.sensitive.info.secret.key=kBwPdo9Ar3rCuwqL6hupIqxfD4b97EGswYMf1hZhuDw=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true


# \u95EE\u9898\u53CD\u9988\u53D1\u9001\u9489\u9489\u7FA4  \u7F51\u9605V3.20 \u5185\u6D4B\u5DE5\u4F5C\u6C9F\u901A\u7FA4#\u95EE\u9898\u53CD\u9988\u673A\u5668\u4EBA
feedback.dingtalk.access-token=55594a578413861ae8090abe9001a5d160140fa378a29de4c237a6681ac5d3b6
feedback.dingtalk.sign-secret=

# \u8003\u8BD5\u4E2D\u505A\u7684\u90E8\u5206\u64CD\u4F5C\u53D1\u9001\u6D88\u606F\u5230\u9489\u9489 \u7F51\u9605V3.26-\u89E3\u8026-\u8BD5\u9898\u8BBE\u7F6E
dongni.exam.dingtalk.notice.access-token=bbb70528ae9f022b5c8bf164844da1a531c7054b7886c35271a0e95e1850f1f0
dongni.exam.dingtalk.notice.sign-secret=SEC7a84398c44d26da793f54f749b9f43799e1fa5614d2a47001c85b20f116ed7d1


## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=31104000

dongni.web.device.security.enabled=false
dongni.operation.log.enabled=false


spring.datasource.basedata.url=***********************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=******************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni


spring.datasource.tiku.url=*******************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=*************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=**********************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

#
# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=dnredis
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true




#spring.redis.basedata.host=**************
#spring.redis.basedata.password=Dongni2015
#spring.redis.basedata.port=37645
#spring.redis.basedata.database=0

#spring.redis.exam.host=**************
#spring.redis.exam.password=Dongni2015
#spring.redis.exam.port=37645
#spring.redis.exam.database=0

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=dnmongo
spring.data.mongodb.basedata.host2=dnmongo
spring.data.mongodb.basedata.port1=27017
spring.data.mongodb.basedata.port2=27017
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015
spring.data.mongodb.basedata.database=base_data

# \u8003\u8BD5
spring.data.mongodb.exam.host1=dnmongo
spring.data.mongodb.exam.host2=dnmongo
spring.data.mongodb.exam.port1=27017
spring.data.mongodb.exam.port2=27017
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015
spring.data.mongodb.exam.database=exam

## \u9898\u5E93
spring.data.mongodb.tiku.host1=dnmongo
spring.data.mongodb.tiku.host2=dnmongo
spring.data.mongodb.tiku.port1=27017
spring.data.mongodb.tiku.port2=27017
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015
spring.data.mongodb.tiku.database=tiku

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=dnmongo
spring.data.mongodb.analysis.host2=dnmongo
spring.data.mongodb.analysis.port1=27017
spring.data.mongodb.analysis.port2=27017
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015
spring.data.mongodb.analysis.database=analysis

## \u7B2C\u4E09\u65B9\u4E2D\u95F4\u5E93
spring.data.mongodb.third.host1=dnmongo
spring.data.mongodb.third.host2=dnmongo
spring.data.mongodb.third.port1=27017
spring.data.mongodb.third.port2=27017
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015
spring.data.mongodb.third.database=third_data_transfer


##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F aliyun-oss.properties
dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=https://dongnitest2.hqjltech.com/dnfiler/filer
dongni.file-storage.seaweedfs.filer-root-url=http://filer:8080
dongni.file-storage.seaweedfs.admin-header-key=dongni-test2
dongni.file-storage.seaweedfs.admin-header-value=c23e5957fdad498287962cd963180d41656df921ed5e4f3383a108f673fab7dfc0607d5427234172820922361fe48ad4



dongni.third.xkw.tuiti.env-name=test2
dongni.third.xkw.tuiti.main-host=https://www.dongni100.com/api
dongni.third.xkw.tuiti.gateway-host=https://openapi.xkw.com
dongni.third.xkw.tuiti.app-id=103121680860400800
dongni.third.xkw.tuiti.secret=d7SHCIiCJYbjFM7C2zddVzvJqDBUzvvk

dongni.third.xkw.zujuan.gateway-host=https://openapi.xkw.com
dongni.third.xkw.zujuan.app-id=103121680860400800
dongni.third.xkw.zujuan.secret=d7SHCIiCJYbjFM7C2zddVzvJqDBUzvvk

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=DN_KF_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=https://dongnitest2.hqjltech.com/dnserv/api


# \u8BD5\u5377\u89E3\u6790node\u5730\u5740
dongni.node.host=https://dongnitest2.hqjltech.com

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.test.17zuoye.net

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=dongni
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://develop.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://opendev.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongniDev
dongni.web.auth.client.password=DongniDev

# \u597D\u4E13\u4E1A\u6D4B\u8BD5\u5BF9\u63A5\u53C2\u6570
dongni.third.haozhuanye.host=https://mysqltest.hqjltech.com/schoolscheduleserv/integration/
dongni.third.haozhuanye.app-key=36vxzplw
dongni.third.haozhuanye.secret-key=1GasIqmukiE/Zc8N8bCj8eTeiyGMoYvbbEM+fSVyP+eg=
dongni.third.haozhuanye.host-only=https://mysqltest.hqjltech.com/

# ================= \u7B2C\u4E09\u65B9 ==================
# \u666E\u5929
dongni.third.putian.host=http://smartedu.mydongtai.cn:8901
dongni.third.putian.client-id=pt-dnjy
dongni.third.putian.client-secret=dn1234
dongni.third.putian.redirect-uri=http://localhost:8086/auth/putian/dongtai
# \u817E\u8BAF\u4E91
dongni.third.tencent.host=https://open.educloud.tencent.com
dongni.third.tencent.app-id=107
dongni.third.tencent.app-key=1aca53f2866fff94b07a112b601e843f
dongni.third.tencent.app-name=MarkPaper
dongni.third.tencent.root-org-ou=xuexiao
##dongniweixindishanfangpingtai
# \u61C2\u4F60\u5FAE\u4FE1\u516C\u4F17\u53F7 WeChatUtil
dongni.wechat.official-accounts.app-id=wxd4e90477f1ce6e62
dongni.wechat.official-accounts.secret=93a56863da7526ea62ac992022b30796
dongni.wechat.official-accounts.mch-id=**********
dongni.wechat.official-accounts.key=630132feefbd4746a76b819fe965puyb
dongni.wechat.official-accounts.redirect-domain=https://mdev.dongni100.com
dongni.wechat.official-accounts.price=dev
dongni.wechat.official-accounts.template-id-exam-result=oVCiQe7m51BOu9twK72wji3ZLIpDwqQSDyFJnnRIAJU
dongni.wechat.official-accounts.template-id-homework-submit=j6i39BqWQmWbLiGXLWabYj1DcbZtuCV9Ihcm-QM9hVo
dongni.wechat.official-accounts.template-id-roll-plan=4YdMXBSYZfj-i6Bw8GWpO9bxbmqRUzShqSgYIadKbSo
dongni.wechat.official-accounts.template-id-entrust-warning=--CwKReJWMn5aS8ZXqWONKd84DuAPKDLmWilCHusNrk

dongni.wechat.teacher-official-accounts.app-id=wxeacc64fe89da732d
dongni.wechat.teacher-official-accounts.secret=6b28e15ade44fa2de66b99bcf222caea
dongni.wechat.teacher-official-accounts.redirect-domain=https://dongnitest2.hqjltech.com
dongni.wechat.teacher-official-accounts.template-id-teacher-homework-finish=

dongni.tensorflow.version=2
#test

dongni.word-to-pdf.env-name=dongni

dongni.word-to-pdf.render-url=http://**************:8291/wordToPdf

ess.config.provider=tencentCloud
ess.config.regionID=ap-nanjing
ess.config.accessKeyId=AKIDHEFAJcxJbqNuSvW3RRPL9z0GYiniR4d7
ess.config.secretKey=gSCtZSG3nUfrvytdBY4cAf3KiAjcknno

ess.scaling.jobs[0].jobId=dn:recognition
ess.scaling.jobs[0].essGroupId=asg-pxhqy6f7
ess.scaling.jobs[0].enabled=true
ess.scaling.jobs[0].keepFreeFactor=0.2
ess.scaling.jobs[0].protectMinute=10
ess.scaling.jobs[0].executorPerNode=2

# \u9F13\u6559\u901A
gu_jiao_tong.host=http://*************:9501

# WusanInside2AccountService#wusanInside2BizCode
dongni.tiku.third.wusan.inside2.biz-code=100008
# WusanInside2Client#wusanInside2Host
dongni.tiku.third.wusan.inside2.host=https://open-test.53inside.com/api
# WusanInside2ApiTq#wusanInside2RequestToken
dongni.tiku.third.wusan.inside2.request-token=5hbggSo0YZzOQx3ByGjFXB6S4mLaLFHy2GR5XICvrg5A6udjnqKiYIHTadFBEvhW66vdN1rxax6yeSqSVHDcC1CkJxIfjO2Ua4QwHa5JFU6T
# WusanInside2IframeService#wusanInside2IframeHost
dongni.tiku.third.wusan.inside2.iframe-host=https://school-test.53inside.com
