spring.profiles.active=product-huaibei
server.port=8291
dongni.tomcat.apr.enabled=true


dongni.sensitive.info.secret.key=YmExM2JiNzM1YjQyOGM0ZDlhZDVhMDM3N2IwMTgyZTI=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=false
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=true

spring.datasource.basedata.url=***************************************************************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=DongniMySqL@HB@2015

spring.datasource.exam.url=**********************************************************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=DongniMySqL@HB@2015

spring.datasource.tiku.url=***********************************************************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=DongniMySqL@HB@2015

spring.datasource.university.url=*****************************************************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=DongniMySqL@HB@2015

spring.datasource.third.url=**************************************************************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=DongniMySqL@HB@2015

# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=r-bp1civf17olotjw8wk.redis.rds.aliyuncs.com
dongni.redis.password=hbsedb@WXJZ4321
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=dds-bp1f316c054926441.mongodb.rds.aliyuncs.com
spring.data.mongodb.basedata.host2=dds-bp1f316c054926442.mongodb.rds.aliyuncs.com
spring.data.mongodb.basedata.port1=3717
spring.data.mongodb.basedata.port2=3717
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData9102@)!(

# \u8003\u8BD5
spring.data.mongodb.exam.host1=dds-bp1f316c054926441.mongodb.rds.aliyuncs.com
spring.data.mongodb.exam.host2=dds-bp1f316c054926442.mongodb.rds.aliyuncs.com
spring.data.mongodb.exam.port1=3717
spring.data.mongodb.exam.port2=3717
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam8102@)!*

## \u9898\u5E93
spring.data.mongodb.tiku.host1=dds-bp1f316c054926441.mongodb.rds.aliyuncs.com
spring.data.mongodb.tiku.host2=dds-bp1f316c054926442.mongodb.rds.aliyuncs.com
spring.data.mongodb.tiku.port1=3717
spring.data.mongodb.tiku.port2=3717
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku7102@)!&

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=dds-bp1f316c054926441.mongodb.rds.aliyuncs.com
spring.data.mongodb.analysis.host2=dds-bp1f316c054926442.mongodb.rds.aliyuncs.com
spring.data.mongodb.analysis.port1=3717
spring.data.mongodb.analysis.port2=3717
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis6102@)!^)!(

## \u4E2D\u95F4\u5E93
spring.data.mongodb.third.host1=dds-bp1f316c054926441.mongodb.rds.aliyuncs.com
spring.data.mongodb.third.host2=dds-bp1f316c054926442.mongodb.rds.aliyuncs.com
spring.data.mongodb.third.port1=3717
spring.data.mongodb.third.port2=3717
spring.data.mongodb.third.database=third
spring.data.mongodb.third.username=third
spring.data.mongodb.third.password=third6102@)!^)!(

##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F
##### \u963F\u91CC\u4E91: oss
dongni.file-storage.type=oss
#dongni.file-storage.cdn-url=//hbxypjxt.8dsun.com
# TODO \u6DEE\u5317\u4E34\u65F6\u57DF\u540D
dongni.file-storage.cdn-url=//cdnhbxypjxt.dongni100.com

### oss \u914D\u7F6E dongni.file-storage.type=oss \u65F6\u751F\u6548
dongni.file-storage.oss.endpoint=oss-cn-hangzhou-internal.aliyuncs.com
dongni.file-storage.oss.access-key-id=LTAI4GDHJP9LCKBxUfvghRhW
dongni.file-storage.oss.access-key-secret=******************************
dongni.file-storage.oss.role-arn=acs:ram::1772802917407836:role/temporary-dong-ni-oss
dongni.file-storage.oss.bucket-name=hbsedb
dongni.file-storage.oss.region=oss-cn-hangzhou

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=HB_ZS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://*************:8491


# \u8BD5\u5377\u89E3\u6790node\u5730\u5740
#dongni.node.host=http://home.hbxypjxt.com
# TODO \u6DEE\u5317\u4E34\u65F6\u57DF\u540D
dongni.node.host=https://hbxypjxt.dongni100.com

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=huaibei
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-huaibei
dongni.web.auth.client.password=143e2b8e-ea17-4963-838c-12ba47474e32

# \u540C\u6B65-\u6DEE\u5317-\u57DF\u540D
dongni.third.huaibei.host=http://edu.k12c.com/oData/


dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=