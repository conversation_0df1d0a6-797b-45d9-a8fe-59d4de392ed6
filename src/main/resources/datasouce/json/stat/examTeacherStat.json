[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id", "hidden": true}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级名称"}, {"name": "excellentRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "优秀率"}, {"name": "goodRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "优良率"}, {"name": "passRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "及格率"}, {"name": "lowScoreRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "低分率"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "averageScoreRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "均分排名"}]