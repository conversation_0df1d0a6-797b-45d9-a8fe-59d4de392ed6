[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id", "hidden": true}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "paperName", "dataType": "String", "structType": "StringType", "alias": "试卷"}, {"name": "structureNumber", "dataType": "String", "structType": "StringType", "alias": "题号"}, {"name": "questionTypeName", "dataType": "String", "structType": "StringType", "alias": "题型"}, {"name": "averageScore", "dataType": "String", "structType": "StringType", "alias": "均分"}, {"name": "fullScoreRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "满分率"}, {"name": "zeroScoreRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "零分率"}, {"name": "difficultyCoefficient", "dataType": "String", "structType": "StringType", "alias": "难度"}, {"name": "discrimination", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "区分度"}, {"name": "passRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "得分率"}]