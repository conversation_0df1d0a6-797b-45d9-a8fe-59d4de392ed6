[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id", "hidden": true}, {"name": "studentId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学生id"}, {"name": "studentNum", "dataType": "String", "structType": "StringType", "alias": "学号"}, {"name": "studentName", "dataType": "String", "structType": "StringType", "alias": "姓名"}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级名称"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "paperId", "dataType": "<PERSON>", "structType": "LongType", "alias": "试卷id"}, {"name": "questionNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "唯一题号"}, {"name": "structureNumber", "dataType": "String", "structType": "StringType", "alias": "题号"}, {"name": "recognitionValue", "dataType": "String", "structType": "StringType", "alias": "学生作答"}, {"name": "finallyScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "得分"}, {"name": "scoreValue", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "分值"}]