[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "areaId", "dataType": "<PERSON>", "structType": "LongType", "alias": "区域id", "hidden": true}, {"name": "areaName", "dataType": "String", "structType": "StringType", "alias": "区域名称"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "participationNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "实参人数"}, {"name": "highestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最高分"}, {"name": "schoolIds", "dataType": "String", "structType": "StringType", "alias": "学校id列表"}, {"name": "totalStudent", "dataType": "Integer", "structType": "IntegerType", "alias": "应参人数"}, {"name": "lines", "alias": "上线分析", "dataType": "Array", "structType": "ArrayType", "expr": "expand(lines,线次,分数,人数,上线率,上临界,下临界)"}]