[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id", "hidden": true}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "averageSubtraction", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "均分差"}, {"name": "averageSubtractionRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "超均率"}, {"name": "avgRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "比均率（B值）"}, {"name": "excellentNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "优秀人数"}, {"name": "excellentRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "优秀率"}, {"name": "goodRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "优良率"}, {"name": "passRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "及格率"}, {"name": "allPassRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "全科及格率"}, {"name": "standardDeviation", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "标准差"}, {"name": "TScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "标准分T"}, {"name": "highestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最高分"}, {"name": "upperQuartile", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "上四分位"}, {"name": "medianValue", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "中位数"}, {"name": "lowerQuartile", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "下四分位"}, {"name": "lowestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最低分"}, {"name": "modeValue", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "众数"}, {"name": "range", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "极差"}, {"name": "participationNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "实参人数"}, {"name": "participationRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "参考率"}, {"name": "averageScoreRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "均分排名"}, {"name": "excellentRateRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "优秀率排名"}, {"name": "passNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "及格人数"}, {"name": "lowScoreNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "低分人数"}, {"name": "lowScoreRate", "dataType": "Double", "structType": "DoubleType", "format": "0.00%", "alias": "低分率"}, {"name": "lowScoreRateRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "低分率排名"}, {"name": "beforeAfterRank", "alias": "排名分布", "dataType": "Array", "structType": "ArrayType", "expr": "expand(beforeAfterRank,名次段,人数)"}, {"name": "proportionBeforeAfterRank", "alias": "排名比例分布", "dataType": "Array", "structType": "ArrayType", "expr": "expand(proportionBeforeAfterRank,名次段,人数)"}, {"name": "scoreSection", "alias": "分数段分布", "dataType": "Array", "structType": "ArrayType", "expr": "expand(scoreSection,分数段,人数)"}, {"name": "rankSection", "alias": "名次段分布", "dataType": "Array", "structType": "ArrayType", "expr": "expand(rankSection,名次段,人数)"}]