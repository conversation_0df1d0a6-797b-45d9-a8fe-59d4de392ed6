[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "studentId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学生id"}, {"name": "studentNum", "dataType": "String", "structType": "StringType", "alias": "学号"}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级"}, {"name": "studentName", "dataType": "String", "structType": "StringType", "alias": "姓名"}, {"name": "resultStatus", "dataType": "String", "structType": "StringType", "alias": "参考状态"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程"}, {"name": "originalScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "原始分"}, {"name": "totalScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "得分"}, {"name": "areaRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "区域排名"}, {"name": "examRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "年级排名"}, {"name": "classRanking", "dataType": "Integer", "structType": "IntegerType", "alias": "班级排名"}, {"name": "consistName", "dataType": "String", "structType": "StringType", "alias": "档次"}, {"name": "upLine", "dataType": "String", "structType": "StringType", "alias": "上线"}]