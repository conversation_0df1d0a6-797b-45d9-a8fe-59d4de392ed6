[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "name", "dataType": "String", "structType": "StringType", "alias": "档次"}, {"name": "totalStudent", "dataType": "Integer", "structType": "IntegerType", "alias": "应参人数"}, {"name": "participationNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "实参人数"}, {"name": "highestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最高分"}, {"name": "lowestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最低分"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "maxScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最大分值"}, {"name": "minScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最小分值"}, {"name": "number", "dataType": "Integer", "structType": "IntegerType", "alias": "人数"}, {"name": "rate", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "比例"}]