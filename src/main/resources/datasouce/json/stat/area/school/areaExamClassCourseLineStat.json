[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级名称"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "totalStudent", "dataType": "Integer", "structType": "IntegerType", "alias": "应参人数"}, {"name": "participationNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "实参人数"}, {"name": "highestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最高分"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "lines", "alias": "上线分析", "dataType": "Array", "structType": "ArrayType", "expr": "expand(lines,线次,单科分数线,单上线,双上线,单科上线率,命中率,贡献率)"}]