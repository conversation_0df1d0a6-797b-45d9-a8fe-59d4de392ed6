[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "areaName", "dataType": "String", "structType": "StringType", "alias": "区域"}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级名称"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "paperName", "dataType": "String", "structType": "StringType", "alias": "试卷"}, {"name": "fullScoreNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "满分人数"}, {"name": "failureNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "不及格人数"}, {"name": "line", "alias": "题型均分", "dataType": "Array", "structType": "ArrayType", "expr": "expand(line,题型,均分,得分率)"}]