[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "schoolId", "dataType": "<PERSON>", "structType": "LongType", "alias": "学校id"}, {"name": "schoolName", "dataType": "String", "structType": "StringType", "alias": "学校"}, {"name": "classId", "dataType": "<PERSON>", "structType": "LongType", "alias": "班级id"}, {"name": "className", "dataType": "String", "structType": "StringType", "alias": "班级名称"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "totalStudent", "dataType": "Integer", "structType": "IntegerType", "alias": "应参人数"}, {"name": "participationNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "实参人数"}, {"name": "highestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最高分"}, {"name": "lowestScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "最低分"}, {"name": "averageScore", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "平均分"}, {"name": "consist", "alias": "档次分析", "dataType": "Array", "structType": "ArrayType", "expr": "expand(consist,档次,最大分值,最小分值,人数,比例)"}]