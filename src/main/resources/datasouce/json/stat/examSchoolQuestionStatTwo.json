[{"name": "examId", "dataType": "<PERSON>", "structType": "LongType", "alias": "考试id", "hidden": true}, {"name": "statId", "dataType": "<PERSON>", "structType": "LongType", "alias": "报告id", "hidden": true}, {"name": "paperId", "dataType": "<PERSON>", "structType": "LongType", "alias": "试卷id"}, {"name": "paperName", "dataType": "String", "structType": "StringType", "alias": "试卷名称"}, {"name": "courseId", "dataType": "<PERSON>", "structType": "LongType", "alias": "课程id"}, {"name": "courseName", "dataType": "String", "structType": "StringType", "alias": "课程名称"}, {"name": "structureNumber", "dataType": "String", "structType": "StringType", "alias": "题号"}, {"name": "questionNumber", "dataType": "Integer", "structType": "IntegerType", "alias": "唯一题号"}, {"name": "questionTypeName", "dataType": "String", "structType": "StringType", "alias": "题型"}, {"name": "readType", "dataType": "String", "structType": "StringType", "alias": "题类"}, {"name": "scoreValue", "dataType": "Double", "structType": "DoubleType", "format": "0.00", "alias": "分值"}, {"name": "answer", "dataType": "String", "structType": "StringType", "alias": "正确答案"}, {"name": "knowledge", "alias": "知识点", "dataType": "Array", "structType": "ArrayType", "expr": "expand(knowledge,标识,知识点,知识点完整路径)"}, {"name": "coreLiteracy", "alias": "核心素养", "dataType": "Array", "structType": "ArrayType", "expr": "expand(mark.coreLiteracy.values,标识,核心素养)"}, {"name": "cognitiveLevel", "alias": "认知层次", "dataType": "Array", "structType": "ArrayType", "expr": "expand(mark.cognitiveLevel.values,标识,认知层次)"}, {"name": "ability", "alias": "学科能力", "dataType": "Array", "structType": "ArrayType", "expr": "expand(mark.ability.values,标识,学科能力)"}, {"name": "difficultyCoefficient", "dataType": "String", "structType": "StringType", "alias": "实测难度值"}, {"name": "difficultyLevel", "dataType": "String", "structType": "StringType", "alias": "实测难度等级"}, {"name": "predictionDifficultyData", "dataType": "String", "structType": "StringType", "alias": "预估难度值"}, {"name": "predictionDifficultyLevel", "dataType": "String", "structType": "StringType", "alias": "预估难度等级"}]