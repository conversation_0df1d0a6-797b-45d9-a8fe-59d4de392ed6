spring.profiles.active=product-aliyun
server.port=8291
#\uFFFD\u0338\uFFFD\uFFFD\uFFFD\uFFFD\u05BB\uFFFD\u02B9\uFFFD\u00F5\u013B\uFFFD\uFFFD\uFFFDid\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u06BD\u0338\uFFFD\uFFFD\uFFFD\u03AC\uFFFD\uFFFD\uFFFD\u0423\uFFFD\uFFFD\u00BD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B1\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFDid\uFFFD\uFFFD\u05B5\uFFFD\uFFFD\uFFFD\uFFFD\u05A4\u022B\uFFFD\uFFFD\u03A8\u04BB
dongni.workbook.envid=1
dongni.tomcat.apr.enabled=false
server.tomcat.max-threads=400

dongni.sensitive.info.secret.key=YzhhMTI5NzcxZWI0ZGRlNjFjODgxNWNhZDI4YWFlMzY=
dongni.login.verify.images.enabled=false
dongni.login.verify.pwd-error.enabled=false
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=false
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=31104000
dongni.web.device.security.enabled=true
dongni.operation.log.enabled=false

feedback.dingtalk.webhook.auth.break-on-success=300f2f08686de96dd0acb5bde3fb2df25d34b56860f9552c07ce4a953d5cc85b:SECa849b96d588c5d58a13c8e6d5cea7da2d74bb6792784bd9d0fa3477fa52e0eca,\
  b93b895c71e2c777cd1b11d8f61069963454dcf0a7b2e21abc2bf0a1ad5b9758:SEC394839808c22c34a01e0926166186e109d65cf60e1ce53f66e13034eb1c45934
feedback.dingtalk.webhook.auth.all=

# \u8003\u8BD5\u4E2D\u505A\u7684\u90E8\u5206\u64CD\u4F5C\u53D1\u9001\u6D88\u606F\u5230\u9489\u9489 \u7F51\u9605V3.26-\u89E3\u8026-\u8BD5\u9898\u8BBE\u7F6E
dongni.exam.dingtalk.notice.access-token=bbb70528ae9f022b5c8bf164844da1a531c7054b7886c35271a0e95e1850f1f0
dongni.exam.dingtalk.notice.sign-secret=SEC7a84398c44d26da793f54f749b9f43799e1fa5614d2a47001c85b20f116ed7d1

##### \u61C2\u4F60api\u524D\u7F00
dongni.server-api.prefix=/api

##kafka ---- \u5929\u95FB
## \u6307\u5B9Akafka \u4EE3\u7406\u5730\u5740\uFF0C\u53EF\u4EE5\u591A\u4E2A
#spring.kafka.bootstrap-servers=open.teewon.net:9092
## \u6307\u5B9A\u9ED8\u8BA4\u6D88\u8D39\u8005group id
#spring.kafka.consumer.group-id=E000007233bb2f33e074ff9a343c34ff5b33b4b
## \u6307\u5B9A\u9ED8\u8BA4topic id
#spring.kafka.template.default-topic= messageDeliveryEvent_E000007
## \u6307\u5B9Alistener \u5BB9\u5668\u4E2D\u7684\u7EBF\u7A0B\u6570\uFF0C\u7528\u4E8E\u63D0\u9AD8\u5E76\u53D1\u91CF
#spring.kafka.listener.concurrency= 3
## \u6BCF\u6B21\u6279\u91CF\u53D1\u9001\u6D88\u606F\u7684\u6570\u91CF
#spring.kafka.producer.batch-size= 1000

## \u8001\u7CFB\u7EDF
#spring.datasource.dongniservice.url=**************************************************************************************************
#spring.datasource.dongniservice.username=dn
#spring.datasource.dongniservice.password=Dongni2015

## \u57FA\u7840\u6570\u636E
spring.datasource.basedata.url=**************************************************************************************************************************\
  &useServerPrepStmts=true&prepStmtCacheSqlLimit=1024\
  &useConfigs=maxPerformance&rewriteBatchedStatements=false&defaultFetchSize=10240
spring.datasource.basedata.username=base_data
spring.datasource.basedata.password=BaseData!@#2019
spring.datasource.basedata.max-active=700
spring.datasource.basedata.min-idle=30
spring.datasource.basedata.initial-size=30

## \u8003\u8BD5
spring.datasource.exam.url=*********************************************************************************************************************\
  &useServerPrepStmts=true&prepStmtCacheSqlLimit=1024\
  &useConfigs=maxPerformance&rewriteBatchedStatements=false&defaultFetchSize=10240
spring.datasource.exam.username=exam
spring.datasource.exam.password=Exam#@!2019
spring.datasource.exam.max-active=700
spring.datasource.exam.min-idle=30
spring.datasource.exam.initial-size=30

## \u9898\u5E93
spring.datasource.tiku.url=**********************************************************************************************\
  &useServerPrepStmts=true&prepStmtCacheSqlLimit=1024\
  &useConfigs=maxPerformance&rewriteBatchedStatements=false&defaultFetchSize=10240
spring.datasource.tiku.username=tiku
spring.datasource.tiku.password=Tiku@#!2019


## \u5927\u5B66\u7AEF
spring.datasource.university.url=****************************************************************************************************\
  &useServerPrepStmts=true&prepStmtCacheSqlLimit=1024\
  &useConfigs=maxPerformance&rewriteBatchedStatements=false&defaultFetchSize=10240
spring.datasource.university.username=university
spring.datasource.university.password=University!#@2019

## \u7B2C\u4E09\u65B9\u4E2D\u95F4\u5E93
spring.datasource.third.url=*************************************************************************************************************\
  &useServerPrepStmts=true&prepStmtCacheSqlLimit=1024\
  &useConfigs=maxPerformance&rewriteBatchedStatements=false&defaultFetchSize=10240
spring.datasource.third.username=third
spring.datasource.third.password=ThirdDataTransfer5102@!^&

# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=hqjlredis
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=true
dongni.redis.pool.test-on-create=true
dongni.redis.pool.test-while-idle=true
dongni.redis.pool.max-total=1023


# \u8BC6\u522B\u8C03\u5EA6redis\u914D\u7F6E.
redis2_host=dnredis2
redis2_port=6379
redis2_database=0
redis2_password=Dongni2015


#spring.redis.basedata.host=*************
#spring.redis.basedata.password=Dongni2015
#spring.redis.basedata.port=37645
#spring.redis.basedata.database=0

#spring.redis.exam.host=*************
#spring.redis.exam.password=Dongni2015
#spring.redis.exam.port=37645
#spring.redis.exam.database=0


# dongniService \u6B63\u5F0F\u73AF\u5883
spring.data.mongodb.dongniservice.host1=************
spring.data.mongodb.dongniservice.host2=************
spring.data.mongodb.dongniservice.port1=34352
spring.data.mongodb.dongniservice.port2=34352
spring.data.mongodb.dongniservice.username=dongniProduct
spring.data.mongodb.dongniservice.password=dongni2015
spring.data.mongodb.dongniservice.database=dongniProduct


# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=************
spring.data.mongodb.basedata.host2=************
spring.data.mongodb.basedata.port1=34352
spring.data.mongodb.basedata.port2=34352
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData!@#2019

# \u8003\u8BD5
spring.data.mongodb.exam.host1=************
spring.data.mongodb.exam.host2=************
spring.data.mongodb.exam.port1=34352
spring.data.mongodb.exam.port2=34352
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam#@!2019

## \u9898\u5E93
spring.data.mongodb.tiku.host1=************
spring.data.mongodb.tiku.host2=************
spring.data.mongodb.tiku.port1=34352
spring.data.mongodb.tiku.port2=34352
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku#!@2019

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=************
spring.data.mongodb.analysis.host2=************
spring.data.mongodb.analysis.port1=34352
spring.data.mongodb.analysis.port2=34352
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis!#@2019
spring.data.mongodb.analysis.max-size=700

## mongoshake\u6570\u636E\u540C\u6B65
spring.data.mongodb.mongoshake.host1=************
spring.data.mongodb.mongoshake.host2=************
spring.data.mongodb.mongoshake.port1=34352
spring.data.mongodb.mongoshake.port2=34352

## \u4E2D\u95F4\u5E93
spring.data.mongodb.third.host1=************
spring.data.mongodb.third.host2=************
spring.data.mongodb.third.port1=34352
spring.data.mongodb.third.port2=34352
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer5102@!^&
spring.data.mongodb.third.database=third_data_transfer
spring.data.mongodb.third.option.max-connection-idle-time=300000

##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F aliyun-oss.properties
dongni.file-storage.type=oss
## TODO
dongni.file-storage.cdn-url=https://cdn.dongni100.com
### oss \u914D\u7F6E dongni.file-storage.type=oss \u65F6\u751F\u6548
dongni.file-storage.oss.endpoint=oss-cn-shenzhen-internal.aliyuncs.com
dongni.file-storage.oss.access-key-id=Q45J2pUct7bl4ILE
dongni.file-storage.oss.access-key-secret=bgUgdLh83RP570bpuJavdhFW2VAiXf
dongni.file-storage.oss.role-arn=acs:ram::1195803250657709:role/dongni-product
dongni.file-storage.oss.bucket-name=dongni-product
dongni.file-storage.oss.web.endpoint=oss.dongni100.com

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali
# \u77ED\u4FE1\u7B7E\u540D\uFF1A\u51C6\u6559\u6167\u5B66
dongni.sms.sign-name=\u51C6\u6559\u6167\u5B66

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=DN_ZS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF\u5730\u5740
# \u90E8\u5206\u60C5\u51B5fat-service\u9700\u8981\u8C03\u7528\u5206\u6790\u7684\u670D\u52A1
dongniAnalysisServer=http://analysis:8080


# node\u5730\u5740
dongni.node.host=https://www.dongni100.com

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

# todo \u6682\u65F6\u63D0\u9AD8\u4E0A\u4F20\u6587\u4EF6\u7684\u5927\u5C0F\u9650\u5236
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=dongni
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com
dongni.server.jimei=http://dongni.xmjmedu.com
dongni.server.putian=http://smartedu.mydongtai.cn:8086
dongni.server.lishu=https://exam.lishuedu.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-product
dongni.web.auth.client.password=860cb2a6-bdb9-4293-b395-0148458c8d31

## \u6D88\u606F\u63A8\u9001
dongni.mq.type=ali-rocket-http
dongni.mq.env=prod
dongni.mq.message.topic=dongni
dongni.mq.messages[0].client=dn
dongni.mq.messages[0].instance=MQ_INST_1195803250657709_BbXyhSW0
dongni.mq.messages[0].topic=dn-homework
dongni.mq.messages[0].queue[0].id=homework-result
dongni.mq.messages[0].queue[1].id=homework-todo

# \u5929\u95FB
dongni.third.teewon.host=http://open.teewon.net:9000
dongni.third.teewon.client-id=c24f651e4d2846fc95f3e6192705d18d
dongni.third.teewon.client-secret=a5533ac8b676e7bb
dongni.third.teewon.x-user-account=TWPAAS1200002380864
dongni.third.teewon.current-org-id=S000336600000000000
dongni.third.teewon.tenant-id=E000007

# \u9898\u5E93\u901A\u77E5\u5F00\u542F \u9700\u8981\u6570\u636E\u8868/\u5B57\u5178\u652F\u6301 \u8BE6\u89C1com.dongni.tiku.third.common.package-info.java
dongni.tiku.notification.enabled=true


# \u817E\u8BAF\u667A\u6167\u6821\u56ED
dongni.third.tencent.school.host=https://oapi.campus.qq.com
dongni.third.tencent.school.app-id=600149
dongni.third.tencent.school.secret-id=600149
dongni.third.tencent.school.secret-key=1de8d6684f3240e2b07fb5ae3daba571

# \u597D\u4E13\u4E1A\u5BF9\u63A5\u53C2\u6570
dongni.third.haozhuanye.host=https://school.nicezhuanye.com/schoolscheduleserv/integration/
dongni.third.haozhuanye.app-key=36vxzplw
dongni.third.haozhuanye.secret-key=1GasIqmukiE/Zc8N8bCj8eTeiyGMoYvbbEM+fSVyP+eg=
dongni.third.haozhuanye.host-only=https://school.nicezhuanye.com/
dongni.third.haozhuanye.jump-url=https://nicezhuanye.dongni100.com/

# \u597D\u4E13\u4E1A\u4E50\u5C71\u73AF\u5883
dongni.third.haozhuanye2.host=http://xgk.lsedu.org.cn/schoolscheduleserv/integration/
dongni.third.haozhuanye2.app-key=lt26fcvs
dongni.third.haozhuanye2.secret-key=1j5NFmMQ7hL5R4TMWwEh5bNFCE/O3vPSr7GOmUmhvjYM=
dongni.third.haozhuanye2.host-only=http://xgk.lsedu.org.cn/

# \u597D\u4E13\u4E1Amongo-analysis\u6570\u636E\u5E93
dongni.third.haozhuanye.mongodb.analysis.database=analysis
hzy.mongo.inputuri.placeholder={dongni.mongo.uri}

# \u60A6\u8BAF\u5BF9\u63A5\u53C2\u6570
dongni.third.yuexun.guzhang.host=http://yuexunedu.com/auth/api/v1.0/
dongni.third.yuexun.guzhang.host-only=http://yuexunedu.com/
dongni.third.yuexun.guzhang.app-key=726348402544640
dongni.third.yuexun.guzhang.app-secret=64c9de6a06b64239ac3ffc1bbd13477e

dongni.third.yuexun.siming.host=http://*************:81/auth/api/v1.0/
dongni.third.yuexun.siming.host-only=http://*************:81/
dongni.third.yuexun.siming.app-key=729276162682880
dongni.third.yuexun.siming.app-secret=13e7b3cf53f747889f19b24b573d723f
dongni.third.yuexun.siming.redirect-uri=http://*************:81/

# \u7269\u601D\u5355\u70B9\u767B\u5F55
dongni.third.wusi.host=https://eduxm.topeti.com/
dongni.third.wusi.app-id=yeKrhw5H
dongni.third.wusi.app-secret=58ac7c77dc2c9a30647a9852fdb53281019d919b
dongni.third.wusi.redirect-uri=https://www.dongni.com

# i\u6559\u80B2\u5355\u70B9\u767B\u5F55
dongni.third.iedu.host=https://www.xmedu.cn/
dongni.third.iedu.api.prefix=https://www.xmedu.cn/
dongni.third.iedu.appid=xmcds4de8be96f4c8f414858e3c0389d210bc
dongni.third.iedu.appkey=6b5e1d29b5f8435cced7b0db6045d02d
dongni.third.iedu.client-id=iedu_dong_ni
dongni.third.iedu.client-key=ff1f46d16a0c4b74ab0362757ac1b969
dongni.third.iedu.redirect-url=https://www.dongni100.com/api/base/data/system/auth/ixiamen/callback
dongni.third.iedu.h5.client-id=iedu_dong_ni_h5
dongni.third.iedu.h5.client-key=97af2a3313c64dd69ee16beeb1a195f8
dongni.third.iedu.h5.redirect-url=https://m.dongni100.com/api/base/data/system/auth/ixiamen/mobile/callback

# \u61C2\u4F60\u5FAE\u4FE1\u7B2C\u4E09\u65B9\u5E73\u53F0
dongni.wechat.open.componentAppId=wxea5f93bafbb775f7
dongni.wechat.open.componentSecret=23c0d8f8708414815323e4ad0de17171
dongni.wechat.open.componentToken=dongni2015
dongni.wechat.open.componentAesKey=RYoTkcUSR1iDEC83SjIRefB64fkwwBp8SwhWY9ZutC8
dongni.wechat.xmwz.appId=wx7bcff6ecf651d851
dongni.wechat.xmwz.templateId=ZwDpxY9R9JLZ9FeH1seMgFageWRHJUHUvNAlv25a61Y

# \u61C2\u4F60\u5FAE\u4FE1\u516C\u4F17\u53F7 WeChatUtil
dongni.wechat.official-accounts.app-id=wx574a1b37f9671e3b
dongni.wechat.official-accounts.secret=d57a4a058867e92bb44b29c2a18851fd
dongni.wechat.official-accounts.mch-id=**********
dongni.wechat.official-accounts.key=a6f7deac760b40959a6a9c106598g9mw
dongni.wechat.official-accounts.redirect-domain=https://m.nicezhuanye.com
dongni.wechat.official-accounts.price=product
dongni.wechat.official-accounts.template-id-exam-result=X_fdWnJCHSCgBFJS0xyqJDlyIYghWr6ZPI8eVxaCi9w
dongni.wechat.official-accounts.template-id-homework-submit=DOwwDPdlkhb5-4GQ93PGj438FuqKvgEFd6rfSkhe9sk
dongni.wechat.official-accounts.template-id-roll-plan=da2009banq5xRJ-xfXXqQ0qbY8KNDRDTksgvAV9WMl8
dongni.wechat.official-accounts.template-id-entrust-warning=D1-BYC38ULZl8nvK8rCZnP248ZqM4WOknQGvOqa7T58

dongni.wechat.teacher-official-accounts.app-id=wxe1817781567ac86e
dongni.wechat.teacher-official-accounts.secret=b261b294993dbc3d5bb5331d627f1f98
dongni.wechat.teacher-official-accounts.redirect-domain=https://m.nicezhuanye.com
dongni.wechat.teacher-official-accounts.template-id-teacher-homework-finish=GZrud-epKmaJR8JHRiRI50mwlrdHHFKSTypWOogYjLo

## \u652F\u4ED8\u5B9D
dongni.alipay.app-id=****************
dongni.alipay.partner-id=****************
dongni.alipay.private-key=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCEOeqgUbGvFFlmYFvikDAstf1fgw0+tA3QkM1hLuEsRUjSNGJ1J1a+E8MJbrDSPB8R7BwnjZ9etGpXNOMZ10+drOVyuhI3u1SXidBNjDzF3UtL/K574kMycgX0ZYVwcBEgiVeIeWw21zIgEAVdE3FgbVmlegCh63rxCKRHHZ0IUGmGe/jevn6TTi4jcaPytMrnA2W74y8uWqw+Bc4mh1zJ+uHiPmDRxTi7NeW1nXm8l/mCMHo63zptC3AygZSYJuyHFbJJ5GJWM3Vz1TpRa+O80Qz949bzj21FZBjHEoWxnKsw0P25mgx42ajRA1LkD+u7R7C20p0m+wnjWxhoM/CdAgMBAAECggEAf0HGYBl/6sKo7/DZPgoLc1yLyDq2qpxbl1llcJzPte3Qkg48nAgKYdFfKlrP7uZR+MvGpQKWWW1F0zxhPWONinD2Ox59ngm69kV6psfLTZsXdsBvpWczyP8YYAi9DmOs+CV2izBhR76AzX7dyqCFw4o6dxoeNbgOkKACizcjBPX8snPZOHNseo7qPnHLxlbdjI4LcyHKsaRgjuCmxPmnKy44Ok9RAVhzrxuvHBeZ3tqfwH4WlAgtQhBQTDTOOCSQ5xzyYk4hyBNI6IF14acLuXcJndCY+xaiT48Y8c1xHzjo3pGeY6PnEPR3MdxLxlHK0X6DiP7QtSGXYpqjrt1b+QKBgQD5GtptJ26uJxNCyLjzFXXD9Qfn2fnyEMWj9xqA9YADc/TiHJWYwIYZczkrL25Ay+EyCibqT7ORiy2NssY2PaxxXxnV1735O6fl44fklJ2a9HfP4Onr0bRiEalZY5SZPuOsTKGMIC0CfRtTxy6hR52mOfulBakxybAKF66YCVz5QwKBgQCH4t20nE1qbVYql4dzFO/62C/RGQgaG3efO22UdMRhIi4FQeHqNvyer7TZA6MChvU1QLvbjGVxTReEZj0XgB+9b7wNSJQX2vT0KgEmrRhC2tQ0oIKxvCt/jHa2le6hcsVuZQ5UKx/2ALrAdI2R94BmPM4CjcSu3HnXyK2rUFRgnwKBgBDN2/BDGbdL0YPO0JMtXm1iqS7dyHuo3/xInhQW+5TADMP4E6tjSWiMGk4se+6JEl7yyl9CEyX9Uoxdg/uCu5BGTxLa4+jdOl1KtQ8Za2xZk3+Fd6I92kJHWLo7+nlAAdHMtZUjUoX5dI95P08H6r2c6bnfIOPH7EUyBcgh0ZDLAoGAPLSvUyaYtzkNS43SZyktZKT8B6/lHLS0piBIfhAEQ1ifsp2SLa5PAR2B74leY5yjIx0+T4DTOOC15uI93KfShjDKlCyebGHXGNVDGmIt+oAEWVZzzK9Bx4Oa7UriBqJdbUocMqHpb184Dw6CyHkHlaUIa7+l5HB+xlXSsDv81gUCgYEA4vU0UPi9Uc2ZYgSD7ij05MELqV2ZdqDLupeIhwLPn1ZnqoyAau5NsGnnLsHNu5nAqj/wCzRyG0zC29EXFAjK7C11gECRoTcdEJguNPLjSgoYXu5hhN9iETbz6/MdmC+a+FShX5vuyt5rSSHgb1HQ/NcxAAN1sO20E5mKjlYn5dw=
dongni.alipay.public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhDnqoFGxrxRZZmBb4pAwLLX9X4MNPrQN0JDNYS7hLEVI0jRidSdWvhPDCW6w0jwfEewcJ42fXrRqVzTjGddPnazlcroSN7tUl4nQTYw8xd1LS/yue+JDMnIF9GWFcHARIIlXiHlsNtcyIBAFXRNxYG1ZpXoAoet68QikRx2dCFBphnv43r5+k04uI3Gj8rTK5wNlu+MvLlqsPgXOJodcyfrh4j5g0cU4uzXltZ15vJf5gjB6Ot86bQtwMoGUmCbshxWySeRiVjN1c9U6UWvjvNEM/ePW849tRWQYxxKFsZyrMND9uZoMeNmo0QNS5A/ru0ewttKdJvsJ41sYaDPwnQIDAQAB
dongni.alipay.alipay-public-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsFf9mDzWiuUytQEFzBxEpIAnI/+gyKDh2Jaks2lY3xssVzXh9UEw0iCTibacOBj4/0fVpiJC/CkXiLNCWfMIG1lD5D19U+wUGeASOLmZW2pI4mKKjhrdBGrMcFMpzqlGSKvJJeWTRiztunoTr09ZpIl4HslonslKNo+OIgtjm30AuNVbuEoKuxrvRoCsC8Ivh325XjTo9ic9vimp+7vJgb+lWSmPwMMCz/k0LrS6K9B6MTqJTmarbPsemGDBYl+i83c4yOVPn/93qrcqcTqAi4IZ2XKXdcE/Ay5oefVHkPU72neJ9fwPpB9AM7JPVYTy+X3eOZ+qkF+HdIprzagwuwIDAQAB
dongni.alipay.gateway=https://openapi.alipay.com/gateway.do
dongni.alipay.notify-url=https://www.dongni100.com/api/base/data/system/account/user/membership/alipay/notify
dongni.alipay.notify-check-url=https://mapi.alipay.com/gateway.do

# \u6CE8\u91CA

# \u5B66\u79D1\u7F51 \u6388\u6743\u767B\u5F55\u6D41\u7A0B \u8DF3\u8F6C/iframe\u65E0\u56DE\u8C03/iframe\u6709\u56DE\u8C03 \u9700\u8981\u5728\u5B66\u79D1\u7F51\u5907\u6848\u57DF\u540D
dongni.third.xkw.oauth.app-id=zxxk96B1H7UU99W8V
dongni.third.xkw.oauth.secret=48f03fdb975a49258407c45172e46ce0
dongni.third.xkw.oauth.oauth-server-url=https://sso.zxxk.com
dongni.third.xkw.oauth.service=http://www.zxxk.com/
dongni.third.xkw.oauth.open-school-id-prefix=DN_NEW_

# \u5B66\u79D1\u7F51\u5F00\u653E\u5E73\u53F0 \u63A8\u9898\u529F\u80FD \u6240\u6709\u5B50\u7CFB\u7EDF\u8981\u8BBF\u95EE\u7684\u4E3B\u7CFB\u7EDF\u4FE1\u606F
dongni.third.xkw.tuiti.env-name=dongni
dongni.third.xkw.tuiti.main-host=https://www.dongni100.com/api
# \u5B66\u79D1\u7F51\u5F00\u653E\u5E73\u53F0 \u63A8\u9898\u529F\u80FD \u7AE0\u8282/\u77E5\u8BC6\u70B9\u63A5\u53E3\u3001\u4E3E\u4E00\u53CD\u4E09\u63A5\u53E3\u3001\u641C\u9898\uFF08\u7CBE\u54C1\uFF09\u3001\u5173\u952E\u8BCD\u641C\u9898 \u8BA1\u6B21\u6536\u8D39
dongni.third.xkw.tuiti.gateway-host=https://openapi.xkw.com
dongni.third.xkw.tuiti.app-id=103121680860400800
dongni.third.xkw.tuiti.secret=d7SHCIiCJYbjFM7C2zddVzvJqDBUzvvk

# \u5B66\u79D1\u7F51\u5F00\u653E\u5E73\u53F0 \u7EC4\u5377\u529F\u80FD iframe\u5D4C\u5165\u540E\u7684\u56DE\u8C03\u83B7\u53D6\u6570\u636E
dongni.third.xkw.zujuan.gateway-host=https://openapi.xkw.com
dongni.third.xkw.zujuan.app-id=103121680860400800
dongni.third.xkw.zujuan.secret=d7SHCIiCJYbjFM7C2zddVzvJqDBUzvvk

# word\u8F6Cpdf \u8BF4\u660E\u89C1application.properties\u6216WordToPdfService
dongni.word-to-pdf.env-name=dongni
#dongni.word-to-pdf.main-host=https://www.dongni100.com/api
dongni.word-to-pdf.render-url=http://**************:8080/wordToPdf


# \u8BC6\u522B\u4F38\u7F29\u7EC4\u914D\u7F6E
ess.config.regionID=cn-shenzhen
ess.config.accessKeyId=LTAI5tH3qeL1ZSzhmt7AbWYE
ess.config.secretKey=******************************

ess.scaling.jobs[0].jobId=dn:recognition
ess.scaling.jobs[0].essGroupId=asg-wz9btwob3z4hcpo7xcf3
ess.scaling.jobs[0].enabled=true
ess.scaling.jobs[0].keepFreeFactor=0.2
ess.scaling.jobs[0].protectMinute=20
ess.scaling.jobs[0].executorPerNode=2

ess.scaling.jobs[1].jobId=dn:be:render
ess.scaling.jobs[1].essGroupId=asg-wz9exv421zls8tkruzy5
ess.scaling.jobs[1].enabled=true
ess.scaling.jobs[1].keepFreeFactor=0.2
ess.scaling.jobs[1].protectMinute=10
ess.scaling.jobs[1].executorPerNode=2
spring.cloud.sentinel.enabled=true
dongni.exception.reporter.mail.enabled=true
management.metrics.export.prometheus.pushgateway.enabled=true



# \u9F13\u6559\u901A
gu_jiao_tong.host=http://************:20080/stage-api

# WusanInside2AccountService#wusanInside2BizCode
dongni.tiku.third.wusan.inside2.biz-code=100017
# WusanInside2Client#wusanInside2Host
dongni.tiku.third.wusan.inside2.host=https://open.53inside.com/api
# WusanInside2ApiTq#wusanInside2RequestToken
dongni.tiku.third.wusan.inside2.request-token=iR5caQiT2SuZqx4rRMXqggKRhKgUKwS6YeBfYGRqg4KQK3kxRSZgzLVyFyi7sRcecGB7BIw9KLPhbfGfMnGlV6qYWjxTu5gV6eGYVxUokkMZGbhpypbW9XdzrMsGkrpAf
# WusanInside2IframeService#wusanInside2IframeHost
dongni.tiku.third.wusan.inside2.iframe-host=https://school.53inside.com

#å¤§æ¨¡å
ai.big.model.url=https://school.nicezhuanye.com/aicenterserv/model/chatSync
ai.big.model.auth.url=https://school.nicezhuanye.com/aicenterserv/auth/getAuthCode
