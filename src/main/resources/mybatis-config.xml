<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>


    <settings>
        <!-- 打印执行的SQL STDOUT_LOGGING SLF4J -->
<!--        <setting name="logImpl" value="STDOUT_LOGGING"/>-->
        <!-- 在null时也调用 setter,适应于返回Map,3.2版本以上可用 -->
        <setting name="callSettersOnNulls" value="true"/>
        <!-- 开启驼峰命名规则 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="logPrefix" value="mapper."/>
    </settings>
    <!--    <mappers>-->
    <!--        <mapper resource="mybatis/exam/recognition/RecognitionItemMapper.xml"/>-->
    <!--    </mappers>-->

    <plugins>
        <!--mybatis 分页插件，详情点进去看-->
        <plugin interceptor="com.dongni.exam.common.mark.mybatis.PageInterceptor">
            <property name="dbType" value="mysql"/>
        </plugin>
    </plugins>
</configuration>