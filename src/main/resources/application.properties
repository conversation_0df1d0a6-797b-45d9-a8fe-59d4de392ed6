# SpringProfilesActiveUtil \u7EDF\u4E00\u7BA1\u7406 spring.profiles.active
spring.profiles.active=@maven.profile@
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration,org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration,org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration

server.forward-headers-strategy=native
server.tomcat.remoteip.protocol-header=X-Forwarded-Proto

#spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=true
spring.jackson.time-zone=GMT+8
spring.servlet.multipart.max-request-size=100MB
# \u04BB\uFFFD\uFFFD\uFFFD\uFFFD\u04B5\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u05B7
dongni.third.yiqi.host=http://gemserver.17zuoye.com

logging.pattern.console=%d{HH:mm:ss} %5level [%thread] [%logger{36}:%L] %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}:%L] -%msg%n
dongni.exception.reporter.mail.from-username=<EMAIL>
dongni.exception.reporter.mail.group2-receivers-map.base=<EMAIL>,<EMAIL>
dongni.exception.reporter.mail.group2-receivers-map.mark=<EMAIL>,<EMAIL>
dongni.exception.reporter.mail.group2-receivers-map.answercard=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
dongni.exception.reporter.mail.group2-receivers-map.analysis=<EMAIL>,<EMAIL>
dongni.exception.reporter.mail.group2-receivers-map.tiku=<EMAIL>,<EMAIL>,<EMAIL>
dongni.exception.reporter.mail.to=<EMAIL>,<EMAIL>

dongni.exception.reporter.mail.enabled=false
## mongoshake\u5E93\u7684\u6570\u636E\u5E93\u540D\u3001\u7528\u6237\u540D\u3001\u5BC6\u7801\uFF0C\u76EE\u524D\u53EA\u6709\u90E8\u5206\u73AF\u5883\u6709\u8FD9\u4E2A\u5E93
spring.data.mongodb.mongoshake.database=mongoshake
spring.data.mongodb.mongoshake.username=mongoshake
spring.data.mongodb.mongoshake.password=mongoshake

spring.application.name=fat-service
management.metrics.export.prometheus.pushgateway.base-url=hqjlpushgateway:9091

# word\u8F6Cpdf
# \u73AF\u5883\u540D\u79F0 dongni\u4E3A\u4E3B\u73AF\u5883\uFF0C\u4F1A\u76F4\u63A5\u8BF7\u6C42renderUrl\u8FDB\u884C\u8F6C\u6362
#dongni.word-to-pdf.env-name=dongni
# \u4E3B\u73AF\u5883host\uFF0C\u5F53envName!=dongni\u65F6\u9700\u8981\u914D\u7F6E\uFF0C\u5C06\u901A\u8FC7\u4E3B\u73AF\u5883\u8FDB\u884C\u8F6C\u6362\uFF0C\u9002\u7528\u4E8Ejimei\u7B49\u4E0A\u7F51\u9700\u8981\u62A5\u5907\u7684\u60C5\u51B5
#dongni.word-to-pdf.main-host=https://www.dongni100.com/api
# word\u8F6Cpdf\u7684\u6E32\u67D3url\uFF0C\u5F53envName==dongni\u65F6\u4F1A\u76F4\u63A5\u8C03\u7528
# TODO \u7531\u4E8E\u6E32\u67D3\u670D\u52A1\u5668\u8FD4\u56DE\u7684\u662F\u4E0D\u5E26host\u7684url(/path/to/1.pdf)\uFF0C\u6240\u4EE5\u8981\u6C42\u4E3B\u73AF\u5883\u5FC5\u987B\u4E0E\u6E32\u67D3\u670D\u52A1\u5171\u7528\u4E00\u4E2Aoss
# **************:8080-cdn.dongni100.com
#dongni.word-to-pdf.render-url=http://**************:8080/wordToPdf

# \u6587\u4EF6\u5B58\u50A8\u7684\u751F\u547D\u5468\u671F(\u5929)-\u7B54\u9898\u5361\u56FE\u7247(t_exam_uploader.answer_card_path) \u9ED8\u8BA43\u5E74 \u6545\u610F\u52A0\u591A1\u4E2A\u6708\u5DE6\u53F3\u5E76\u51D1\u4E2A\u6574 366\u5929*3\u5E74+31\u5929=1129
dongni.file-storage.life-cycle-days.exam-uploader=1130
# \u6A21\u677F\u9884\u89C8\u56FE \u9ED8\u8BA445\u5929
dongni.file-storage.life-cycle-days.standard-image=45
# mysql\u7248\u8BBF\u95EE\u65E5\u5FD7\u5F00\u5173
#dongni.operation.log.enabled=false

# accessLog \u65E5\u5FD7\u5F00\u5173
dongni.access.log.enabled=true
# accessLogDetail \u65E5\u5FD7\u5F00\u5173
dongni.access.log-detail.enabled=true


## \u5FAE\u4FE1\u5F00\u53D1\u5E73\u53F0 \u7528\u4E8E\u63A7\u5236\u522B\u7684\u516C\u4F17\u53F7\u7684 \u5982\u4E0B\u9762\u90A3\u4E2A\u4E94\u4E2D\u7684
dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=

## \u53A6\u95E8\u4E94\u4E2D \u4E94\u4E2D\u516C\u4F17\u53F7\u63A8\u9001\u6D88\u606F \u751F\u4EA7\u73AF\u5883\u7528
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=

##\u672C\u5730host\u914D\u7F6E************* hqjlkafka
spring.kafka.bootstrap-servers=hqjlkafka:9092

## \u9700\u8981\u53D1\u9001\u9489\u9489\u6D88\u606F\u901A\u77E5\u7684\u64CD\u4F5C\u540D\u79F0
dongni.exam.dingtalk.notice.operation.names=\u6279\u91CF\u8BBE\u7F6E\u667A\u80FD\u6279\u6539
management.metrics.export.prometheus.pushgateway.enabled=false
spring.cloud.sentinel.enabled=false
spring.cloud.sentinel.transport.dashboard=hqjlsentinel:8080
spring.cloud.sentinel.transport.port=8719
spring.cloud.sentinel.datasource.redis.redis.rule-type=flow
spring.cloud.sentinel.datasource.redis.redis.rule-key=sentinelflow
spring.cloud.sentinel.datasource.redis.redis.channel=flowchannel
spring.cloud.sentinel.datasource.redis.redis.host=${dongni.redis.host}
spring.cloud.sentinel.datasource.redis.redis.port=${dongni.redis.port}
spring.cloud.sentinel.datasource.redis.redis.password=${dongni.redis.password}


# \u597D\u4E13\u4E1Aai\u914D\u7F6E\u4E2D\u5FC3-\u76EE\u524D\u6CA1\u6709\u8BF4\u8981\u533A\u5206\u4E0D\u540C\u73AF\u5883
hqjl.aicenter.app.key=76b890ecde8585ac47eb45ce93fafe2b
server.max-http-header-size=65536


spring.datasource.autotest.driverClassName=com.mysql.jdbc.Driver
spring.datasource.autotest.url=**************************************************************************************************************************************
spring.datasource.autotest.username=root
spring.datasource.autotest.password=4dxiern3-*RWFAH

