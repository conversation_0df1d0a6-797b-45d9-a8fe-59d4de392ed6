spring.profiles.active=product-jimei-local
server.port=8291
dongni.tomcat.apr.enabled=false

dongni.tensorflow.version=2

dongni.sensitive.info.secret.key=MzExMTVhYTI1YmU3NGJhNjQ5YWRlYTVlODBlZDI4NDU=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=false

spring.datasource.basedata.url=**********************************************************************************************
spring.datasource.basedata.username=dn2021
spring.datasource.basedata.password=1b5abc6c5

spring.datasource.exam.url=*****************************************************************************************
spring.datasource.exam.username=dn2021
spring.datasource.exam.password=1b5abc6c5

spring.datasource.tiku.url=******************************************************************
spring.datasource.tiku.username=dn2021
spring.datasource.tiku.password=1b5abc6c5

spring.datasource.university.url=************************************************************************
spring.datasource.university.username=dn2021
spring.datasource.university.password=1b5abc6c5

dongni.redis.host=dalao.dongni100.com
dongni.redis.password=Dongni2015
dongni.redis.port=60824
dongni.redis.database=2
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=dalao.dongni100.com
spring.data.mongodb.basedata.port1=60823
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData!@#2019

# \u8003\u8BD5
spring.data.mongodb.exam.host1=dalao.dongni100.com
spring.data.mongodb.exam.port1=60823
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam#@!2019

## \u9898\u5E93
spring.data.mongodb.tiku.host1=dalao.dongni100.com
spring.data.mongodb.tiku.port1=60823
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku#!@2019

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=dalao.dongni100.com
spring.data.mongodb.analysis.port1=60823
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis!#@2019

## mongoshake
spring.data.mongodb.mongoshake.host1=dalao.dongni100.com
spring.data.mongodb.mongoshake.port1=60823

## \u7B2C\u4E09\u65B9\u6570\u636E\u5E93
spring.data.mongodb.third.host1=dalao.dongni100.com
spring.data.mongodb.third.port1=60823
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer!#@2019
spring.data.mongodb.third.database=third_data_transfer
spring.data.mongodb.third.option.max-connection-idle-time=300000

##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F  config/filestorage/common-config.properties
##### \u963F\u91CC\u4E91: oss
##### \u672C\u5730 : seaweedfs
dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=https://dongni.xmjmedu.com/filer
dongni.file-storage.seaweedfs.filer-root-url=https://dongni.xmjmedu.com/filer
dongni.file-storage.seaweedfs.admin-header-key=jimei-local
dongni.file-storage.seaweedfs.admin-header-value=ffd0403f6fce40d99fe03a0bdb0f7c919b4b27657c8144e5a7f7b8d1152169a5c21732f0e0ed4b22b85692decc1c02c5

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=JM_ZS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://localhost:8491


# node\u5730\u5740
dongni.node.host=https://dongni.xmjmedu.com

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=jimei
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-jimei
dongni.web.auth.client.password=405b79f1-fd44-4968-8abc-00dd59460d6b

dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=

# word\u8F6Cpdf \u8BF4\u660E\u89C1application.properties\u6216WordToPdfService
dongni.word-to-pdf.env-name=jimei
dongni.word-to-pdf.main-host=https://www.dongni100.com/api
#dongni.word-to-pdf.render-url=http://**************:8080/wordToPdf