spring.profiles.active=product-lishu
server.port=8291
dongni.tomcat.apr.enabled=true

dongni.sensitive.info.secret.key=ZTg2ZDI2NWUxODI2ZGI5YTgwNGViNWNjOWZmMjQ5YTU=
dongni.login.verify.images.enabled=false
dongni.login.verify.pwd-error.enabled=false
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=31104000
dongni.web.device.security.enabled=true
dongni.operation.log.enabled=true

spring.datasource.basedata.url=****************************************************************************************************************************
spring.datasource.basedata.username=lishu_product
spring.datasource.basedata.password=lishu2017Product

spring.datasource.exam.url=***********************************************************************************************************************
spring.datasource.exam.username=lishu_product
spring.datasource.exam.password=lishu2017Product

spring.datasource.tiku.url=************************************************************************************************
spring.datasource.tiku.username=lishu_product
spring.datasource.tiku.password=lishu2017Product

spring.datasource.university.url=******************************************************************************************************
spring.datasource.university.username=lishu_product
spring.datasource.university.password=lishu2017Product

## \u7B2C\u4E09\u65B9\u4E2D\u95F4\u5E93
spring.datasource.third.url=******************************************************************************************************
spring.datasource.third.username=lishu_product
spring.datasource.third.password=lishu2017Product


# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=*************
dongni.redis.password=Dongni2015
dongni.redis.port=37645
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=*************
spring.data.mongodb.basedata.port1=34352
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData!@#2019

# \u8003\u8BD5
spring.data.mongodb.exam.host1=*************
spring.data.mongodb.exam.port1=34352
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam#@!2019

## \u9898\u5E93
spring.data.mongodb.tiku.host1=*************
spring.data.mongodb.tiku.port1=34352
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku#!@2019

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=*************
spring.data.mongodb.analysis.port1=34352
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis!#@2019
###
spring.data.mongodb.third.host1=*************
spring.data.mongodb.third.port1=34352
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer!#@2019
spring.data.mongodb.third.database=third_data_transfer
spring.data.mongodb.third.option.max-connection-idle-time=300000


##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F  config/filestorage/common-config.properties
##### \u963F\u91CC\u4E91: oss
##### \u672C\u5730 : seaweedfs
dongni.file-storage.type=oss
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=//cdn.lishuedu.com
### oss \u914D\u7F6E dongni.file-storage.type=oss \u65F6\u751F\u6548
dongni.file-storage.oss.endpoint=oss-cn-shenzhen-internal.aliyuncs.com
dongni.file-storage.oss.access-key-id=wymR4n3BFjKf1iXr
dongni.file-storage.oss.access-key-secret=******************************
dongni.file-storage.oss.role-arn=acs:ram::1195803250657709:role/dongni-develop
dongni.file-storage.oss.bucket-name=lishu-product

# \u77ED\u4FE1\u670D\u52A1 Yunpian  Ali
dongni.sms.server=Ali
dongni.sms.access-key-id=LTAI5tSkhGEorqxzayByb7se
dongni.sms.access-key-secret=******************************
dongni.sms.template-code=SMS_180355916
# \u4E0D\u652F\u6301\u4E2D\u6587\uFF0C url\u7F16\u7801\uFF1A\u7ACB\u6570\u6559\u80B2
dongni.sms.sign-name=%e7%ab%8b%e6%95%b0%e6%95%99%e8%82%b2

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=LS_ZS_NEW_

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://*************:8491


# node\u5730\u5740
dongni.node.host=https://exam.lishuedu.com/

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540D
sandbox_host_url=http://127.0.0.1:8291

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=lishu
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-lishu
dongni.web.auth.client.password=81a5869f-66e8-4cd5-8e3f-d64e0d4134c0

dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=

# \u61C2\u4F60\u5FAE\u4FE1\u516C\u4F17\u53F7 WeChatUtil
dongni.wechat.official-accounts.app-id=wx3d4cd5590cd19363
dongni.wechat.official-accounts.secret=95ef1f60c036e2a979c4704d005c9ef0
dongni.wechat.official-accounts.mch-id=**********
dongni.wechat.official-accounts.key=95ef1f60c036e2a979c4704d005c9ef0
dongni.wechat.official-accounts.redirect-domain=https://m.lishuedu.com
dongni.wechat.official-accounts.price=product
dongni.wechat.official-accounts.template-id-exam-result=bpSsN-219ODS301zv-Zq5DYGSuujHwNg-Hjt-bZoJxU
dongni.wechat.official-accounts.template-id-homework-submit=DOwwDPdlkhb5-4GQ93PGj438FuqKvgEFd6rfSkhe9sk
dongni.wechat.official-accounts.template-id-roll-plan=da2009banq5xRJ-xfXXqQ0qbY8KNDRDTksgvAV9WMl8
dongni.wechat.official-accounts.template-id-entrust-warning=

# word\u8F6Cpdf \u8BF4\u660E\u89C1application.properties\u6216WordToPdfService
dongni.word-to-pdf.env-name=lishu
dongni.word-to-pdf.main-host=https://www.dongni100.com/api
#dongni.word-to-pdf.render-url=http://**************:8080/wordToPdf
dongni.exception.reporter.mail.enabled=true