server.port=8291
dongni.tomcat.apr.enabled=false

dongni.sensitive.info.secret.key=kBwPdo9Ar3rCuwqL6hupIqxfD4b97EGswYMf1hZhuDw=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true



## clientType=2 å¾®ä¿¡ tokenæææå3ä¸ªæ
dongni.web.token.expire-seconds.2=7776000

dongni.web.device.security.enabled=false
dongni.operation.log.enabled=true

spring.datasource.basedata.url=***********************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=******************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni


spring.datasource.tiku.url=*******************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=*************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=**********************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

#
# flyway èæ¬åçº§éç½®
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=dnredis
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=false
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

#spring.redis.basedata.host=**************
#spring.redis.basedata.password=Dongni2015
#spring.redis.basedata.port=37645
#spring.redis.basedata.database=0

#spring.redis.exam.host=**************
#spring.redis.exam.password=Dongni2015
#spring.redis.exam.port=37645
#spring.redis.exam.database=0

# åºç¡æ°æ®
spring.data.mongodb.basedata.host1=dnmongo
spring.data.mongodb.basedata.host2=dnmongo
spring.data.mongodb.basedata.port1=27017
spring.data.mongodb.basedata.port2=27017
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015
spring.data.mongodb.basedata.database=base_data

# èè¯
spring.data.mongodb.exam.host1=dnmongo
spring.data.mongodb.exam.host2=dnmongo
spring.data.mongodb.exam.port1=27017
spring.data.mongodb.exam.port2=27017
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015
spring.data.mongodb.exam.database=exam

## é¢åº
spring.data.mongodb.tiku.host1=dnmongo
spring.data.mongodb.tiku.host2=dnmongo
spring.data.mongodb.tiku.port1=27017
spring.data.mongodb.tiku.port2=27017
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015
spring.data.mongodb.tiku.database=tiku

## åæç³»ç»
spring.data.mongodb.analysis.host1=dnmongo
spring.data.mongodb.analysis.host2=dnmongo
spring.data.mongodb.analysis.port1=27017
spring.data.mongodb.analysis.port2=27017
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015
spring.data.mongodb.analysis.database=analysis

## ç¬¬ä¸æ¹ä¸­é´åº
spring.data.mongodb.third.host1=dnmongo
spring.data.mongodb.third.host2=dnmongo
spring.data.mongodb.third.port1=27017
spring.data.mongodb.third.port2=27017
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015
spring.data.mongodb.third.database=third_data_transfer




##### æä»¶å­å¨æå¡ç¸å³éç½®ä¿¡æ¯ aliyun-oss.properties
dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=https://jzjx.jzsmartedu.cn/dnfiler/filer
dongni.file-storage.seaweedfs.filer-root-url=http://filer:8080
dongni.file-storage.seaweedfs.admin-header-key=product-jiaozuo
dongni.file-storage.seaweedfs.admin-header-value=c23e5957fdad498287962cd963180d41656df921ed5e4f3383a108f673fab7dfc0607d5427234172820922361fe48ad4

# ç­ä¿¡æå¡
dongni.sms.server=Ali

# èä¼ç¨æ·æ³¨ååç¼ï¼è¯·ä¿è¯åä¸ªå­ç³»ç»å¯ä¸  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=DN_KF_NEW_

# dongni-analysis æä½ æ°æ®åæç³»ç»
dongniAnalysisServer=http://dnnginx/api

#logging.config=classpath:logback-spring-develop.xml

# è¯å·è§£ænodeå°å
dongni.node.host=http://dnnginx/dnserv

# ä¸èµ·ä½ä¸æ¨é¢å°å
dongni.third.yiqi.host.entrust=http://thanos.test.17zuoye.net

# æ²ç®±ç¯å¢ä¸»æºåå
sandbox_host_url=http://127.0.0.1:8291

# æè¯¾ä¸»æºååå°å
jike_host_url=http://127.0.0.1:9999
# æè¯¾è·åtokençkey
jike_key=

# æè¯¾æ¢åtokençsign
open_app_id_jike=

##### å½åæå¡ä¿¡æ¯
currentServer=jiaozuo
##### æä½ åå
dongni.server=https://www.dongni100.com

## æä½ å¼æ¾å¹³å°ä¿¡æ¯
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-jiaozuo
dongni.web.auth.client.password=os684y91eoi2

# ================= ç¬¬ä¸æ¹ ==================
# æ®å¤©
dongni.third.putian.host=http://smartedu.mydongtai.cn:8901
dongni.third.putian.client-id=pt-dnjy
dongni.third.putian.client-secret=dn1234
dongni.third.putian.redirect-uri=http://localhost:8086/auth/putian/dongtai
# è¾è®¯äº
dongni.third.tencent.host=https://open.educloud.tencent.com
dongni.third.tencent.app-id=107
dongni.third.tencent.app-key=1aca53f2866fff94b07a112b601e843f
dongni.third.tencent.app-name=MarkPaper
dongni.third.tencent.root-org-ou=xuexiao


##å¥½ä¸ä¸
dongni.third.haozhuanye.host=https://jzjx.jzsmartedu.cn/schoolscheduleserv/integration/
dongni.third.haozhuanye.app-key=36vxzplw
dongni.third.haozhuanye.secret-key=1GasIqmukiE/Zc8N8bCj8eTeiyGMoYvbbEM+fSVyP+eg=
dongni.third.haozhuanye.host-only=https://jzjx.jzsmartedu.cn/
dongni.third.haozhuanye.jump-url=https://jzjx.jzsmartedu.cn/dnpc/

##dongniweixindishanfangpingtai
dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=

dongni.tensorflow.version=2
#test

dongni.word-to-pdf.env-name=dongni

dongni.word-to-pdf.render-url=http://**************:8291/wordToPdf

# xkw user login iframe
dongni.third.xkw.oauth.app-id=107371728957356700
dongni.third.xkw.oauth.secret=enufgcvfwAWAwCm1Pi5f5hknCFKD7qFX
dongni.third.xkw.oauth.oauth-server-url=https://sso.zxxk.com
dongni.third.xkw.oauth.service=https://www.zxxk.com/
dongni.third.xkw.oauth.open-school-id-prefix=jzedu
dongni.third.xkw.oauth.extra-encrypt=true
# get paper detail
dongni.third.xkw.zujuan.gateway-host=https://openapi.xkw.com
dongni.third.xkw.zujuan.app-id=107371728957356700
dongni.third.xkw.zujuan.secret=enufgcvfwAWAwCm1Pi5f5hknCFKD7qFX
