spring.profiles.active=product-haikou-local
server.port=8291
dongni.tomcat.apr.enabled=false


dongni.sensitive.info.secret.key=NGYzMWMwOGEzZDU3NDJhMTk1Yzg1OTA3NmM1ZmQ2Zjc=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=true

#clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=false

#mysql\u914D\u7F6E\u6587\u4EF6
spring.datasource.basedata.url=jdbc:mysql://************:60842/base_data?serverTimezone=GMT%2B8&allowMultiQueries=true&useSSL=false
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=jdbc:mysql://************:60842/exam?serverTimezone=GMT%2B8&allowMultiQueries=true&useSSL=false
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni

spring.datasource.tiku.url=jdbc:mysql://************:60842/tiku?serverTimezone=GMT%2B8&useSSL=false
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=jdbc:mysql://************:60842/university?serverTimezone=GMT%2B8&useSSL=false
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=***************************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

#flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=false
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

#redis\u914D\u7F6E\u6587\u4EF6
dongni.redis.host=dalao-internal.dongni100.com
dongni.redis.password=Dongni2015
dongni.redis.port=60844
dongni.redis.database=1
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

#mongo\u914D\u7F6E
spring.data.mongodb.basedata.host1=dalao-internal.dongni100.com
spring.data.mongodb.basedata.host2=dalao-internal.dongni100.com
spring.data.mongodb.basedata.port1=60843
spring.data.mongodb.basedata.port2=60843
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015
spring.data.mongodb.basedata.database=base_data

spring.data.mongodb.exam.host1=dalao-internal.dongni100.com
spring.data.mongodb.exam.host2=dalao-internal.dongni100.com
spring.data.mongodb.exam.port1=60843
spring.data.mongodb.exam.port2=60843
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015
spring.data.mongodb.exam.database=exam

spring.data.mongodb.tiku.host1=dalao-internal.dongni100.com
spring.data.mongodb.tiku.host2=dalao-internal.dongni100.com
spring.data.mongodb.tiku.port1=60843
spring.data.mongodb.tiku.port2=60843
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015
spring.data.mongodb.tiku.database=tiku

spring.data.mongodb.analysis.host1=dalao-internal.dongni100.com
spring.data.mongodb.analysis.host2=dalao-internal.dongni100.com
spring.data.mongodb.analysis.port1=60843
spring.data.mongodb.analysis.port2=60843
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015
spring.data.mongodb.analysis.database=analysis

spring.data.mongodb.third.host1=dalao-internal.dongni100.com
spring.data.mongodb.third.host2=dalao-internal.dongni100.com
spring.data.mongodb.third.port1=60843
spring.data.mongodb.third.port2=60843
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015
spring.data.mongodb.third.database=third_data_transfer

dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=https://yj.hkjyyx.cn/filer
dongni.file-storage.seaweedfs.filer-root-url=https://yj.hkjyyx.cn/filer
dongni.file-storage.seaweedfs.admin-header-key=haikou
dongni.file-storage.seaweedfs.admin-header-value=c23e5957fdad498287962cd963180d41656df921ed5e4f3383a108f673fab7dfc0607d5427234172820922361fe48ad4

#\u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

#\u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00 {jyeoo.user.prefix}{userId}
yeoo.user.prefix=DN_KF_NEW_

#dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://localhost:8491

#\u8BD5\u5377\u89E3\u6790node\u5730\u5740
dongni.node.host.paper=http://dnnginx
dongni.node.host=http://dnnginx

#\u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

#\u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=dongni

#\u61C2\u4F60\u57DF\u540D
dongni.server=http://yj.hkjyyx.cn

# \u597D\u4E13\u4E1A\u5BF9\u63A5\u53C2\u6570
dongni.third.haozhuanye.host=https://xgk.hainan.edu.cn/schoolscheduleserv/integration/
dongni.third.haozhuanye.app-key=aj0ql5o4
dongni.third.haozhuanye.secret-key=1woregAmru7TV567yRve6olCgLKZGE2B3HgJe2Yg/lS8=
dongni.third.haozhuanye.host-only=https://xgk.hainan.edu.cn/
dongni.third.haozhuanye.jump-url=https://yj.hkjyyx.cn/

#\u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://opendev.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongniDev
dongni.web.auth.client.password=DongniDev
##dongniweixindishanfangpingtai
dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=
