<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="StudyGuideWrongMapper">
    <select id="getStudyGuideWrongItem" parameterType="com.dongni.exam.studyguide.bean.param.StudyGuideWrongItemGetParam"
            resultType="com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO">
        SELECT study_guide_wrong_item_id studyGuideWrongItemId,
               student_id studentId,
               question_id questionId,
               exam_id examId,
               course_id courseId,
               paper_id paperId,
               class_id classId,
               mark_question_numbers markQuestionNumbers
        FROM t_study_guide_wrong_item
        WHERE student_id = #{studentId}
            AND course_id = #{courseId}
            AND exam_id = #{examId}
    </select>
</mapper>