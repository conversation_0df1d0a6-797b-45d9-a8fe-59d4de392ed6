<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamDataDeleteMapper">

    <delete id="deleteExam" parameterType="map">
      DELETE FROM t_exam WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamCourse" parameterType="map">
      DELETE FROM t_exam_course WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamPaper" parameterType="map">
      DELETE FROM t_exam_paper WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamSchool" parameterType="map">
      DELETE FROM t_exam_school WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamClass" parameterType="map">
      DELETE FROM t_exam_class WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamStudent" parameterType="map">
     DELETE FROM t_exam_student WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamResult" parameterType="map">
      DELETE FROM t_exam_result WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamResultTemp" parameterType="map">
      DELETE FROM t_exam_result_temp WHERE exam_id = #{examId}
    </delete>

    <!-- TODO sharding7 checkExamId -->
    <delete id="deleteExamItem" parameterType="map">
      DELETE FROM t_exam_item WHERE exam_id = #{examId} and paper_id = #{paperId} limit 10000
    </delete>

    <delete id="deleteExamWorker" parameterType="map">
      DELETE FROM t_exam_worker WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamPermission" parameterType="map">
      DELETE FROM t_exam_permission WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamUploader" parameterType="map">
      DELETE teu,teuc
      FROM t_exam_uploader teu
      LEFT JOIN t_exam_uploader_class teuc ON teu.exam_uploader_id = teuc.exam_uploader_id
      WHERE teu.exam_id = #{examId}
    </delete>

    <delete id="deleteExamUploaderFile" parameterType="map">
      DELETE FROM t_exam_uploader_file WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamSchoolPaper" parameterType="map">
      DELETE FROM t_exam_school_paper WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamClassPaper" parameterType="map">
      DELETE FROM t_exam_class_paper WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamItemComment" parameterType="map">
      DELETE FROM t_exam_item_comment WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamItemMistake" parameterType="map">
      DELETE FROM t_exam_item_mistake WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamItemReread" parameterType="map">
      DELETE FROM t_exam_item_reread WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteExamTag" parameterType="map">
      DELETE FROM t_exam_tag WHERE exam_id = #{examId}
    </delete>

    <delete id="deleteAnswerCard" parameterType="map">
      DELETE FROM t_answer_card WHERE exam_id = #{examId}
    </delete>

    <delete id="deletePaperReadAndRecord" parameterType="map">
      DELETE tpr,trb,tprr
      FROM t_paper_read tpr
      LEFT JOIN t_read_block trb ON tpr.read_block_id = trb.read_block_id
      LEFT JOIN t_paper_mark_record tprr ON tpr.paper_read_id = tprr.paper_read_id
      WHERE tpr.exam_id = #{examId}
    </delete>

    <!-- TODO sharding7 checkExamId -->
    <delete id="deleteWrongItem" parameterType="map">
       DELETE twi,twti
       FROM t_exam_item tei
       INNER JOIN t_wrong_item twi ON tei.exam_item_id = twi.exam_item_id
       LEFT JOIN t_wrong_tag_item twti ON twi.wrong_item_id = twti.wrong_item_id
       WHERE tei.exam_id = #{examId}
        AND tei.paper_id = #{paperId}
        AND tei.question_number = #{questionNumber}
    </delete>

    <delete id="deleteStudyGuideWrongItem" parameterType="long">
      DELETE thwi, thwti
      FROM t_study_guide_wrong_item thwi
      LEFT JOIN t_study_guide_wrong_tag_item thwti
          ON thwi.study_guide_wrong_item_id = thwti.study_guide_wrong_item_id
      WHERE thwi.exam_id = #{examId}
    </delete>

    <delete id="deleteWrongClass" parameterType="map">
       DELETE twi
       FROM t_wrong_class_item twi
       WHERE twi.exam_id = #{examId}
    </delete>

    <delete id="deleteWrongGrade" parameterType="map">
       DELETE twi
       FROM t_wrong_grade_item twi
       WHERE twi.exam_id = #{examId}
    </delete>

    <!-- 删除报告 -->
    <delete id="deleteExamStat" parameterType="map">
        DELETE FROM t_exam_stat WHERE exam_id = #{examId}
    </delete>

    <!-- 删除对比 -->
    <delete id="deleteClassStatCompare" parameterType="map">
        DELETE FROM t_class_stat_compare WHERE exam_id = #{examId}
    </delete>
    <!-- 删除学生互评 -->
    <delete id="deleteExamResultEvaluation" parameterType="map">
   delete  from  t_exam_result_evaluation where exam_id=#{examId}
    </delete>

    <!--成绩导入模式-->
    <delete id="deleteExamPaperScoreImport" parameterType="map">
        DELETE FROM t_exam_paper_score_import WHERE exam_id = #{examId}
    </delete>

  <delete id="deleteExamArea" parameterType="map">
    delete from t_exam_area where exam_id = #{examId}
  </delete>

  <delete id="deleteExamSchoolByIds" parameterType="map">
    delete from t_exam_school where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
  </delete>

  <delete id="deleteExamClassByIds" parameterType="map">
    delete from t_exam_class where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
    <if test="classIds != null">
      and class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="keepClassIds != null and keepClassIds.size() > 0">
      and class_id not in
      <foreach collection="keepClassIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
  </delete>

  <delete id="deleteExamWorkerByIds" parameterType="map">
    delete from t_exam_worker where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamUploaderByIds" parameterType="map">
    delete teu, teuf
    from t_exam_uploader teu
    left join t_exam_uploader_file teuf on teu.exam_uploader_id = teuf.exam_uploader_id
    where teu.exam_id = #{examId} and teu.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="paperId!=null and paperId!=0">
      and teu.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamUploaderClassByIds" parameterType="map">
    delete teuc
    from t_exam_uploader teu
    inner join t_exam_uploader_class teuc ON teu.exam_uploader_id = teuc.exam_uploader_id
    where teu.exam_id = #{examId} and teu.school_id = #{schoolId} and teuc.class_id in
    <foreach collection="classIds" item="classId" separator="," open="(" close=")">
      #{classId}
    </foreach>
    <if test="paperId!=null and paperId!=0">
      and teu.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamUploaderByUploaderClass" parameterType="map">
    delete teu, teuf
    from t_exam_uploader teu
    left join t_exam_uploader_file teuf on teu.exam_uploader_id = teuf.exam_uploader_id
    left join t_exam_uploader_class teuc ON teu.exam_uploader_id = teuc.exam_uploader_id
    where teu.exam_id = #{examId} and teu.school_id = #{schoolId} and teu.upload_type in (2,12) and teuc.exam_uploader_id is null
    <if test="paperId!=null and paperId!=0">
      and teu.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamSchoolPaperByIds" parameterType="map">
    delete from t_exam_school_paper where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="paperId!=null and paperId!=0">
      and paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamClassPaperByIds" parameterType="map">
    delete from t_exam_class_paper where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="paperId!=null and paperId!=0">
      and paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamItemCommentByIds" parameterType="map">
    delete teic
    from t_exam_item tei
    inner join t_exam_item_comment teic on tei.exam_item_id = teic.exam_item_id
    <if test="classIds != null">
      inner join t_exam_student tes on tei.exam_id = tes.exam_id and tei.student_id = tes.student_id
    </if>
    where tei.exam_id = #{examId} and tei.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and tei.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamItemMistakeByIds" parameterType="map">
    delete teim
    from t_exam_item tei
    inner join t_exam_item_mistake teim on tei.exam_item_id = teim.exam_item_id
    <if test="classIds != null">
      inner join t_exam_student tes on tei.exam_id = tes.exam_id and tei.student_id = tes.student_id
    </if>
    where tei.exam_id = #{examId} and tei.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and tei.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamItemRereadByIds" parameterType="map">
    delete teir
    from t_exam_item tei
    inner join t_exam_item_reread teir on tei.exam_item_id = teir.exam_item_id
    <if test="classIds != null">
      inner join t_exam_student tes on tei.exam_id = tes.exam_id and tei.student_id = tes.student_id
    </if>
    where tei.exam_id = #{examId} and tei.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and tei.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamTagByIds" parameterType="map">
    delete
    <if test="classIds == null">
      tet,
    </if> test
    from t_exam_tag tet
    left join t_exam_student_tag test on tet.exam_id = test.exam_id and tet.exam_tag_id = test.exam_tag_id
    <if test="classIds != null">
      left join t_exam_student tes on test.exam_id = tes.exam_id and test.student_id = tes.student_id
    </if>
    where tet.exam_id = #{examId} and tet.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and tet.school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="keepClassIds != null and keepClassIds.size() > 0">
      and tes.class_id not in
      <foreach collection="keepClassIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
  </delete>

  <delete id="deleteExamSchoolStatByIds" parameterType="map">
    delete from t_school_exam_stat where exam_id = #{examId} and stat_id = 0 and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
  </delete>

  <!-- 删除对比 -->
  <delete id="deleteClassStatCompareByIds" parameterType="map">
    delete from t_class_stat_compare where exam_id = #{examId} and school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
  </delete>

  <delete id="deleteWrongItemByIds" parameterType="map">
    delete /*+INL_JOIN(tei,twi,twti)*/ twi,twti
    from t_exam_item tei
    inner join t_wrong_item twi ON tei.exam_item_id = twi.exam_item_id
    left join t_wrong_tag_item twti ON twi.wrong_item_id = twti.wrong_item_id
    WHERE tei.exam_id = #{examId} and tei.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    AND tei.paper_id IN
    <foreach collection="paperIds" item="paperId" open="(" separator="," close=")">
      #{paperId}
    </foreach>
    AND tei.read_type IN (0, 1, 2)
  </delete>

  <delete id="deleteWrongClassByIds" parameterType="map">
    delete twci
    from t_exam_class_paper tecp
    inner join t_wrong_class_item twci on tecp.exam_id = twci.exam_id and tecp.paper_id = twci.paper_id and tecp.class_id = twci.class_id
    where tecp.exam_id = #{examId} and tecp.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
  </delete>

  <delete id="deleteWrongGradeByIds" parameterType="map">
    delete twgi
    from t_exam_school tes
    inner join t_wrong_grade_item twgi on tes.exam_id = twgi.exam_id and tes.grade_id = twgi.grade_id
    where tes.exam_id = #{examId} and tes.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
  </delete>

  <delete id="deleteAnswerCardByIds" parameterType="map">
    delete tac
    from t_exam_student tes
    inner join t_answer_card tac on tes.exam_id = tac.exam_id and tes.student_id = tac.student_id
    <if test="paperId!=null and paperId!=0">
        inner join t_exam_uploader teu on tac.exam_uploader_id = teu.exam_uploader_id
    </if>
    where tes.exam_id = #{examId} and tes.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and teu.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deletePaperReadAndRecordByIds" parameterType="map">
    delete tprr
    from t_exam_item tei
    inner join t_paper_mark_record tprr ON tei.exam_item_id = tprr.exam_item_id
    <if test="classIds != null">
      inner join t_exam_student tes on tei.exam_id = tes.exam_id and tei.student_id = tes.student_id
    </if>
    where tei.exam_id = #{examId} and tei.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and tei.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamItemByIds" parameterType="map">
    delete tei
    from t_exam_student tes
    inner join t_exam_item tei on tes.exam_id = tei.exam_id and tes.student_id = tei.student_id
    where tes.exam_id = #{examId} and tes.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      and tes.class_id in
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and tei.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamStudentAndResultByIds" parameterType="map">
    delete ter, tert
    from t_exam_student tes
    inner join t_exam_result ter on tes.exam_id = ter.exam_id and tes.student_id = ter.student_id
    left join t_exam_result_temp tert on ter.exam_id = tert.exam_id and ter.student_id = tert.student_id and ter.paper_id = tert.paper_id
    where tes.exam_id = #{examId} and tes.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="classIds != null">
      <choose>
        <when test="paperId != null and paperId != 0">
          and ter.class_id in
        </when>
        <otherwise>
          and tes.class_id in
        </otherwise>
      </choose>
      <foreach collection="classIds" item="classId" separator="," open="(" close=")">
        #{classId}
      </foreach>
    </if>
    <if test="paperId!=null and paperId!=0">
      and ter.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamClassPaperByResult" parameterType="map">
    delete tecp
    from t_exam_class_paper tecp
    left join (select exam_id, class_id, paper_id from t_exam_result
    where exam_id = #{examId} and school_id = #{schoolId}
    and class_id in
    <foreach collection="classIds" item="classId" separator="," open="(" close=")">
      #{classId}
    </foreach>
    <if test="paperId != null and paperId != 0">
      and paper_id = #{paperId}
    </if>
    group by class_id, paper_id
    ) t
        on tecp.exam_id = t.exam_id and tecp.class_id= t.class_id and tecp.paper_id = t.paper_id
    where tecp.exam_id =#{examId} and school_id = #{schoolId} and t.exam_id is null
    and tecp.class_id in
    <foreach collection="classIds" item="classId" separator="," open="(" close=")">
      #{classId}
    </foreach>
    <if test="paperId != null and paperId != 0">
      and tecp.paper_id = #{paperId}
    </if>
  </delete>

  <delete id="deleteExamSchoolClassHeader" parameterType="map">
    delete tet
    from t_exam_class tec
    inner join t_exam_teacher tet on tec.exam_id = tet.exam_id and tec.class_id = tet.class_id
    where tec.exam_id = #{examId} and tet.course_id = 0 and tec.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and tec.school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
  </delete>

  <delete id="deleteExamSchoolClassTeacher" parameterType="map">
    delete tet
    from t_exam_paper tep
    inner join t_exam_class_paper tecp on tep.exam_id = tecp.exam_id and tep.paper_id = tecp.paper_id
    inner join t_exam_teacher tet on tecp.exam_id = tet.exam_id and tecp.class_id = tet.class_id
    where tep.exam_id = #{examId} and tet.course_id != 0 and tecp.school_id in
    <foreach collection="schoolIds" item="schoolId" separator="," open="(" close=")">
      #{schoolId}
    </foreach>
    <if test="keepSchoolIds != null and keepSchoolIds.size() > 0">
      and tecp.school_id not in
      <foreach collection="keepSchoolIds" item="schoolId" separator="," open="(" close=")">
        #{schoolId}
      </foreach>
    </if>
    and
    <foreach collection="courseId2member" item="item" open="(" close=")" separator="or">
      tep.course_id = #{item.courseId} and tet.course_id in
      <foreach collection="item.member" item="memberCourseId" open="(" close=")" separator=",">
        #{memberCourseId}
      </foreach>
    </foreach>
  </delete>

  <select id="getStuIdsByHeadClass" parameterType="map" resultType="long">
    select student_id
    from t_exam_student
    where exam_id = #{examId}
      and school_id = #{schoolId}
      and class_id in
    <foreach collection="classIds" item="classId" separator="," open="(" close=")">
      #{classId}
    </foreach>
  </select>

</mapper>
