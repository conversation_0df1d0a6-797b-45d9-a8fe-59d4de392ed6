<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="SystemConfigMapper">

    <!-- 配置项 -->
    <resultMap id="SystemConfigItem" type="map">
        <id column="config_id" property="configId"/>
        <result column="name" property="name" javaType="string"/>
        <result column="key" property="key" javaType="string"/>
        <result column="value" property="value" javaType="string"/>
        <result column="default_value" property="defaultValue" javaType="string"/>
        <result column="status" property="status" javaType="string"/>
        <result column="status" property="status" javaType="string"/>
        <result column="remark" property="remark" javaType="string"/>
    </resultMap>

    <!-- 获取所有的配置项 -->
    <select id="getAll" parameterType="map" resultMap="SystemConfigItem">
        SELECT
        config_id,
        `name`,
        `key`,
        `value`,
        default_value,
        `status`,
        remark
        FROM sys_config
        <where>
            <if test="search != null and search != '' ">
                AND (
                    `name` LIKE concat('%',#{search},'%') OR
                    `key` LIKE concat('%',#{search},'%') OR
                    `remark` LIKE concat('%',#{search},'%')
                )
            </if>
        </where>
    </select>

    <!-- 获取配置项通过指定的key -->
    <select id="getSystemValueByKey" parameterType="map" resultMap="SystemConfigItem">
        SELECT
        config_id,
        `name`,
        `key`,
        `value`,
        default_value,
        `status`,
        remark
        FROM sys_config
        WHERE `key` = #{key}
    </select>

    <!-- 更新配置项 -->
    <update id="updateSystemConfig" parameterType="map">
        UPDATE sys_config
        SET
        `name` = #{name},
        `value` = #{value},
        `status` = #{status},
        remark = #{remark},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE `key` = #{key}
    </update>

    <!-- 更新配置项的值 -->
    <update id="updateSystemConfigValue" parameterType="map">
        UPDATE sys_config
        SET
        `value` = #{value},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE `key` = #{key}
    </update>

    <!-- 更新配置项状态 -->
    <update id="updateSystemConfigStatus" parameterType="map">
        UPDATE sys_config
        SET
        `status` = #{status},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        WHERE `key` = #{key}
    </update>

</mapper>