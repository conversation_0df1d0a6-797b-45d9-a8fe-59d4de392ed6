<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="NotebookGoodsCouponMapper">

    <insert id="insertNotebookGoodsCoupon" parameterType="map" keyProperty="notebookGoodsCouponId" useGeneratedKeys="true">
        INSERT INTO t_notebook_goods_coupon(
            notebook_goods_coupon_id,
            notebook_goods_id,
            discount,
            discount_type,
            total_count,
            status,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        )
        VALUES (
            #{notebookGoodsCouponId},
            #{notebookGoodsId},
            #{discount},
            #{discountType},
            #{totalCount},
            #{status},
            #{userId},
            #{userName},
            #{currentTime},
            #{userId},
            #{userName},
            #{currentTime}
        )
    </insert>

    <select id="getEnableNotebookGoodsCoupon" resultType="map" parameterType="map">
        select
        notebook_goods_id notebookGoodsId,
        notebook_goods_coupon_id notebookGoodsCouponId,
        discount_type discountType,
        discount discount,
        total_count totalCount
        from t_notebook_goods_coupon
        where notebook_goods_id = #{notebookGoodsId} and status = 1
        order by total_count
    </select>

    <select id="getNotebookGoodsCoupon" resultType="map" parameterType="map">
        select
        notebook_goods_id notebookGoodsId,
        notebook_goods_coupon_id notebookGoodsCouponId,
        discount_type discountType,
        discount discount,
        status,
        total_count totalCount
        from t_notebook_goods_coupon
        where notebook_goods_id = #{notebookGoodsId}
        order by total_count
    </select>

    <select id="getNotebookGoodsCouponStatus" resultType="map" parameterType="map">
        select
        notebook_goods_id notebookGoodsId,
        notebook_goods_coupon_id notebookGoodsCouponId,
        discount_type discountType,
        discount discount,
        status
        from t_notebook_goods_coupon
        where notebook_goods_id = #{notebookGoodsId} limit 1
    </select>

    <update id="updateNotebookGoodsCoupon" parameterType="map">
        update t_notebook_goods_coupon
        set
        total_count = #{totalCount},
        discount_type = #{discountType},
        discount = #{discount},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        where notebook_goods_coupon_id = #{notebookGoodsCouponId}
    </update>

    <delete id="deleteNotebookGoodsCoupon" parameterType="map">
        delete from t_notebook_goods_coupon where notebook_goods_coupon_id = #{notebookGoodsCouponId}
    </delete>

    <update id="updateEnableNotebookGoodsCoupon" parameterType="map">
        update t_notebook_goods_coupon
        set
        status = #{status},
        modifier_id = #{userId},
        modifier_name = #{userName},
        modify_date_time = #{currentTime}
        where notebook_goods_id = #{notebookGoodsId}
    </update>


</mapper>