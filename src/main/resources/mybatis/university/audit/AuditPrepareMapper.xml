<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="AuditPrepareMapper">

    <!-- 查询学校年级 -->
    <select id="selectGradeById" parameterType="map" resultType="map">
       SELECT
            grade_id gradeId,
            grade_name gradeName,
            grade_type gradeType,
            graduate_status graduateStatus,
            grade_start_date startDate,
            grade_end_date endDate
        FROM t_grade
        WHERE grade_id = #{gradeId}
    </select>

    <select id="selectAuditCount" parameterType="map" resultType="int">
        SELECT count(1)
        FROM t_audit
        WHERE grade_id = #{gradeId}
        AND course_id = #{courseId}
        AND type = #{auditType}
    </select>

    <select id="selectUnSubmitAuditExamCount" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM t_audit
        WHERE type = 2
        AND status = 1
        AND course_id = #{courseId}
    </select>

    <!-- 根据课程查询上报的考试 -->
    <select id="selectAuditExamByCourseId" parameterType="map" resultType="map">
        SELECT
          audit_exam_id auditExamId,
          exam_id examId,
          course_id courseId,
          course_name courseName
        FROM t_audit_exam
        WHERE course_id = #{courseId}
    </select>

    <!-- 生成审批 -->
    <insert id="insertAudit" parameterType="map" useGeneratedKeys="true" keyColumn="audit_id" keyProperty="auditId">
      INSERT INTO t_audit(
        audit_name,
        submitter_id,
        submitter_name,
        submit_time,
        course_id,
        course_name,
        grade_id,
        type,
        status,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
      ) VALUES (
        #{auditName},
        #{userId},
        #{userName},
        now(),
        #{courseId},
        #{courseName},
        #{gradeId},
        #{auditType},
        #{auditStatus},
        #{userId},
        #{userName},
        now(),
        #{userId},
        #{userName},
        now()
      )
    </insert>

    <!-- 生成审批成绩关联表 -->
    <insert id="insertAuditExam" parameterType="map" useGeneratedKeys="true" keyColumn="audit_exam_id" keyProperty="auditExamId">
        INSERT INTO t_audit_exam(
          audit_id,
          exam_id,
          course_id,
          course_name,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{auditId},
          #{examId},
          #{courseId},
          #{courseName},
          #{userId},
          #{userName},
          now(),
          #{userId},
          #{userName},
          now()
        )
    </insert>

    <!-- 查询当前年级当前课程已经上报的学生 -->
    <select id="selectAuditExamItemByGradeAndCourse" parameterType="map" resultType="map">
        SELECT
            taei.audit_exam_item_id auditExamItemId,
            taei.student_id studentId,
            taei.student_num studentNum,
            taei.student_name studentName,
            taei.class_id classId,
            taei.class_name className
        FROM
            t_audit_exam_item taei
        INNER JOIN t_audit_exam tae ON taei.audit_exam_id = tae.audit_exam_id
        INNER JOIN t_audit ta ON tae.audit_id = ta.audit_id
        WHERE ta.grade_id = #{gradeId}
            AND ta.course_id = #{courseId}
            AND ta.type = 2
            AND taei.student_status = 0
        GROUP BY taei.student_id
    </select>

    <!-- 插入老师课程考试明细 -->
    <insert id="insertAuditExamItem" parameterType="map">
        INSERT INTO t_audit_exam_item(
            student_id,
            student_num,
            student_name,
            class_id,
            class_name,
            audit_exam_id,
            creator_id,
            creator_name,
            create_date_time,
            modifier_id,
            modifier_name,
            modify_date_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
             (
                #{item.studentId},
                #{item.studentNum},
                #{item.studentName},
                #{item.classId},
                #{item.className},
                #{auditExamId},
                #{userId},
                #{userName},
                now(),
                #{userId},
                #{userName},
                now()
            )
        </foreach>
    </insert>

    <!-- 生成课程自评 -->
    <insert id="insertAuditCourse" parameterType="map">
        INSERT INTO t_audit_course (
          audit_id,
          course_id,
          course_name,
          creator_id,
          creator_name,
          create_date_time,
          modifier_id,
          modifier_name,
          modify_date_time
        ) VALUES (
          #{auditId},
          #{courseId},
          #{courseName},
          #{userId},
          #{userName},
          now(),
          #{userId},
          #{userName},
          now()
        )
    </insert>

</mapper>