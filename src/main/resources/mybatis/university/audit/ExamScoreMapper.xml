<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExamScoreMapper">

    <!-- 获取班级学生用于校验导入数据是否正确 -->
    <select id="getClassStudentStr" parameterType="map" resultType="string">
        SELECT
        taei.student_num studentNum
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}

    </select>

    <!-- 更新学生课程考试综合数据表 -->
    <update id="updateStudentUsualScore" parameterType="map">
        UPDATE t_audit_exam_item
        SET
            usual_score = #{usualScore},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE audit_exam_id = #{auditExamId}
        AND student_num = #{studentNum}
    </update>

    <!-- 更新学生课程考试成绩 -->
    <update id="updateAuditExamItemScore" parameterType="map">
        UPDATE t_audit_exam_item taei, t_audit_exam tae
        SET
            taei.exam_score = #{totalScore},
            taei.result_status = #{resultStatus},
            taei.modifier_id = #{userId},
            taei.modifier_name = #{userName},
            taei.modify_date_time = #{currentTime}
        WHERE taei.audit_exam_id = tae.audit_exam_id
        AND tae.exam_id = #{examId}
        AND tae.course_id = #{courseId}
        AND taei.student_id = #{studentId}
    </update>

    <!-- 更新学生课程考试综合数据表 -->
    <update id="updateAuditExamItem" parameterType="map">
        UPDATE t_audit_exam_item
        SET
            exam_score = #{examScore},
            usual_score = #{usualScore},
            student_status = #{studentStatus},
            result_status = #{resultStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE audit_exam_item_id = #{auditExamItemId}
    </update>

    <!-- 初始化综合评分 -->
    <update id="initAuditExamItemOverall" parameterType="map">
        UPDATE t_audit_exam_item
        SET
            exam_score = #{examScore},
            usual_score = #{usualScore},
            student_status = null,
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE audit_exam_item_id = #{auditExamItemId}
    </update>

    <!-- 获取课程考试科目的考试信息 -->
    <select id="getExamSubjectByCourseId" parameterType="map" resultType="map">
        SELECT
            teb.exam_base_id examBaseId,
            tebs.exam_base_subject_id examBaseSubjectId,
            teb.exam_base_name examBaseName,
            teb.exam_type examType,
            teb.start_date startDate,
            teb.end_date endDate,
            teb.exam_base_status examBaseStatus,
            teps.exam_plan_subject_id examPlanSubjectId
        FROM t_course tc
        INNER JOIN t_course_exam_subject tces  ON tc.course_id = tces.course_id
        INNER JOIN t_exam_base_subject tebs ON  tces.exam_subject_id= tebs.exam_subject_id
        INNER JOIN t_exam_base teb ON teb.exam_base_id = tebs.exam_base_id
        INNER JOIN t_exam_plan_subject teps ON teps.exam_base_subject_id = tebs.exam_base_subject_id
        LEFT JOIN t_audit_exam tae ON tebs.exam_base_subject_id = tae.exam_base_subject_id
        LEFT JOIN t_audit ta ON ta.audit_id = tae.audit_id
        WHERE
            tc.course_id = #{courseId}
            AND teb.exam_base_status = 3
            AND (tae.audit_exam_id IS NULL OR ta.status IN (1, 3))
        ORDER BY teb.start_date DESC
    </select>

    <!-- 获取课程考试成绩 -->
    <select id="getExamScore" parameterType="map" resultType="map">
        SELECT
            taei.audit_exam_item_id auditExamItemId,
            IF( ter.result_status = 0, ter.total_score, NULL ) examScore,
            ter.result_status resultStatus
        from  t_audit_exam_item taei
            LEFT JOIN t_exam_result ter ON ter.student_id = taei.student_id
        WHERE ter.exam_base_subject_id = #{examBaseSubjectId}
              AND taei.audit_exam_id = #{auditExamId}
    </select>

    <select id="getAudit" parameterType="map" resultType="map">
        SELECT
            ta.audit_id auditId,
            ta.audit_name auditName,
            ta.course_id courseId,
            ta.course_name courseName
        FROM t_audit ta
        INNER JOIN t_audit_exam tae ON ta.audit_id = tae.audit_id
        WHERE tae.audit_exam_id = #{auditExamId}
    </select>

    <select id="getExamPlanSubjectId" parameterType="map" resultType="long">
        SELECT
          teps.exam_plan_subject_id examPlanSubjectId
        FROM t_audit_exam tae
        INNER JOIN t_exam_plan_subject teps ON tae.exam_base_subject_id = teps.exam_base_subject_id
        WHERE tae.audit_exam_id = #{auditExamId}
        AND tae.exam_base_subject_id != 0
    </select>

    <select id="getExamScoreUnFinishCount" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM t_audit_exam_item
        WHERE audit_exam_id = #{auditExamId}
        AND student_status IS NULL
    </select>

    <!-- 获取课程考试成绩明细 -->
    <select id="getExamScoreDetail" parameterType="map" resultType="map">
        SELECT
        taei.audit_exam_item_id auditExamItemId,
        taei.student_id studentId,
        taei.student_name studentName,
        taei.student_num studentNum,
        taei.class_name className,
        taei.usual_score usualScore,
        taei.exam_score examScore,
        taei.overall_score overallScore,
        taei.result_status resultStatus,
        taei.student_status studentStatus,
        taei.score_level scoreLevel
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}
        <if test="search != null and search != '' ">
            <bind name="search" value="'%'+search+'%'"/>
            AND( taei.student_name LIKE #{search}
            OR taei.class_name LIKE #{search}
            OR taei.student_num LIKE #{search}
            )
        </if>
        <if test="resultStatus !=null and resultStatus != '' ">
            AND taei.result_status = #{resultStatus}
        </if>
        <if test="usualScoreStatus !=null and usualScoreStatus == 0 ">
            AND taei.usual_score is not null
        </if>
        <if test="usualScoreStatus !=null and usualScoreStatus == 1 ">
            AND taei.usual_score is null
        </if>
        <if test="studentStatus !=null and studentStatus != '' ">
            AND taei.student_status = #{studentStatus}
        </if>
        ORDER BY taei.class_id, taei.student_num
        <if test="pageSize !=null and currentIndex != null">
            LIMIT #{currentIndex, jdbcType=INTEGER},#{pageSize, jdbcType=INTEGER}
        </if>
    </select>

    <!-- 获取课程考试成绩明细数量-->
    <select id="getExamScoreDetailCount" parameterType="map" resultType="long">
        SELECT
        count(*)
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}
        <if test="search != null and search != '' ">
            <bind name="search" value="'%'+search+'%'"/>
            AND( taei.student_name LIKE #{search}
            OR taei.class_name LIKE #{search}
            OR taei.student_num LIKE #{search}
            )
        </if>
        <if test="resultStatus !=null and resultStatus != '' ">
            AND taei.result_status = #{resultStatus}
        </if>
        <if test="usualScoreStatus !=null and usualScoreStatus != '' ">
            AND taei.usual_score is not null
        </if>
        <if test="studentStatus !=null and studentStatus != '' ">
            AND taei.student_status = #{studentStatus}
        </if>
    </select>

    <!-- 获取课程考试成绩等级数量-->
    <select id="getExamScoreLevel" parameterType="map" resultType="long">
        SELECT
            count(*) countLevel
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}
            AND score_level = 'A+'
    </select>

    <!-- 获取课程考试总数量-->
    <select id="getExamCount" parameterType="map" resultType="long">
        SELECT
            count(*) count
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}
        and student_status = 0
    </select>

    <select id="getAuditExam" parameterType="map" resultType="map">
        SELECT
          exam_id examId,
          course_id courseId
        FROM t_audit_exam
        WHERE audit_exam_id = #{auditExamId}
    </select>

    <!-- 获取课程考试成绩明细 -->
    <select id="getAuditStudentTemplate" parameterType="map" resultType="map">
        SELECT
        taei.student_name studentName,
        taei.student_num studentNum,
        taei.class_name className
        FROM t_audit_exam_item taei
        WHERE taei.audit_exam_id = #{auditExamId}
    </select>

    <!-- 获取课程考试成绩和平时成绩用于计算明细 -->
    <select id="getScoreForOverall" parameterType="map" resultType="map">
        SELECT
        taei.audit_exam_item_id auditExamItemId,
        taei.usual_score usualScore,
        taei.exam_score examScore
        FROM t_audit_exam_item taei
        <where>
            <if test="auditExamId != null and auditExamId != ''">
                taei.audit_exam_id = #{auditExamId}
            </if>
            <if test="auditExamItemId != null and auditExamItemId != ''">
                AND taei.audit_exam_item_id = #{auditExamItemId}
            </if>
            AND taei.usual_score IS NOT NULL
            AND taei.result_status = 0
        </where>
    </select>

    <update id="updateExamStudent" parameterType="map">
        UPDATE t_audit_exam_item
          SET exam_score = null,
              score_level = null,
              overall_score = null,
              student_status = null,
              modifier_id = #{userId},
              modifier_name = #{userName},
              modify_date_time = #{currentTime}
        WHERE audit_exam_id = #{auditExamId}
        AND result_status = 1
        AND student_status != 1
    </update>

    <!-- 更新学生综合等级 -->
    <update id="updateStudentOverallLevel" parameterType="map">
        UPDATE t_audit_exam_item
        SET
            overall_score = #{overallScore},
            student_status = #{studentStatus},
            score_level = #{scoreLevel}
        WHERE audit_exam_item_id = #{auditExamItemId}
    </update>

    <!-- 更新学生综合等级 -->
    <update id="updateStudentStatus" parameterType="map">
        UPDATE t_audit_exam_item
        SET
            student_status = #{studentStatus},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE audit_exam_item_id = #{auditExamItemId}
    </update>

    <!-- 更新学生综合等级 -->
    <update id="updateAuditExamBaseSubjectId" parameterType="map">
        UPDATE t_audit_exam
        SET
            exam_id = #{examId},
            course_id = #{courseId},
            modifier_id = #{userId},
            modifier_name = #{userName},
            modify_date_time = #{currentTime}
        WHERE audit_exam_id = #{auditExamId}
    </update>

    <!-- 获取学生考试课程id -->
    <select id="getStudentExamBaseSubjectId" parameterType="map" resultType="map">
        SELECT
            tae.exam_id examId,
            tae.course_id courseId,
        <if test="!_parameter.containsKey('usualScore')">
            taei.usual_score usualScore,
        </if>
            taei.student_id studentId
        FROM t_audit_exam_item taei
            INNER JOIN t_audit_exam tae ON tae.audit_exam_id = taei.audit_exam_id
        WHERE taei.audit_exam_item_id = #{auditExamItemId}
    </select>

    <!-- 根据课程id 查询课程分数配置信息  -->
    <select id="getCourseConfig" parameterType="map" resultType="map">
        SELECT course_score_config_id courseScoreConfigId,
               course_id              courseId,
               usually_ratio          usuallyRatio,
               exam_ratio             examRatio,
               level_rules            levelRules
        FROM t_course_score_config
        WHERE course_id = #{courseId}
    </select>

    <!-- 获取学生是否生成综合成绩 -->
    <select id="getStudentOverallScoreStatus" parameterType="map" resultType="map">
        SELECT
            overall_score overallScore
        FROM t_audit_exam_item
        WHERE audit_exam_item_id = #{auditExamItemId}
    </select>

    <!-- 根据课程获取未上报的成绩上报 -->
    <select id="getAuditExamByCourse" parameterType="map" resultType="map">
        SELECT
          tae.audit_exam_id auditExamId,
          tae.course_id courseId
        FROM t_audit_exam tae
        INNER JOIN t_audit ta ON tae.audit_id = ta.audit_id
        WHERE tae.course_id = #{courseId}
        AND ta.status = 1
    </select>
</mapper>