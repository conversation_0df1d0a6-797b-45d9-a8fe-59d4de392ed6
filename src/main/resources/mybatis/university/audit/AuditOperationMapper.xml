<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="AuditOperationMapper">

    <!-- 插入审核操作 -->
    <insert id="insertAuditOperation" parameterType="map">
        INSERT INTO t_audit_operation(
        audit_id,
        operate_time,
        operator_id,
        operator_name,
        operation,
        remark,
        creator_id,
        creator_name,
        create_date_time,
        modifier_id,
        modifier_name,
        modify_date_time
        )VALUES (
        #{auditId},
        #{operateTime},
        #{operatorId},
        #{operatorName},
        #{operation},
        #{remark},
        #{userId},
        #{userName},
        #{currentTime},
        #{userId},
        #{userName},
        #{currentTime}
        )
    </insert>

    <!-- 删除审核操作 -->
    <delete id="deleteAuditOperation" parameterType="map">
        DELETE FROM t_audit_operation
        WHERE audit_id = #{auditId}
    </delete>

    <!-- 查询审核操作 -->
    <select id="getAuditOperation" parameterType="map" resultType="map">
        SELECT
        tao.audit_id auditId,
        tao.operate_time operateTime,
        tao.operator_id operatorId,
        tao.operator_name operatorName,
        tao.operation,
        tao.remark,
        ta.creator_id creatorId,
        ta.type
        FROM t_audit_operation tao
        INNER JOIN t_audit ta ON ta.audit_id = tao.audit_id
        WHERE ta.audit_id = #{auditId}
    </select>

</mapper>