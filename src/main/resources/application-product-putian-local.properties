spring.profiles.active=product-putian-local
server.port=8291
dongni.tomcat.apr.enabled=false


dongni.sensitive.info.secret.key=OTk3YTZmYTljYmY3MjI1NWU4OGM3YmZhOTIyMWQwMDA=
dongni.login.verify.images.enabled=true
dongni.login.verify.pwd-error.enabled=true
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=true
dongni.web.token.enabled=false
## clientType=2 \u5FAE\u4FE1 token\u6709\u6548\u671F\u554A3\u4E2A\u6708
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=true

spring.datasource.basedata.url=****************************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=dongni

spring.datasource.exam.url=***********************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=dongni

spring.datasource.tiku.url=************************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=dongni

spring.datasource.university.url=******************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=dongni

spring.datasource.third.url=*********************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=dongni

# flyway \u811A\u672C\u5347\u7EA7\u914D\u7F6E
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=0624
dongni.flyway.baselineDescription=first_init_2019-06-24

dongni.redis.host=************
dongni.redis.password=Dongni2015
dongni.redis.port=37645
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=false
dongni.redis.pool.test-on-create=false
dongni.redis.pool.test-while-idle=true

#spring.redis.basedata.host=*************
#spring.redis.basedata.password=Dongni2015
#spring.redis.basedata.port=37645
#spring.redis.basedata.database=0

#spring.redis.exam.host=*************
#spring.redis.exam.password=Dongni2015
#spring.redis.exam.port=37645
#spring.redis.exam.database=0

# \u57FA\u7840\u6570\u636E
spring.data.mongodb.basedata.host1=dalao_inner.dongni100.com
spring.data.mongodb.basedata.port1=60103
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData9102@)!(

# \u8003\u8BD5
spring.data.mongodb.exam.host1=dalao_inner.dongni100.com
spring.data.mongodb.exam.port1=60103
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam8102@)!*

## \u9898\u5E93
spring.data.mongodb.tiku.host1=dalao_inner.dongni100.com
spring.data.mongodb.tiku.port1=60103
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku7102@)!&

## \u5206\u6790\u7CFB\u7EDF
spring.data.mongodb.analysis.host1=dalao_inner.dongni100.com
spring.data.mongodb.analysis.port1=60103
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis6102@)!^)!(

## \u7B2C\u4E09\u65B9\u4E2D\u95F4\u5E93
spring.data.mongodb.third.host1=dalao_inner.dongni100.com
spring.data.mongodb.third.port1=60103
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer5102@!^&
spring.data.mongodb.third.database=third_data_transfer

##### \u6587\u4EF6\u5B58\u50A8\u670D\u52A1\u76F8\u5173\u914D\u7F6E\u4FE1\u606F  config/filestorage/common-config.properties
##### \u963F\u91CC\u4E91: oss
##### \u672C\u5730 : seaweedfs
dongni.file-storage.type=seaweedfs
##### seaweedfs  config/filestorage/seaweedfs-filer.properties
dongni.file-storage.cdn-url=http://smartedu.mydongtai.cn:8086/filer
dongni.file-storage.seaweedfs.filer-root-url=http://smartedu.mydongtai.cn:8086/filer
dongni.file-storage.seaweedfs.admin-header-key=putian
dongni.file-storage.seaweedfs.admin-header-value=41584fa353b34ffcbbe233b47815d4cf7aa116f7e7684d9894cee276742491737ae4e48ccb3f488c8aeb27cfedcbdcfc

# \u77ED\u4FE1\u670D\u52A1
dongni.sms.server=Ali

# \u83C1\u4F18\u7528\u6237\u6CE8\u518C\u524D\u7F00\uFF0C\u8BF7\u4FDD\u8BC1\u5404\u4E2A\u5B50\u7CFB\u7EDF\u552F\u4E00  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=

# dongni-analysis \u61C2\u4F60\u6570\u636E\u5206\u6790\u7CFB\u7EDF
dongniAnalysisServer=http://**************:8491


# \u8BD5\u5377\u89E3\u6790node\u5730\u5740
dongni.node.host=http://smartedu.mydongtai.cn:8086

# \u4E00\u8D77\u4F5C\u4E1A\u63A8\u9898\u5730\u5740
dongni.third.yiqi.host.entrust=http://thanos.17zuoye.com

# \u6C99\u7BB1\u73AF\u5883\u4E3B\u673A\u57DF\u540Dke
sandbox_host_url=http://127.0.0.1:8291

# \u6781\u8BFE\u4E3B\u673A\u57DF\u540D\u5730\u5740
jike_host_url=http://127.0.0.1:9999
# \u6781\u8BFE\u83B7\u53D6token\u7684key
jike_key=jkjy52b56451acb4653a

# \u6781\u8BFE\u6362\u53D6token\u7684sign
open_app_id_jike=dn5rqzkxjk1nuq5

# \u666E\u5929
dongni.third.putian.host=http://smartedu.mydongtai.cn:8901
dongni.third.putian.client-id=pt-bszh
dongni.third.putian.client-secret=bs1234
dongni.third.putian.redirect-uri=http://smartedu.mydongtai.cn:8086/auth/putian/dongtai

##### \u5F53\u524D\u670D\u52A1\u4FE1\u606F
currentServer=putian
##### \u61C2\u4F60\u57DF\u540D
dongni.server=https://www.dongni100.com

## \u61C2\u4F60\u5F00\u653E\u5E73\u53F0\u4FE1\u606F
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-localisation-putian
dongni.web.auth.client.password=7e9164f1-7bbc-46bc-bff6-fe9ac48ac11d

dongni.wechat.open.componentAppId=
dongni.wechat.open.componentSecret=
dongni.wechat.open.componentToken=
dongni.wechat.open.componentAesKey=
dongni.wechat.xmwz.appId=
dongni.wechat.xmwz.templateId=

# \u61C2\u4F60\u5FAE\u4FE1\u516C\u4F17\u53F7 WeChatUtil
dongni.wechat.official-accounts.app-id=wxf9fd691ea194890f
dongni.wechat.official-accounts.secret=8ab2ae08cae3402ac83e3ee7bf01100f
dongni.wechat.official-accounts.mch-id=
dongni.wechat.official-accounts.key=
dongni.wechat.official-accounts.redirect-domain=
dongni.wechat.official-accounts.price=
dongni.wechat.official-accounts.template-id-exam-result=
dongni.wechat.official-accounts.template-id-homework-submit=
dongni.wechat.official-accounts.template-id-roll-plan=
dongni.wechat.official-accounts.template-id-entrust-warning=

