create table if not exists t_homework_weekly_report_student_homework
(
    homework_weekly_report_student_homework_id   bigint(20)     auto_increment comment '作业周报学生作业详情表主键' primary key,
    homework_weekly_report_student_id            bigint(20)     not null comment '作业周报学生id',
    exam_id                                      bigint(20)     not null comment '考试Id',
    exam_name                                    varchar(255)   not null comment '考试名称',
    score_mode                                   tinyint(2)     not null comment '作业打分模式 1：不打分模式 0：打分模式',
    scan_time                                    datetime       not null comment '扫描时间',
    course_id                                    bigint(20)     not null comment '课程id',
    course_name                                  varchar(200)   not null comment '课程名称',
    question_count                               tinyint(4)     not null comment '试卷结构试题数',
    full_mark                                    decimal(10, 2) not null comment '试卷总分',
    result_status                                tinyint(2)     not null comment '提交状态 0为提交，1为未提交',
    student_wrong_question_count                 tinyint(4)     null comment '试卷结构错题数',
    total_score                                  decimal(10, 2) null comment '学生得分',
    score_rate                                   decimal(10, 4) null comment '学生得分率',
    class_ranking                                tinyint(4)     null comment '学生班级排名',
    class_wrong_question_count                   decimal(10, 2) null comment '班级平均错题数',
    class_average_score                          decimal(10, 2) null comment '班级平均得分',
    class_score_rate                             decimal(10, 4) null comment '班级平均得分率',
    class_total_student                          tinyint(4)     not null comment '班级总人数 (参考+缺考) 即 (提交+未提交)',
    class_participation_number                   tinyint(4)     not null comment '班级参考(提交)人数',
    class_absent_number                          tinyint(4)     not null comment '班级缺考(未提交)人数',
    class_last_ranking                           tinyint(4)     null comment '班级最后一名名次',
    creator_id                                   bigint(20)     not null comment '创建人Id',
    creator_name                                 varchar(20)    not null comment '创建人',
    create_date_time                             datetime       not null comment '创建时间',
    modifier_id                                  bigint(20)     not null comment '修改人Id',
    modifier_name                                varchar(20)    not null comment '修改人',
    modify_date_time                             datetime       not null comment '修改时间',
    unique key logic (homework_weekly_report_student_id, exam_id)
) comment '作业周报学生作业详情表';