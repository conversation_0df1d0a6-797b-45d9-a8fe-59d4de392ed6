CREATE TABLE t_study_guide_wrong_item
(
    `study_guide_wrong_item_id` BIGINT        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `student_id`                BIGINT        NOT NULL COMMENT '学生ID',
    `question_id`               VARCHAR(64)   NOT NULL COMMENT '试题ID',
    `exam_id`                   BIGINT        NOT NULL COMMENT '考试ID',
    `course_id`                 BIGINT        NOT NULL COMMENT '课程ID',
    `paper_id`                  BIGINT        NOT NULL COMMENT '试卷ID',
    `class_id`                  BIGINT        NOT NULL COMMENT '班级ID，特指学生这场考试所在的班级ID',
    `mark_question_numbers`     VARCHAR(1024) NOT NULL COMMENT '对应的阅卷结构，多个用逗号隔开',
    `creator_id`                BIGINT        NOT NULL COMMENT '创建人ID',
    `creator_name`              VARCHAR(20)   NOT NULL COMMENT '创建人',
    `create_date_time`          DATETIME      NOT NULL COMMENT '创建时间',
    `modifier_id`               BIGINT        NOT NULL COMMENT '修改人ID',
    `modifier_name`             VARCHAR(20)   NOT NULL COMMENT '修改人',
    `modify_date_time`          DATETIME      NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`study_guide_wrong_item_id`),
    KEY `idx_examId_classId` (`exam_id`, `class_id`),
    KEY `idx_studentId_courseId_examId` (`student_id`, `course_id`, `exam_id`),
    KEY `idx_studentId_questionId` (`student_id`, `question_id`)
) COMMENT = '学生教辅作业错题表';

CREATE TABLE t_study_guide_wrong_tag_item
(
    `study_guide_wrong_tag_item_id` BIGINT      NOT NULL COMMENT '主键',
    `study_guide_wrong_item_id`     BIGINT      NOT NULL COMMENT '错题ID',
    `wrong_tag_id`                  BIGINT      NOT NULL COMMENT '标签ID',
    `tag_name`                      VARCHAR(64) NOT NULL COMMENT '标签名称',
    `creator_id`                    BIGINT      NOT NULL COMMENT '创建人ID',
    `creator_name`                  VARCHAR(20) NOT NULL COMMENT '创建人',
    `create_date_time`              DATETIME    NOT NULL COMMENT '创建时间',
    `modifier_id`                   BIGINT      NOT NULL COMMENT '修改人ID',
    `modifier_name`                 VARCHAR(20) NOT NULL COMMENT '修改人',
    `modify_date_time`              DATETIME    NOT NULL COMMENT '修改时间',
    PRIMARY KEY (`study_guide_wrong_tag_item_id`),
    KEY `idx_homeworkWrongItemId` (`study_guide_wrong_item_id`)
) COMMENT = '学生教辅作业错题错因表';