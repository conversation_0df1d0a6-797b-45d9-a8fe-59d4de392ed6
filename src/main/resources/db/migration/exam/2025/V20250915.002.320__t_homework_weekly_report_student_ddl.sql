create table if not exists t_homework_weekly_report_student
(
    homework_weekly_report_student_id   bigint(20)  auto_increment comment '作业周报学生表主键' primary key,
    homework_weekly_report_id           bigint(20)  not null comment '作业周报id',
    student_id                          bigint(20)  not null comment '学生id',
    student_name                        varchar(30) not null comment '学生名称',
    creator_id                          bigint(20)  not null comment '创建人Id',
    creator_name                        varchar(20) not null comment '创建人',
    create_date_time                    datetime    not null comment '创建时间',
    modifier_id                         bigint(20)  not null comment '修改人Id',
    modifier_name                       varchar(20) not null comment '修改人',
    modify_date_time                    datetime    not null comment '修改时间',
    unique key logic (student_id, homework_weekly_report_id)
) comment '作业周报';