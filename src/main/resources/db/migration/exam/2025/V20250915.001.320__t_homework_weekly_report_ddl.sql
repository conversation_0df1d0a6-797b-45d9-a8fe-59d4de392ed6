create table if not exists t_homework_weekly_report
(
    homework_weekly_report_id   bigint(20)  auto_increment comment '作业周报表主键' primary key,
    epoch_week                  bigint(20)  not null comment '第多少周，从1970-01-01算第几周',
    start_date                  datetime    not null comment '周报开始时间',
    end_date                    datetime    not null comment '周报结束时间',
    creator_id                  bigint(20)  not null comment '创建人Id',
    creator_name                varchar(20) not null comment '创建人',
    create_date_time            datetime    not null comment '创建时间',
    modifier_id                 bigint(20)  not null comment '修改人Id',
    modifier_name               varchar(20) not null comment '修改人',
    modify_date_time            datetime    not null comment '修改时间',
    unique key logic (epoch_week)
) comment '作业周报表';