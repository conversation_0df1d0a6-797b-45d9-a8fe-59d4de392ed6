<?xml version="1.0" encoding="UTF-8" ?>
<!--

 该文件为开发环境的日志配置文件，
 日志文件存放在运行服务器的 /home/<USER>/logs/recently/{APP_NAME}/下

-->
<!-- Logback configuration. See http://logback.qos.ch/manual/index.html -->
<configuration scan="true" scanPeriod="100 seconds">

    <include resource="org/springframework/boot/logging/logback/base.xml" />
    <property name="APP_NAME" value="fat-service"/>                <!--应用名称，会应用在日志文件名上-->
    <property name="LOG_PATH" value="/home/<USER>/logs/test"/>   <!--日志文件主路径-->

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_PATH}/${APP_NAME}/${APP_NAME}-info.log</File>  <!--info级别的日志文件全路径-->

        <!--日志备份 不限制历史保存时间，自行手动维护-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}/backup/%d{yyyyMM, aux}/${APP_NAME}-info-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>180</maxHistory>
        </rollingPolicy>

        <layout class="ch.qos.logback.classic.PatternLayout">            <!--日志输出格式-->
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </layout>

    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">   <!--只记录ERROR级别的日志-->
            <level>ERROR</level>
        </filter>
        <File>${LOG_PATH}/${APP_NAME}/${APP_NAME}-error.log</File>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}/backup/%d{yyyyMM, aux}/${APP_NAME}-error-%d{yyyyMMdd}-%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>180</maxHistory>
        </rollingPolicy>

        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>${FILE_LOG_PATTERN}</Pattern>
        </layout>

    </appender>


    <!--调度的日志文件-->
    <appender name="TASK_SCHEDULE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%tid] [%logger{36}:%L] -%msg%n</Pattern>
            </layout>
        </encoder>
        <File>${LOG_PATH}/${APP_NAME}/task-info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${APP_NAME}/task%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <maxHistory>20</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 系统默认日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>

    <!-- 懂你日志记录 -->
    <logger name="mapper.TaskMapper" level="INFO" addtivity="false"/>
    <logger name="mapper.OperationLogMapper" level="INFO" addtivity="false"/>
    <logger name="com.dongni" level="INFO" addtivity="false"/>
    <logger name="mapper" level="DEBUG" addtivity="false"/>
    <logger name="com.dongni.open.mq.service" level="DEBUG" addtivity="false"/>

    <logger additivity="false" level="INFO" name="com.dongni.exam.newcard.service.impl.NewRecognitionTask">
        <appender-ref ref="TASK_SCHEDULE_FILE"/>
    </logger>


</configuration>