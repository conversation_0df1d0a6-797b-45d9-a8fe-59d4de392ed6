# å½åç¯å¢åç§° å¼å¤´ä¸ºproduct-çä¸ºçäº§ç¯å¢ï¼ç»å°¾ä¸º-localçä¸ä¼å¯å¨å®æ¶ä»»å¡
spring.profiles.active=product-tencent
# å¯å¨ç«¯å£
server.port=8291


spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# tomcat.apræ¯å¦å¯å¨
dongni.tomcat.apr.enabled=false
# ææºå·èº«ä»½è¯è±æç¨çå å¯key SensitiveInfoUtilTest#genSensitiveSecretKey
dongni.sensitive.info.secret.key=kBwPdo9Ar3rCuwqL6hupIqxfD4b97EGswYMf1hZhuDw=
# ç»å½é¡µé¢çæ»ååè½ å¯ç éè¯¯ä¸å®æ¬¡æ°åéè¦éªè¯æ»å
dongni.login.verify.images.enabled=false
# ç»å½é¡µé¢çå¯ç éè¯¯åè½ å¯ç éè¯¯ä¸å®æ¬¡æ°åéå®è´¦å·
dongni.login.verify.pwd-error.enabled=false
dongni.web.interceptor.enabled=true
dongni.web.interceptor.header.user-info-check=false
dongni.web.exception.global.enabled=true
dongni.web.exception.stack.enabled=false
dongni.web.token.enabled=true
## clientType=2 å¾®ä¿¡ tokenæææå3ä¸ªæ
dongni.web.token.expire-seconds.2=7776000
dongni.web.device.security.enabled=false
dongni.operation.log.enabled=false

##### å½åæå¡ä¿¡æ¯
currentServer=tencent
##### æä½ åå
dongni.server=https://www.dongni100.com
##### æä½ apiåç¼
dongni.server-api.prefix=/api

## åºç¡æ°æ®
spring.datasource.basedata.url=***********************************************************************************************
spring.datasource.basedata.username=dn
spring.datasource.basedata.password=Dongni@015

## èè¯
spring.datasource.exam.url=******************************************************************************************
spring.datasource.exam.username=dn
spring.datasource.exam.password=Dongni@015

## é¢åº
spring.datasource.tiku.url=******************************************************************************************
spring.datasource.tiku.username=dn
spring.datasource.tiku.password=Dongni@015

## å¤§å­¦ç«¯
spring.datasource.university.url=************************************************************************************************
spring.datasource.university.username=dn
spring.datasource.university.password=Dongni@015

## ç¬¬ä¸æ¹ä¸­é´åº
spring.datasource.third.url=*********************************************************************************************************
spring.datasource.third.username=dn
spring.datasource.third.password=Dongni@015

# flyway èæ¬åçº§éç½®
dongni.flyway.enabled=true
dongni.flyway.user=dongni_admin
dongni.flyway.password=Dongni!@#123
dongni.flyway.baselineVersion=20210629
dongni.flyway.baselineDescription=20210629 repository start

dongni.redis.host=dnredis
dongni.redis.password=Dongni2015
dongni.redis.port=6379
dongni.redis.database=0
dongni.redis.pool.test-on-borrow=true
dongni.redis.pool.test-on-return=true
dongni.redis.pool.test-on-create=true
dongni.redis.pool.test-while-idle=true
dongni.redis.pool.max-total=1023

# è¯å«è°åº¦rediséç½®.
redis2_host=dnredis2
redis2_port=6379
redis2_database=0
redis2_password=Dongni2015



# åºç¡æ°æ®
spring.data.mongodb.basedata.host=dnmongo
spring.data.mongodb.basedata.port=27017
spring.data.mongodb.basedata.database=base_data
spring.data.mongodb.basedata.username=base_data
spring.data.mongodb.basedata.password=BaseData2015

# èè¯
spring.data.mongodb.exam.host=dnmongo
spring.data.mongodb.exam.port=27017
spring.data.mongodb.exam.database=exam
spring.data.mongodb.exam.username=exam
spring.data.mongodb.exam.password=Exam2015

## é¢åº
spring.data.mongodb.tiku.host=dnmongo
spring.data.mongodb.tiku.port=27017
spring.data.mongodb.tiku.database=tiku
spring.data.mongodb.tiku.username=tiku
spring.data.mongodb.tiku.password=Tiku2015

## åæç³»ç»
spring.data.mongodb.analysis.host=dnmongo
spring.data.mongodb.analysis.port=27017
spring.data.mongodb.analysis.database=analysis
spring.data.mongodb.analysis.username=analysis
spring.data.mongodb.analysis.password=Analysis2015

## ä¸­é´åº
spring.data.mongodb.third.host=dnmongo
spring.data.mongodb.third.port=27017
spring.data.mongodb.third.username=third_data_transfer
spring.data.mongodb.third.database=third_data_transfer
spring.data.mongodb.third.password=ThirdDataTransfer2015

##### æä»¶å­å¨æå¡ç¸å³éç½®ä¿¡æ¯
dongni.file-storage.type=cos
dongni.file-storage.cdn-url=https://dn-product-1321973628.cos.ap-nanjing.myqcloud.com
dongni.file-storage.cos.secret-id=AKIDMpHdzRASdrMmHh5TmzCJSgqz2487zrhW
dongni.file-storage.cos.secret-key=ukhgDyneedh8X0ibbbKxR3rgTC2pVHCD
dongni.file-storage.cos.region=ap-nanjing
dongni.file-storage.cos.bucket-name=dn-product-1321973628

# ç­ä¿¡æå¡
dongni.sms.server=Ali
# ç­ä¿¡ç­¾åï¼åææ§å­¦
dongni.sms.sign-name=\u51C6\u6559\u6167\u5B66

# èä¼ç¨æ·æ³¨ååç¼ï¼è¯·ä¿è¯åä¸ªå­ç³»ç»å¯ä¸  {jyeoo.user.prefix}{userId}
jyeoo.user.prefix=DN_TX_

# dongni-analysis æä½ æ°æ®åæç³»ç»å°å
# é¨åæåµfat-serviceéè¦è°ç¨åæçæå¡
dongniAnalysisServer=https://v2024.dongni100.com/api

# nodeå°å
dongni.node.host=https://v2024.dongni100.com

## æä½ å¼æ¾å¹³å°ä¿¡æ¯
dongni.web.auth.server-host=https://open.dongni100.com
dongni.web.auth.uri.check-token=/api/auth/oauth/check_token
dongni.web.auth.uri.access-token=/api/auth/oauth/token
dongni.web.auth.client.id=dongni-product-tencent
dongni.web.auth.client.password=bfd868b3-73cd-4c46-92f3-2d87f5b3807e

# å¥½ä¸ä¸mongo-analysisæ°æ®åº
dongni.third.haozhuanye.mongodb.analysis.database=analysis
hzy.mongo.inputuri.placeholder={dongni.mongo.uri}

# wordè½¬pdf è¯´æè§application.propertiesæWordToPdfService
dongni.word-to-pdf.env-name=tencent
dongni.word-to-pdf.main-host=https://www.dongni100.com/api
dongni.word-to-pdf.render-url=http://**************:8080/wordToPdf

# ------------------------------------------- åå¤åºå¼
# é¢åºéç¥å¼å¯ éè¦æ°æ®è¡¨/å­å¸æ¯æ è¯¦è§com.dongni.tiku.third.common.package-info.java
# è¯¥åè½å¾å¤å¹´æ²¡ç¨äºï¼åæ¬è®¾è®¡ä¸ºæ¥æ¶ä¸èµ·ä½ä¸ç½çæ¨ééç¥
dongni.tiku.notification.enabled=true

# ------------------------------------------- çä¼¼åºå¼
# æ²ç®±ç¯å¢ä¸»æºåå
sandbox_host_url=http://127.0.0.1:8291
# æè¯¾ä¸»æºååå°å
jike_host_url=http://127.0.0.1:9999
# æè¯¾è·åtokençkey
jike_key=jkjy52b56451acb4653a
# æè¯¾æ¢åtokençsign
open_app_id_jike=dn5rqzkxjk1nuq5

# \u61C2\u4F60\u5FAE\u4FE1\u516C\u4F17\u53F7 WeChatUtil
dongni.wechat.official-accounts.app-id=wxeacc64fe89da732d
dongni.wechat.official-accounts.secret=6b28e15ade44fa2de66b99bcf222caea
dongni.wechat.official-accounts.mch-id=**********
dongni.wechat.official-accounts.key=a6f7deac760b40959a6a9c106598g9mw
dongni.wechat.official-accounts.redirect-domain=https://m2023.dongni100.com
dongni.wechat.official-accounts.price=product
dongni.wechat.official-accounts.template-id-exam-result=
dongni.wechat.official-accounts.template-id-homework-submit=
dongni.wechat.official-accounts.template-id-roll-plan=
dongni.wechat.official-accounts.template-id-entrust-warning=

dongni.wechat.teacher-official-accounts.app-id=wxeacc64fe89da732d
dongni.wechat.teacher-official-accounts.secret=6b28e15ade44fa2de66b99bcf222caea
dongni.wechat.teacher-official-accounts.redirect-domain=https://m2023.dongni100.com
dongni.wechat.teacher-official-accounts.template-id-teacher-homework-finish=
