package com.dongni.tiku.own.service;

import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.basedata.school.course.service.impl.CourseServiceImpl;
import com.dongni.common.utils.MongoUtil;
import com.dongni.common.utils.TreeCodeUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.service.TikuCourseRelationService;
import com.dongni.tiku.common.util.KnowledgeUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.common.util.TreeUtil2;
import com.dongni.tiku.common.util.question.QuestionUtil;
import com.dongni.tiku.manager.impl.KnowledgeManager;
import com.dongni.tiku.third.yiqi.api.YiqiManualApi;
import com.dongni.tiku.third.yiqi.service.YiqiConfigService;
import com.mongodb.client.model.ReplaceOneModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.dongni.common.mongo.Order.Field.asc;
import static com.dongni.common.mongo.Order.Field.desc;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

/**
 * 知识点
 *    知识点的操作最好都是经过该类进行操作，包括查询，记得进行过滤！！！！
 * <AUTHOR>
 * @date 2019/01/17 20:43
 */
@Service
public class OwnKnowledgeService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(OwnKnowledgeService.class);
    
    @Autowired
    private KnowledgeManager knowledgeManager;
    @Autowired
    private OwnPaperService ownPaperService;
    @Autowired
    private OwnQuestionService ownQuestionService;
    @Autowired
    private CommonCourseService commonCourseService;
    @Autowired
    private TikuCourseRelationService tikuCourseRelationService;
    @Autowired
    private YiqiConfigService yiqiConfigService;
    @Autowired
    private YiqiManualApi yiqiManualApi;
    @Autowired
    private TikuBaseDataVersionService tikuBaseDataVersionService;
    @Autowired
    private KnowledgeMasterServiceImpl knowledgeMasterService;
    
    // -------------------------------------------------------------------- 各种查询
    
    /**
     * 获取所有的知识点 不过滤
     * @return 所有的知识点
     */
    public List<Document> getAllKnowledgeDocumentSource() {
        return knowledgeManager.getList();
    }
    
    /**
     * 获取所有的知识点 不过滤
     * @return 所有的知识点
     */
    public List<Map<String, Object>> getAllNotDeletedKnowledgeSource() {
        return knowledgeManager.getListMap(eq("deleted", false));
    }
    
    /**
     * 查询知识点树
     * @param params courseId
     * @return 知识点树
     */
    public List<Map<String, Object>> getKnowledgeTree(Map<String, Object> params, String... fields) {
        Verify.of(params).isValidId("courseId").verify();
        return TreeUtil.list2Tree(getKnowledgeByCourseId(params, fields), "knowledgeId", "parentId", "child");
    }
    
    /**
     * 初始化所有知识点树索引
     */
    public void knowledgeTreeAddIndexAll() {
        Stream.of(2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20).forEach(courseId ->
            knowledgeTreeAddIndex(MapUtil.of("courseId",courseId))
        );
    }
    
    /**
     * 知识点树构造父子节点索引
     * @param params courseId
     */
    public void knowledgeTreeAddIndex(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").verify();
        
        // 获取课程的所有知识点
        List<Map<String, Object>> knowledgeList = getKnowledgeByCourseId(params);
        Map<Object, List<Map<String, Object>>> knowledgeMap = knowledgeList.stream().collect(groupingBy(a -> a.get("knowledgeId")));
        
        // 获取课程的知识点树
        List<Map<String, Object>> knowledgeTreeList = TreeUtil.list2Tree(knowledgeList, "knowledgeId", "parentId", "child");
        
        // 构造初始索引
        StringBuilder treeCode = new StringBuilder();
        treeCode.append(params.get("courseId")).append(".").append("#");
        
        // 递归遍历构造索引
        addTreeCode(knowledgeTreeList, treeCode);
        
        // 含有新索引的课程知识点
        List<Map<String, Object>> newKnowledgeList = TreeUtil2.treeMenuList(knowledgeTreeList, "child");
        
        // 对比原有的知识点，更新变动的索引
        List<Map<String, Object>> updateTreeCodeKnowledge = new ArrayList<>();
        for (Map<String, Object> newKnowledgeMap : newKnowledgeList) {
            // 得到原有的索引
            String oldTreeCode = Optional
                    .ofNullable(knowledgeMap.get(newKnowledgeMap.get("knowledgeId")).get(0).get(treeCode.toString()))
                    .orElse("0").toString();
            
            // 比较索引是否变动，是否更新索引
            if (!ObjectUtil.isValueEquals(oldTreeCode, newKnowledgeMap.get("TreeCode"))) {
                updateTreeCodeKnowledge.add(newKnowledgeMap);
            }
        }
        
        // 更新索引
        updateTreeCodeKnowledge.forEach(a -> knowledgeManager.updateOne(
                eq("_id", new ObjectId(a.get("knowledgeId").toString())),
                set("treeCode", a.get("treeCode")))
        );
    }
    
    /**
     * 新增课程知识点
     * @param params 见校验 [parentId]
     */
    public void addCourseKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isIntegerPositive("stage")
                .isValidId("courseId")
                .isNotBlank("courseName")
                .isNotBlank("knowledgeName")
                .isIntegerNatural("deep")
                .isNotBlank("leaf")
                .verify();
        
        String parentId = MapUtil.getTrim(params, "parentId");
        ObjectId parentMongoId = MongoUtil.getMongoId(parentId);
        Bson queryParent = eq("_id", parentMongoId);
        Document parentKnowledge = knowledgeManager.getFirst(queryParent);
        if (MapUtils.isEmpty(parentKnowledge)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点的父知识点不存在");
        }
        
        long courseId = MapUtil.getLong(params, "courseId");
        long parentCourseId = MapUtil.getLong(parentKnowledge, "courseId");
        if (parentCourseId != courseId) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "知识点的课程id与父知识点的课程id不一致");
        }
        
        int deep = MapUtil.getInt(params, "deep");
        int parentDeep = MapUtil.getInt(parentKnowledge, "deep");
        if (parentDeep != deep - 1) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "知识点的层级与父知识点的层级无法对应");
        }
    
        boolean leaf = MapUtil.getBoolean(params, "leaf");
        boolean parentLeaf = MapUtil.getBoolean(parentKnowledge, "leaf");
        
        Document maxSortKnowledge = knowledgeManager.where(and(
                        eq("courseId", courseId),
                        eq("deep", deep)
                ))
                .select("sort")
                .notSelectId()
                .orderBy(desc("sort"))
                .first();
        int sort = MapUtils.isEmpty(maxSortKnowledge) ? 1 : MapUtil.getInt(maxSortKnowledge, "sort", 0) + 1;
        
        long userId = MapUtil.getLong(params, "userId");
        String userName = MapUtil.getTrim(params, "userName");
        String currentDateTime = DateUtil.getCurrentDateTime();
        Document doc = new Document()
                .append("stage", MapUtil.getInt(params, "stage"))
                .append("courseId", courseId)
                .append("courseName", MapUtil.getTrim(params, "courseName"))
                .append("knowledgeName", MapUtil.getTrim(params, "knowledgeName"))
                .append("parentId", MapUtil.getTrimNullable(params, "parentId"))
                .append("deep", deep)
                .append("sort", sort)
                .append("leaf", leaf)
                // .append("treeCode", ?)
                .append("deleted", false)
                .append("creatorId", userId)
                .append("creatorName", userName)
                .append("createDateTime", currentDateTime)
                .append("modifierId", userId)
                .append("modifierName", userName)
                .append("modifyDateTime", currentDateTime)
                ;
        knowledgeManager.insertOne(doc);
        // 新增的节点为叶子节点 且父节点为叶子节点 则需要将父节点更改为非叶子节点
        if (leaf && parentLeaf) {
            knowledgeManager.updateOne(queryParent, set("leaf", false));
        }
    }
    
    /**
     * 编辑课程知识点
     * @param params 见校验
     */
    public void updateCourseKnowledge(Map<String, Object> params) {
        Verify.of(params).isNotBlank("_id").isNotBlank("knowledgeName").verify();
        ObjectId objectId = MongoUtil.getMongoId(MapUtil.getTrim(params, "_id"));
        Bson query = eq("_id", objectId);
        Bson setBson = set("knowledgeName", MapUtil.getTrim(params, "knowledgeName"));
        knowledgeManager.updateOne(query, setBson);
    }
    
    /**
     * 删除课程知识点
     * @param params courseId _id
     */
    public void deleteCourseKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isNotBlank("_id")
                .verify();
        String knowledgeId = MapUtil.getTrim(params, "_id");
        ObjectId objectId = MongoUtil.getMongoId(knowledgeId);
        Bson query = eq("_id", objectId);
        Document deleteMap = knowledgeManager.getFirst(query);
        if (MapUtils.isEmpty(deleteMap)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点不存在");
        }
        
        // 删除该节点及其所有子节点
        List<Map<String, Object>> allKnowledgeTreeList = TreeUtil.list2Tree(getKnowledgeByCourseId(params), "knowledgeId", "parentId", "child");
        Map<String, Object> deleteKnowledgeTree = TreeUtil.getTreeChild(allKnowledgeTreeList.get(0), "knowledgeId", knowledgeId);
        List<Map<String, Object>> deleteKnowledgeTreeList = Stream.of(deleteKnowledgeTree).collect(Collectors.toList());
        List<Map<String, Object>> deleteChildren = TreeUtil2.treeMenuList(deleteKnowledgeTreeList, "child");
        List<ObjectId> deleteIdList = deleteChildren.stream()
                .map(m -> new ObjectId(MapUtil.getTrim(m, "_id")))
                .collect(Collectors.toList());
        knowledgeManager.deleteMany(in("_id", deleteIdList));
        
        int deep = deleteMap.getInteger("deep");
        if (deep > 1) {
            // 该节点删除后，如果它没有其他兄弟了，则将父节点更改为叶子节点
            String parentKnowledgeId = deleteMap.get("parentId").toString();
            long brotherCount = knowledgeManager.count(eq("parentId", parentKnowledgeId));
            if (brotherCount == 0) {
                ObjectId parentObjectId = MongoUtil.getMongoId(parentKnowledgeId);
                knowledgeManager.updateOne(eq("_id", parentObjectId),set("leaf", true));
            }
        }
    }
    
    /**
     * 获取默认知识点
     * @param params courseId
     */
    public Map<String, Object> getDefaultKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        Bson query = and(
                eq("courseId", MapUtil.getLong(params, "courseId")),
                eq("leaf", true),
                eq("deleted", false));
        Map<String, Object> knowledge = knowledgeManager.getFirst(query);
        if (MapUtils.isEmpty(knowledge)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "课程的默认知识点不存在");
        }
        return knowledge;
    }
    
    /**
     * 获取知识点 树形中的单层结构
     * @param params courseId [parentId]
     * @return [{}, {}, ...]
     *   _id, knowledgeId(等同于_id), parentId, courseId, courseName, stage, knowledgeName, knowledgeType treeCode
     *   leaf(是否为叶子节点)   sort  deep
     *   subject(17作业网的subject) weight deleted
     */
    public List<Map<String, Object>> getKnowledgeLayer(Map<String, Object> params, String... fields) {
        Verify.of(params).isValidId("courseId").verify();
        return getKnowledgeByParent(params, fields);
    }

    /**
     * 根据父节点获取知识点
     * @param params courseId [parentId]
     * @return
     */
    public List<Map<String, Object>> getKnowledgeByParent(Map<String, Object> params, String... fields) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        Bson query = and(
                eq("courseId", MapUtil.getLong(params, "courseId")),
                eq("parentId", MapUtil.getTrimNullable(params, "parentId")),
                eq("deleted", false)
        );
        List<Map<String, Object>> knowledgeList = knowledgeManager.where(query)
                .select(fields)
                .orderBy(asc("sort"))
                .listMap();
        return KnowledgeUtil.filterObsoleteKnowledgeAndSetId(knowledgeList);
    }
    
    /**
     * 根据treeCode获取子节点的knowledgeList
     * @param params treeCode
     * @return treeCode的子节点的knowledgeList 字段见KnowledgeUtil.transferKnowledgeYiqiToDongni
     */
    public List<Document> getChildKnowledgeList(Map<String,Object> params) {
        Verify.of(params).isNotBlank("treeCode").verify();
        String treeCode = MapUtil.getTrim(params, "treeCode");
        return getChildKnowledgeList(treeCode);
    }
    
    /**
     * 根据treeCode获取子节点的knowledgeList
     * @param treeCode treeCode
     * @return treeCode的子节点的knowledgeList 字段见KnowledgeUtil.transferKnowledgeYiqiToDongni
     */
    public List<Document> getChildKnowledgeList(String treeCode) {
        treeCode = MapUtil.getTrim(treeCode);
        if (!treeCode.endsWith(".")) {
            treeCode = treeCode + ".";
        }
        String pattern = "^" + treeCode.replace(TreeCodeUtil.DELIMITER, "\\" + TreeCodeUtil.DELIMITER);
        Bson query = and(eq("deleted", false), regex("treeCode", pattern));
        List<Document> knowledgeList = knowledgeManager.getList(query);
        return KnowledgeUtil.filterObsoleteKnowledgeAndSetId(knowledgeList);
    }
    
    /**
     * 获取知识点 可能为空
     * @param knowledgeObjectId 知识点id
     * @return 知识点 nullable
     */
    public Document getKnowledgeNullable(ObjectId knowledgeObjectId) {
        if (knowledgeObjectId == null) {
            return null;
        }
        Bson query = and(eq("deleted", false), eq("_id", knowledgeObjectId));
        return knowledgeManager.getFirst(query);
    }
    
    /**
     * 查询知识点
     * @param params courseId search
     * @return
     */
    public List<Map<String, Object>> getKnowledgeBySearch(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isNotBlank("search")
                .verify();
        Bson query = and(
                eq("courseId", MapUtil.getLong(params, "courseId")),
                eq("deleted", false),
                regex("knowledgeName", MapUtil.getTrim(params, "search"))
        );
        List<Map<String, Object>> knowledgeList = knowledgeManager.getListMap(query);
        return KnowledgeUtil.filterObsoleteKnowledgeAndSetId(knowledgeList);
    }
    
    /**
     * 获取课程的所有知识点 删除的不会查出来
     * @param params courseId
     * @return 课程的知识点 删除的不会查出来
     */
    public List<Map<String, Object>> getKnowledgeByCourseId(Map<String, Object> params, String... fields) {
        Verify.of(params).isValidId("courseId").verify();
        return getKnowledgeByCourseId(MapUtil.getLong(params, "courseId"), fields);
    }
    
    /**
     * 获取课程的所有知识点 删除的不会查出来
     * @param courseId courseId
     * @return 课程的知识点 删除的不会查出来
     */
    public List<Map<String, Object>> getKnowledgeByCourseId(long courseId, String... fields) {
        Bson query = and(
                eq("courseId", courseId),
                eq("deleted", false)
        );
        List<Map<String, Object>> knowledgeList = knowledgeManager.where(query)
                .select(fields)
                .orderBy(asc("sort"))
                .listMap();
        return KnowledgeUtil.filterObsoleteKnowledgeAndSetId(knowledgeList);
    }
    
    /**
     * 获取课程的所有知识点 不过滤
     * @param params courseId
     * @return
     */
    private List<Map<String, Object>> getKnowledgeByCourseIdSource(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        Bson query = eq("courseId", courseId);
        List<Map<String, Object>> knowledgeList = knowledgeManager.where(query).listMap();
        KnowledgeUtil.setKnowledgeId(knowledgeList);
        return knowledgeList;
    }
    
    /**
     * 查询课程知识点树(新增课程,如果不存在默认初始化三级结构知识点树)
     * @param params courseId courseName
     * @return _id knowledgeId knowledgeName leaf treeCode child
     */
    public List<Map<String, Object>> getCourseKnowledgeTree(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isNotBlank("courseName")
                .verify();
        List<Map<String, Object>> knowledge = getKnowledgeByCourseId(params);
        // 如果没有 则生成默认的三级结构
        if (CollectionUtils.isEmpty(knowledge)) {
            knowledge = initKnowledge(params);
        }
        return TreeUtil.list2Tree(knowledge, "knowledgeId", "parentId", "child");
    }
    
    /**
     * 获取课程的所有叶子节点知识点idList
     * @param courseId 课程id
     * @return 所有叶子节点的id
     */
    public List<String> getCourseLeafKnowledgeIds(long courseId) {
        Bson query = and(
                eq("courseId", courseId),
                eq("deleted", false),
                eq("leaf", true)
        );
        List<Document> knowledgeList = knowledgeManager.where(query).selectId().list();
        knowledgeList = KnowledgeUtil.filterObsoleteKnowledge(knowledgeList);
        return knowledgeList.stream()
                .map(knowledge -> MapUtil.getTrim(knowledge, "_id"))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取知识点信息
     *
     * @param params - courseId    课程id
     *               - _id 知识点id
     * @return 知识点信息
     */
    public Map<String, Object> getKnowledgeById(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("_id")
                .isValidId("courseId")
                .verify();
        Bson query = and(
                eq("courseId", MapUtil.getLong(params, "courseId")),
                eq("_id", MongoUtil.getMongoId(MapUtil.getTrim(params, "_id"))),
                eq("deleted", false)
        );
        Map<String, Object> knowledge = knowledgeManager.where(query)
                .select("_id", "knowledgeName", "knowledgeType", "parentId", "leaf")
                .first();
        if (knowledge == null) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "查找不到知识点信息");
        }
        if (!KnowledgeUtil.isValidKnowledge(knowledge)) {
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "查找不到知识点(无效知识点)");
        }
        knowledge.put("_id", knowledge.get("_id").toString());
        knowledge.put("leaf", ObjectUtil.isValueEquals(knowledge.get("leaf"), "true") ? 1 : 0);
        return knowledge;
    }
    
    /**
     * 获取知识点信息
     * @return 知识点信息
     */
    private List<Map<String, Object>> getKnowledgeByObjectIds(List<ObjectId> knowledgeIdList, String... fields) {
        if (CollectionUtils.isEmpty(knowledgeIdList)) { return new ArrayList<>(); }
        Bson query = and(
                in("_id", knowledgeIdList),
                eq("deleted", false)
        );
        List<Map<String, Object>> knowledgeList = knowledgeManager.where(query).select(fields).listMap();
        return KnowledgeUtil.filterObsoleteKnowledgeAndSetId(knowledgeList);
    }
    
    /**
     * 获取试卷知识点 不去重
     *
     * @param params paperId courseId
     * @return
     */
    public List<Map<String, Object>> getAllKnowledgeList(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .isNotBlank("paperId")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        // 查询试卷
        List<Map<String, Object>> allKnowledgeList = new ArrayList<>();
        for (String paperIdStr : params.get("paperId").toString().split(",")) {
            Long paperId = MapUtil.getLongNullable(paperIdStr);
            if (paperId == null) { continue; }
            Map<String, Object> paperDetail = ownPaperService.getPaperDetail(MapUtil.of("paperId", paperId));
            List<Map<String, Object>> questionList = PaperUtil.getQuestions(paperDetail);
            for (Map<String, Object> question : questionList) {
                long questionCourseId = MapUtil.getLong(question, "courseId");
                if (questionCourseId == courseId) {
                    List<Map<String, Object>> knowledgeList = QuestionUtil.getKnowledgeList(question);
                    knowledgeList.forEach(knowledge -> knowledge.put("difficulty", question.get("difficulty")));
                    allKnowledgeList.addAll(knowledgeList);
                }
            }
        }
        //过滤废弃知识点
        List<ObjectId> knowledgeObjectIdList = new ArrayList<>();
        allKnowledgeList.forEach(knowledge -> knowledgeObjectIdList.add(new ObjectId(knowledge.get("knowledgeId").toString())));
        List<Map<String, Object>> knowledgeList = getKnowledgeByObjectIds(knowledgeObjectIdList, "_id", "knowledgeName");
        Map<String, List<Map<String, Object>>> knowledgeId2Info = knowledgeList.stream()
                .collect(groupingBy(i -> i.get("_id").toString()));
        allKnowledgeList.removeIf(next -> !knowledgeId2Info.containsKey(next.get("knowledgeId").toString()));
        return allKnowledgeList;
    }
    
    /**
     * 获取课程的id集合
     * @param courseIds  课程idSet
     * @return 课程的 knowledgeIdList
     */
    public Set<String> getKnowledgeIdSetByCourseIds(Set<Long> courseIds) {
        if (CollectionUtils.isEmpty(courseIds)) { return new HashSet<>(); }
        Bson knowledgeQuery = and(
                in("courseId", courseIds),
                eq("deleted", false)
        );
        List<Document> knowledgeList = knowledgeManager.where(knowledgeQuery).selectId().list();
        knowledgeList = KnowledgeUtil.filterObsoleteKnowledge(knowledgeList);
        return knowledgeList.stream()
                .map(item -> item.get("_id").toString())
                .collect(toSet());
    }
    
    
    /**
     * 获取知识点信息
     * @param knowledgeIdList 知识点id列表
     * @return 知识点信息 _id knowledgeName treeCode
     */
    public List<Document> getKnowledgeByObjectIds(Collection<ObjectId> knowledgeIdList) {
        if (CollectionUtils.isEmpty(knowledgeIdList)) { return new ArrayList<>(); }
        Bson knowledgeQuery = and(
                in("_id", knowledgeIdList),
                eq("deleted", false)
        );
        return knowledgeManager.where(knowledgeQuery)
                .select("_id", "knowledgeName", "treeCode")
                .list();
    }
    
    /**
     * 获取知识点信息 知识点转换专用
     * @return 知识点信息 _id knowledgeName treeCode
     */
    public List<Document> getKnowledgeByTreeCodes(Collection<String> TreeCodes) {
        if (CollectionUtils.isEmpty(TreeCodes)) { return new ArrayList<>(); }
        Bson knowledgeQuery = and(
                in("treeCode", TreeCodes),
                eq("deleted", false)
        );
        return knowledgeManager.where(knowledgeQuery)
                .select("_id", "knowledgeName", "treeCode")
                .list();
    }
    
    /**
     * 获取知识点信息 知识点转换专用
     * @return 知识点信息
     */
    List<Map<String, Object>> getKnowledgeByObjectIdsForKnowledgeExchange(List<ObjectId> knowledgeIdList) {
        if (CollectionUtils.isEmpty(knowledgeIdList)) { return new ArrayList<>(); }
        return knowledgeManager.where(in("_id", knowledgeIdList)).listMap();
    }
    
    /**
     * 获取知识点信息 知识点转换专用
     * @return 知识点信息
     */
    public List<Map<String, Object>> getKnowledgeByCourseIdForKnowledgeExchange(long courseId) {
        return knowledgeManager.where(eq("courseId", courseId)).listMap();
    }
    
    /**
     * 获取试卷知识点
     *
     * @param params paperId
     * @return
     */
    public List<Map<String, Object>> getPaperKnowledgeList(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("paperId")
                .verify();
        // 查询试卷
        List<Document> paperList = ownPaperService.getPaperList(params);
        // 查询科目知识点
        Set<Long> courseIds = new HashSet<>();
        for (Map<String, Object> paper : paperList) {
            List<Map<String, Object>> courseFullMarkList = MapUtil.getCast(paper, "courseFullMark");
            if (CollectionUtils.isNotEmpty(courseFullMarkList)) {
                for (Map<String, Object> courseFullMark : courseFullMarkList) {
                    courseIds.add(MapUtil.getLong(courseFullMark, "courseId"));
                }
            } else {
                courseIds.add(MapUtil.getLong(paper, "courseId"));
            }
        }
        
        Bson query = in("courseId", courseIds);
        List<Map<String, Object>> allKnowledgeList = knowledgeManager.where(query)
                .select("knowledgeName", "parentId", "courseId", "leaf")
                .listMap();
        Map<String, Map<String, Object>> allKnowledgeMap = allKnowledgeList.stream()
                .collect(toMap(item -> MapUtil.getTrim(item, "_id"), item -> item));
        
        // 获取试卷结构
        List<Map<String, Object>> answerCardStructure = ownPaperService.getAnswerCardStructure(params);
        
        // 查询试题完整信息
        List<String> questionIdList = new ArrayList<>();
        for (Map<String, Object> item : answerCardStructure) {
            if (item.get("questionId") != null) {
                questionIdList.add(item.get("questionId").toString());
            }
        }
        
        // 存在 questionId 的情况再统计知识点
        if (CollectionUtils.isNotEmpty(questionIdList)) {
            List<Document> questionList = ownQuestionService.getQuestionListSource(questionIdList);
            Map<String, Document> questionMap = questionList.stream()
                    .collect(toMap(item -> MapUtil.getTrim(item, "_id"), item -> item));
            List<Map<String, Object>> knowledgeList = new ArrayList<>();
            // 根据试题结构获取小问知识点
            answerCardStructure.forEach(item -> getQuestionItemKnowledge(allKnowledgeMap, questionMap, knowledgeList, item));
            return knowledgeList;
        }
        
        return new ArrayList<>();
    }
    
    // --------------------------------------------------------------------------------- 增删改等操作
    
    /**
     * 插入多个
     * @param knowledgeList 知识点List 调用者保证字段及类型
     */
    public void insertMany(List<Map<String, Object>> knowledgeList) {
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            List<Document> knowledgeDocumentList = knowledgeList.stream().map(Document::new).collect(Collectors.toList());
            insertManyDocument(knowledgeDocumentList);
        }
    }
    
    /**
     * 插入多个
     * @param knowledgeDocumentList 知识点List 调用者保证字段及类型
     */
    public void insertManyDocument(List<Document> knowledgeDocumentList) {
        if (CollectionUtils.isNotEmpty(knowledgeDocumentList)) {
            knowledgeDocumentList.forEach(item -> item.put("_id", new ObjectId(item.get("_id").toString())));
            knowledgeManager.insertMany(knowledgeDocumentList);
        }
    }
    
    /**
     * 替换
     * @param knowledge 知识点
     */
    public void replaceOne(Document knowledge) {
        ObjectId objectId = new ObjectId(knowledge.get("_id").toString());
        knowledge.put("_id", objectId);
        knowledgeManager.replaceOne(eq("_id", objectId), knowledge);
    }
    
    /**
     * 替换多个
     * @param knowledgeList 知识点
     */
    public void replaceMany(List<Map<String, Object>> knowledgeList) {
        if (CollectionUtils.isNotEmpty(knowledgeList)) {
            List<Document> knowledgeDocumentList = knowledgeList.stream().map(Document::new).collect(Collectors.toList());
            replaceManyDocument(knowledgeDocumentList);
        }
    }
    
    /**
     * 替换多个
     * @param knowledgeDocumentList 知识点
     */
    public void replaceManyDocument(List<Document> knowledgeDocumentList) {
        if (CollectionUtils.isNotEmpty(knowledgeDocumentList)) {
            List<ReplaceOneModel<Document>> replaceOneModelList = knowledgeDocumentList.stream().map(item -> {
                ObjectId objectId = new ObjectId(item.get("_id").toString());
                item.put("_id", objectId);
                return new ReplaceOneModel<>(
                        eq("_id", objectId),
                        item
                );
            }).collect(Collectors.toList());
            knowledgeManager.bulkWrite(replaceOneModelList);
        }
    }
    
    /**
     * 删除多个
     * @param deleteKnowledgeIdStringSet 删除的idStringSet
     */
    public void deleteManyByIdString(Set<String> deleteKnowledgeIdStringSet) {
        if (CollectionUtils.isNotEmpty(deleteKnowledgeIdStringSet)) {
            Set<ObjectId> deleteKnowledgeObjectIdList = deleteKnowledgeIdStringSet.stream()
                    .map(ObjectId::new)
                    .collect(Collectors.toSet());
            deleteManyById(deleteKnowledgeObjectIdList);
        }
    }
    
    /**
     * 删除多个
     * @param deleteKnowledgeObjectIdList 删除的idList
     */
    public void deleteManyById(List<ObjectId> deleteKnowledgeObjectIdList) {
        if (CollectionUtils.isNotEmpty(deleteKnowledgeObjectIdList)) {
            Set<ObjectId> deleteKnowledgeObjectIdSet = new HashSet<>(deleteKnowledgeObjectIdList);
            deleteManyById(deleteKnowledgeObjectIdSet);
        }
    }
    
    /**
     * 删除多个
     * @param deleteKnowledgeObjectIdSet 删除的idSet
     */
    public void deleteManyById(Set<ObjectId> deleteKnowledgeObjectIdSet) {
        if (CollectionUtils.isNotEmpty(deleteKnowledgeObjectIdSet)) {
            knowledgeManager.deleteMany(in("_id",  deleteKnowledgeObjectIdSet));
        }
    }
    
    /**
     * 删除课程知识点
     */
    public void deleteAllCourseKnowledge(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").verify();
        long courseId = MapUtil.getLong(params, "courseId");
        knowledgeManager.deleteMany(eq("courseId", courseId));
    }
    
    /**
     * 删除课程知识点
     */
    public void deleteAllSubjectKnowledge(Map<String, Object> params) {
        Verify.of(params)
                .isNotBlank("subject")
                .verify();
        knowledgeManager.deleteMany(eq("subject", params.get("subject").toString()));
    }

    /**
    * @Description: 递归构造知识点索引
    * @Param: knowledgeTreeList（知识点子树）
    * @Param: treeCode（索引值）
    */
    private void addTreeCode(List<Map<String, Object>> knowledgeTreeList, StringBuilder treeCode) {

        for (int i = 0; i < knowledgeTreeList.size(); i++) {
            String newTreeCode = treeCode.toString().replace("#", String.valueOf(i));

            Map<String, Object> knowledgeTree = knowledgeTreeList.get(i);
            knowledgeTree.put("treeCode", newTreeCode);

            List<Map<String, Object>> knowledgeTreeChild = (List<Map<String, Object>>) knowledgeTree.get("child");

            if (knowledgeTreeChild != null) {
                StringBuilder childTreeCode = new StringBuilder();
                childTreeCode.append(newTreeCode).append(".").append("#");

                addTreeCode(knowledgeTreeChild, childTreeCode);
            }
        }
    }
    
    /**
     * 初始化三级结构知识点树
     * @return 知识点树
     */
    private List<Map<String, Object>> initKnowledge(Map<String, Object> params) {
        Verify.of(params).isValidId("courseId").isNotBlank("courseName").verify();
        long courseId = MapUtil.getLong(params, "courseId");
        String courseName = MapUtil.getTrim(params, "courseName");
        Map<String, Object> course = commonCourseService.getCourseById(params);
        if (MapUtils.isEmpty(course)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "课程不存在: " + courseId + " - " + courseName);
        }
        int stage = MapUtil.getInt(course, "stage");
        courseName = MapUtil.getTrim(course, "courseName");
        long userId = MapUtil.getLong(params, "userId");
        String userName = MapUtil.getTrim(params, "userName");
        String currentDateTime = DateUtil.getCurrentDateTime();
        
        List<Map<String, Object>> knowledgeList = new ArrayList<>();
        String parentId = null;
        for (int i = 1; i <= 3; i++) {
            Document knowledge = new Document()
                    .append("stage", stage)
                    .append("courseId", courseId)
                    .append("courseName", courseName)
                    .append("knowledgeName", courseName)
                    .append("parentId", parentId)
                    .append("deep", i)
                    .append("sort", 1)
                    .append("leaf", i == 3)
                    // .append("treeCode", ?)
                    .append("deleted", false)
                    .append("creatorId", userId)
                    .append("creatorName", userName)
                    .append("createDateTime", currentDateTime)
                    .append("modifierId", userId)
                    .append("modifierName", userName)
                    .append("modifyDateTime", currentDateTime)
                    ;
            knowledgeManager.insertOne(knowledge);
            String knowledgeId = MapUtil.getTrim(knowledge, "_id");
            knowledge.put("_id", knowledgeId);
            knowledge.put("knowledgeId", knowledgeId);
            knowledgeList.add(knowledge);
            parentId = knowledgeId;
        }
        return knowledgeList;
    }
    
    /**
     * 获取小问的知识点
     *
     * @param questionMap
     * @param knowledgeList
     * @param item
     */
    private void getQuestionItemKnowledge(Map<String, Map<String, Object>> allKnowledgeMap, Map<String, Document> questionMap, List<Map<String, Object>> knowledgeList, Map<String, Object> item) {
        if (ObjectUtil.isBlank(item.get("questionId"))) {
            return;
        }
        String questionId = item.get("questionId").toString();
        Integer questionIndex = Integer.valueOf(item.get("questionIndex").toString());

        Map<String, Object> currentQuestion = questionMap.get(questionId);
        if(currentQuestion == null){
            return;
        }

        Double difficulty = Double.valueOf(currentQuestion.getOrDefault("difficulty", "0").toString());

        if (questionIndex >= 0) {
            // 获取小问
            List questionItems = (List) currentQuestion.get("questions");

            // 当前小问知识点
            if (questionItems != null && questionItems.get(questionIndex) != null) {
                Map currentItem = (Map) questionItems.get(questionIndex);
                List<Map<String, Object>> currentKnowledgeList = (List<Map<String, Object>>) currentItem.get("knowledgeList");
                addKnowledge(allKnowledgeMap, item, knowledgeList, currentKnowledgeList, difficulty);
            }
        } else {
            // 获取试题
            List<Map<String, Object>> questionKnowledgeList = QuestionUtil.getKnowledgeList(currentQuestion);
            addKnowledge(allKnowledgeMap, item, knowledgeList, questionKnowledgeList, difficulty);
        }

        // 同时将试题知识点加入,有点多余
        //List<Map<String, Object>> currentKnowledgeList = (List<Map<String, Object>>) currentQuestion.get("knowledgeList");
        //addKnowledge(allKnowledgeMap, item, knowledgeList, currentKnowledgeList, difficulty);
    }

    /**
     * 添加知识点
     *
     * @param allKnowledgeMap
     * @param question
     * @param knowledgeList
     * @param currentKnowledgeList
     */
    private void addKnowledge(Map<String, Map<String, Object>> allKnowledgeMap, Map<String, Object> question, List<Map<String, Object>> knowledgeList, List<Map<String, Object>> currentKnowledgeList, Double difficulty) {
        if (CollectionUtils.isNotEmpty(currentKnowledgeList)) {
            currentKnowledgeList.forEach(k -> {
                Map<String, Object> currentKnowledge = allKnowledgeMap.get(k.getOrDefault("_id", k.get("knowledgeId")).toString());
                Object questionId = question.get("questionId");
                //todo
                if (null == questionId || null == currentKnowledge) {
                    return;
                }
                currentKnowledge = new HashMap<>(currentKnowledge);
                currentKnowledge.put("questionId", questionId);
                currentKnowledge.put("questionNumber", question.get("questionNumber"));
                currentKnowledge.put("difficulty", difficulty);
                knowledgeList.add(currentKnowledge);
            });
        }
    }
    
    // ------------------------------------------------------------------------- init 从母版数据同步
    
    @Autowired
    private KnowledgeMasterThirdFactory knowledgeMasterThirdFactory;
    @Autowired
    private CourseServiceImpl courseService;
    
    public void syncFromMasterMask() {
        for (long courseId : knowledgeMasterThirdFactory.getKeySet()) {
            try {
                Map<String, Object> params = MapUtil.of("courseId", courseId);
                courseService.getCourseDetailMustExist(params);
                syncFromMasterMask(params);
            } catch (Exception e) {
                LOGGER.error("同步母版数据 失败: {}, cause: {}",
                        KnowledgeMasterServiceImpl.getDataVersionBusinessDesc(courseId),
                        e.getMessage()
                );
            }
        }
    }
    
    public Map<String, Object> syncFromMasterMask(Map<String, Object> params) {
        Verify.of(params)
                .isValidId("courseId")
                .verify();
        long courseId = MapUtil.getLong(params, "courseId");
        Map<String, Object> masterVersion = tikuBaseDataVersionService.executeWithMasterLocalVersionCheck(
                KnowledgeMasterServiceImpl.getDataVersionBusinessType(),
                KnowledgeMasterServiceImpl.getDataVersionBusinessKey(courseId),
                KnowledgeMasterServiceImpl.getDataVersionBusinessDesc(courseId),
                () -> {
                    List<Map<String, Object>> knowledgeMasterList = knowledgeMasterService.getKnowledgeMasterList(params);
                    if (CollectionUtils.isEmpty(knowledgeMasterList)) {
                        throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "母版数据获取不到知识点信息");
                    }
                    initKnowledgeFromMasterMaskByCourseAll(courseId, knowledgeMasterList);
                });
        // 课程同步完了打标记
        if (MapUtils.isNotEmpty(masterVersion)) {
            KnowledgeUtil.setKnowledgeSyncedFromMaster(courseId);
        }
        return masterVersion;
    }
    
    /**
     * 初始化知识点信息 这个方法不要乱调
     *   删除原来课程的数据
     *   新增新的数据
     * @param courseId          课程id
     * @param knowledgeMasterList 知识点数据
     */
    private void initKnowledgeFromMasterMaskByCourseAll(long courseId, List<Map<String, Object>> knowledgeMasterList) {
        if (CollectionUtils.isEmpty(knowledgeMasterList)) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点数据为空，不允许初始化");
        }
        
        long userId = 2L;
        String userName = "系统:题库管理员";
        
        List<Document> insertList = knowledgeMasterList.stream()
                .map(knowledge -> {
                    knowledge.put("creatorId", userId);
                    knowledge.put("creatorName", userName);
                    knowledge.put("modifierId", userId);
                    knowledge.put("modifierName", userName);
                    return knowledgeMasterService.convertDocumentKnowledge(knowledge);
                })
                .collect(Collectors.toList());
        
        knowledgeManager.deleteMany(eq("courseId", courseId));
        knowledgeManager.insertMany(insertList);
    }
}
