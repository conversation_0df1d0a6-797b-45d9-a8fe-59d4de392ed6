package com.dongni.tiku.render.service;

import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.render.bean.dto.IReceiveDTO;
import com.dongni.tiku.render.bean.param.IAckParam;
import com.dongni.tiku.render.bean.param.ICreateParam;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Updates.*;
import static com.mongodb.client.model.Updates.addToSet;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/5 周四 下午 05:37
 * @Version 1.0.0
 */
public abstract class AbstractTikuRenderQuestionTaskService<T extends ICreateParam<?>, R extends IReceiveDTO<?>, X extends IAckParam<?>>
        implements ITikuRenderQuestionTaskService<T, R, X> {

    /** 支持的课程  */
    public static final Set<Long> SUPPORT_COURSE_ID_SET = Collections.unmodifiableSet(
            Stream.of(
                    23,             // 小学 数学
                            13, 15, 16, 17, // 初中 数学 物理 化学 生物
                            3, 5, 6, 7      // 高中 数学 物理 化学 生物
                    )
                    .map(Long::valueOf)
                    .collect(Collectors.toSet())
    );

    /**
     * 课程支持
     * @param courseId 课程id
     * @return true支持
     */
    public boolean courseSupport(long courseId) {
        return SUPPORT_COURSE_ID_SET.contains(courseId);
    }

    /**
     * 保存任务
     */
    public void doCreateTask(T param) {
        // 查询来源任务正在待执行中的任务 如果存在 则该任务不真正添加
        int taskStatusPending = DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "pending");
        Bson queryPending = and(
                eq(getIdFieldName(), param.getId()),
                eq("status", taskStatusPending)
        );
        List<Document> pendingTaskList = getManager().getList(queryPending);
        boolean createPendingTask = CollectionUtils.isEmpty(pendingTaskList);

        Date now = new Date();
        String formatDateTimeMills = DateUtil.formatDateTimeMills(now);
        int status;
        String comment;
        if (createPendingTask) {
            status = taskStatusPending;
            comment = "[" + formatDateTimeMills + "]新建待执行任务";
        } else {
            status = DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "canceled");
            comment = "[" + formatDateTimeMills + "]新建取消任务，已有任务在待执行: " +
                    pendingTaskList.stream()
                            .map(item -> MapUtil.getTrim(item, "_id"))
                            .collect(Collectors.joining(";"));
        }

        Document insertDoc = new Document();
        insertDoc.append(getIdFieldName(), param.getId());
        insertDoc.append("taskSource", param.getTaskSource());
        insertDoc.append("status", status);
        insertDoc.append("statusName", DictUtil.getDictLabel("renderQuestionByPaperTaskStatus", status));
        insertDoc.append("createTimestamp", now.getTime());
        insertDoc.append("createTime", formatDateTimeMills);
        insertDoc.append("processTimestamp", null);
        insertDoc.append("processTime", null);
        insertDoc.append("finishTimestamp", createPendingTask ? null : insertDoc.get("createTimestamp"));
        insertDoc.append("finishTime", createPendingTask ? null : insertDoc.get("createTime"));
        insertDoc.append("renderCostMs", null);
        insertDoc.append("comment", Stream.of(comment).collect(Collectors.toList()));
        insertDoc.append("error", null);
        getManager().insertOne(insertDoc);
    }

    /**
     * 领取任务
     *    找到待执行的任务 pending
     *    更新为 执行中 并写入应答超时时间
     *    返回给调用方 找不到待执行任务则为null
     */
    @Override
    public R receiveTask() {
        Date now = new Date();
        String formatDateTimeMills = DateUtil.formatDateTimeMills(now);
        int taskStatusPending = DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "pending");
        Bson queryPending = eq("status", taskStatusPending);
        Bson update = combine(
                set("status", DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "process")),
                set("statusName", DictUtil.getDictLabel("renderQuestionByPaperTaskStatus", "process")),
                set("processTimestamp", now.getTime()),
                set("processTime", formatDateTimeMills),
                set("progressTimeoutTimestamp", now.getTime() + (1000L * 60 * 60)),  // 60分钟应答超时
                addToSet("comment", "[" + formatDateTimeMills + "]领取渲染任务")
        );
        FindOneAndUpdateOptions options = new FindOneAndUpdateOptions().sort(new Document("createTimestamp", 1));
        Document oldDoc = getManager().findOneAndUpdate(queryPending, update, options);
        if (MapUtils.isEmpty(oldDoc)) {
            return null;  // 没有找到任何任务
        }

        R receiveDTO = newReceiveDTO();
        receiveDTO.setTaskId(MapUtil.getTrim(oldDoc, "_id"));
        receiveDTO.setId(MapUtil.getCast(oldDoc, getIdFieldName()));
        receiveDTO.setReceiveTimestamp(now.getTime());
        receiveDTO.setReceiveFormatDateTimeMills(formatDateTimeMills);
        return receiveDTO;
    }

    /**
     * 保存执行结果
     */
    protected boolean doAckTask(X param) {
        String taskId = param.getTaskId();
        ObjectId taskObjectId = MongoUtil.getMongoId(taskId);
        Map<String, Object> error = param.getError();
        boolean success = MapUtils.isEmpty(error);
        int taskStatusSuccess = DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "success");
        int taskStatusFail = DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "fail");
        int status = success ? taskStatusSuccess : taskStatusFail;
        Date now = new Date();
        long nowTimestamp = now.getTime();
        String formatDateTimeMills = DateUtil.formatDateTimeMills(now);
        Long costMs = null;
        if (param.getReceiveTimestamp() != null) {
            costMs = nowTimestamp - param.getReceiveTimestamp();
        }
        Bson query = and(
                eq("_id", taskObjectId),
                eq(getIdFieldName(), param.getId())
        );
        List<Bson> updateList = new ArrayList<>();
        updateList.add(set("status", status));
        updateList.add(set("statusName", DictUtil.getDictLabel("renderQuestionByPaperTaskStatus", status)));
        updateList.add(set("finishTimestamp", nowTimestamp));
        updateList.add(set("finishTime", formatDateTimeMills));
        updateList.add(set("error", error));
        updateList.add(unset("progressTimeoutTimestamp"));
        updateList.add(addToSet("comment", "[" + formatDateTimeMills + "]应答结果["
                + param.getReceiveFormatDateTimeMills() + "](cost: " + costMs + " ms): success: " + success));
        if (success && costMs != null) {
            updateList.add(set("renderCostMs", costMs));
        }
        Document oldDoc = getManager().findOneAndUpdate(query, combine(updateList));
        if (MapUtils.isEmpty(oldDoc)) {
            return false;
        }
        // 应答失败时，防止之前的应答成功被覆盖掉
        if (!success && taskStatusSuccess == MapUtil.getInt(oldDoc, "status")) {
            getManager().updateOne(query, combine(
                    set("status", taskStatusSuccess),
                    set("statusName", MapUtil.getStringNullable(oldDoc, "statusName")),
                    addToSet("comment", "[" + DateUtil.formatDateTimeMills(new Date()) + "]应答结果时修复应答成功被失败覆盖")
            ));
        }
        return true;
    }

    /**
     * 将执行中的超时任务置为待执行状态
     */
    @Override
    public void rePendingProcessTimeoutTask() {
        Date now = new Date();
        String formatDateTimeMills = DateUtil.formatDateTimeMills(now);
        Bson query = and(
                eq("status", DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "process")),
                lte("progressTimeoutTimestamp", System.currentTimeMillis())
        );
        Bson update = combine(
                set("status", DictUtil.getDictValue("renderQuestionByPaperTaskStatus", "pending")),
                set("statusName", DictUtil.getDictLabel("renderQuestionByPaperTaskStatus", "pending")),
                unset("progressTimeoutTimestamp"),
                addToSet("comment", "[" + formatDateTimeMills + "]执行中任务超时未应答重新进入待执行")
        );
        getManager().updateMany(query, update);
    }
}
