package com.dongni.tiku.render.service;

import com.dongni.common.mongo.IManager;
import com.dongni.common.mongo.Order;
import com.dongni.common.utils.DictUtil;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.lock.DistributeLock;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.manager.impl.RenderQuestionByPaperTaskManager;
import com.dongni.tiku.own.service.OwnPaperService;
import com.dongni.tiku.render.bean.dto.TikuRenderQuestionByPaperTaskReceiveDTO;
import com.dongni.tiku.render.bean.dto.TikuRenderQuestionByQuestionTaskReceiveDTO;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByPaperTaskAckParam;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByPaperTaskCreateParam;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByQuestionTaskAckParam;
import com.dongni.tiku.render.bean.param.TikuRenderQuestionByQuestionTaskCreateParam;
import com.mongodb.Block;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.BsonNull;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Updates.addToSet;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static com.mongodb.client.model.Updates.unset;

/**
 * 渲染试题图片给小力ai使用
 *    从试卷出发 -> 渲染试题图片
 *    https://scm.nicezhuanye.com/confluence/pages/viewpage.action?pageId=96803335
 * <AUTHOR>
 * @date 2025/04/21
 */
@Service
public class TikuRenderQuestionByPaperTaskService extends AbstractTikuRenderQuestionTaskService
        <TikuRenderQuestionByPaperTaskCreateParam,
         TikuRenderQuestionByPaperTaskReceiveDTO,
         TikuRenderQuestionByPaperTaskAckParam> {
    
    private static final Logger log = LoggerFactory.getLogger(TikuRenderQuestionByPaperTaskService.class);
    
    @Autowired
    private TikuRenderQuestionByPaperTaskService selfService;
    @Autowired
    private RenderQuestionByPaperTaskManager renderQuestionByPaperTaskManager;
    @Autowired
    private OwnPaperService ownPaperService;

    /**
     * 注册创建任务
     * @param paperId 试卷id
     * @param taskSource 任务来源
     */
    public void registerSynchronizationToCreateRenderQuestionByPaperTask(long paperId,
                                                                         String taskSource) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                createRenderQuestionByPaperTask(paperId, taskSource);
            }
        });
    }
    /**
     * 创建任务
     * @param paperId 试卷id
     * @param taskSource 任务来源
     */
    public void createRenderQuestionByPaperTask(long paperId, String taskSource) {
        try {
            Document paper = ownPaperService.getSimplePaper(paperId);
            long courseId = MapUtil.getLong(paper, "courseId");
            if (!courseSupport(courseId)) {
                return;   // 不支持的课程不需要生成
            }
            int creationType = MapUtil.getInt(paper, "creationType");
            if (!DictUtil.isEquals(creationType, "paperCreationType", "normal")) {
                return;   // 不是试卷的不需要生成
            }
            TikuRenderQuestionByPaperTaskCreateParam param = new TikuRenderQuestionByPaperTaskCreateParam();
            param.setPaperId(paperId);
            param.setTaskSource(taskSource);
            selfService.createTask(param);
        } catch (Exception e) {
            log.error("题库试题渲染图片创建任务失败: paperId: {}; taskSource: {}", paperId, taskSource, e);
        }
        
    }

    /**
     * 创建任务
     *    1. 新建待执行任务 pending
     *    2. 新建取消任务  canceled  如果paperId在等待执行了，则不添加执行任务，直接将任务置为取消废弃状态
     */
    @DistributeLock(moduleName = "TIKU", name = "renderQuestionByPaperTask", argValueKeys = { "[0].paperId" }, waitTime = 5)
    @Override
    public void createTask(TikuRenderQuestionByPaperTaskCreateParam param) {
        Verify2.of(param)
                .isValidId(TikuRenderQuestionByPaperTaskCreateParam::getPaperId)
                .isNotBlank(TikuRenderQuestionByPaperTaskCreateParam::getTaskSource)
                .verify();

        doCreateTask(param);
    }

    /**
     * 保存执行结果
     */
    @Override
    public boolean ackTask(TikuRenderQuestionByPaperTaskAckParam param) {
        Verify2.of(param)
                .isNotBlank(TikuRenderQuestionByPaperTaskAckParam::getTaskId)
                .isValidId(TikuRenderQuestionByPaperTaskAckParam::getPaperId)
                .verify();

        return doAckTask(param);
    }

    
    @Autowired
    private PaperManager paperManager;
    
    /**
     * 手动执行对旧数据统一产生任务
     */
    public void genTask() {
        Bson query = and(
                in("courseId", SUPPORT_COURSE_ID_SET),
                eq("creationType", DictUtil.getDictValue("paperCreationType", "normal")),
                or(
                        exists("sourcePaperId", false),
                        eq("sourcePaperId", new BsonNull())
                )
        );
        String[] includes = new String[] { "paperId" };
        Order order = Order.Field.desc("paperId");
        paperManager.getFindIterable(query, includes, null, order, null, null).forEach((Block<? super Document>) paper -> {
            Long paperId = MapUtil.getLongNullable(paper, "paperId");
            if (paperId == null) {
                return;
            }
            TikuRenderQuestionByPaperTaskCreateParam param = new TikuRenderQuestionByPaperTaskCreateParam();
            param.setPaperId(paperId);
            param.setTaskSource("手动执行对旧数据统一产生任务");
            selfService.createTask(param);
        });
        
    }

    @Override
    public String getIdFieldName() {
        return "paperId";
    }

    @Override
    public IManager getManager() {
        return renderQuestionByPaperTaskManager;
    }

    @Override
    public TikuRenderQuestionByPaperTaskReceiveDTO newReceiveDTO() {
        return new TikuRenderQuestionByPaperTaskReceiveDTO();
    }
}
