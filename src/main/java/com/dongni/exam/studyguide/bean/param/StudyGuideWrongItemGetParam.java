package com.dongni.exam.studyguide.bean.param;

import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/15 周一 下午 05:17
 * @Version 1.0.0
 */
@Getter
@Setter
public class StudyGuideWrongItemGetParam {
    private Long studentId;
    private Long courseId;
    private Long examId;

    public StudyGuideWrongItemGetParam(Long studentId,
                                       Long courseId,
                                       Long examId) {
        this.studentId = studentId;
        this.courseId = courseId;
        this.examId = examId;
    }

    public void verify() {
        Verify2.of(this)
                .isValidId(StudyGuideWrongItemGetParam::getStudentId)
                .isValidId(StudyGuideWrongItemGetParam::getCourseId)
                .isValidId(StudyGuideWrongItemGetParam::getExamId)
                .verify();
    }
}
