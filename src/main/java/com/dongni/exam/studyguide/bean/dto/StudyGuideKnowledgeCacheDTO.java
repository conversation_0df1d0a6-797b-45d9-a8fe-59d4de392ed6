package com.dongni.exam.studyguide.bean.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/09/15
 */
@Data
public class StudyGuideKnowledgeCacheDTO {
    
    /** 是否已经删除 删除的没有以下的信息 */
    private boolean deleted;
    /** 知识点id */
    private String knowledgeId;
    /** 知识点名称 */
    private String knowledgeName;
    /** treeCode */
    private String treeCode;
    
    /**
     * 分析的treeCode 取三层
     * 如果 treeCode 为 1.2.3.4.5.6 则 analysisTreeCode 为 1.2.3
     * 如果 treeCode 为 1.2.3 则 analysisTreeCode 为 1.2.3
     * 如果 treeCode 为 1.2 则 analysisTreeCode 为 1.2
     */
    private String analysisTreeCode;
    
}
