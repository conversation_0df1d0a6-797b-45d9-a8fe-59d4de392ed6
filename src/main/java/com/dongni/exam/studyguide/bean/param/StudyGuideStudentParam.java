package com.dongni.exam.studyguide.bean.param;

import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.utils.verify.Verify2;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@Getter
@Setter
public class StudyGuideStudentParam extends BaseRequestParams {

	private static final long serialVersionUID = -5310723355799363159L;

	private Long examId;

	private Long studentId;

	public void verify() {
		Verify2.of(this)
				.isValidId(StudyGuideStudentParam::getExamId)
				.isValidId(StudyGuideStudentParam::getStudentId)
				.verify();
	}
}
