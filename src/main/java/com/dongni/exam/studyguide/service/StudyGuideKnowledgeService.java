package com.dongni.exam.studyguide.service;

import com.dongni.common.utils.MongoUtil;
import com.dongni.exam.studyguide.bean.dto.StudyGuideQuestionScoreDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideKnowledgeCacheDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideKnowledgeRateDTO;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2025/09/15
 */
@Service
public class StudyGuideKnowledgeService {
    
    @Autowired
    private OwnKnowledgeService ownKnowledgeService;

    
    public List<StudyGuideKnowledgeRateDTO> computeKnowledgeRate(List<StudyGuideQuestionScoreDTO> studyGuideQuestionScoreDTOList,
                                                                 Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                                                 Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
        // 汇集所有的知识点id 用于查询知识点信息
        List<String> allKnowledgeIdList = studyGuideQuestionScoreDTOList.stream()
                .map(StudyGuideQuestionScoreDTO::getKnowledgeIdList)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        // 处理知识点信息
        handlerCacheKnowledgeInfo(allKnowledgeIdList, knowledgeId2InfoCache, knowledgeTreeCode2InfoCache);
        
        // 对于每一个试题，其知识点信息需要进行转换为统计的知识点信息，如果转换后重复了需要去重
        // 试题 知识点
        //  知识点1   1.2.3.4.5.6 -> 取analysisTreeCode -> 1.2.3
        //  知识点2   1.2.3.4     -> 取analysisTreeCode -> 1.2.3
        //  知识点3   1.2.4.3     -> 取analysisTreeCode -> 1.2.4
        //  知识点4   1.2         -> 取analysisTreeCode -> 1.2
        //  知识点5   1.3.4.5     -> 已删除的知识点不参与计算
        //  知识点6   1.4.2.3.4   -> 取analysisTreeCode -> 1.4.2 -> 已删除的知识点不参与计算
        // 去重后得该题的知识点为 1.2  1.2.3  1.2.4
        
        
        // 将试题的分值及得分赋值给每一个知识点
        // 试题1  题目分数10分   得分6分
        // 则 知识点1.2   试题1 分数10分   得分6分
        //    知识点1.2.3 试题1 分数10分   得分6分
        //    知识点1.2.4 试题1 分数10分   得分6分
        
        
        // 将知识点进行分组并统计关联的试题信息 questionId2ScoreInfo
        
        // 对没有得满分的计算到错题数量中 wrongQuestionCount
        
        // 计算总的得分率 设置到 scoreRate
        
        return new ArrayList<>();
    }
    
    public void handlerCacheKnowledgeInfo(List<String> knowledgeIdList,
                                          Map<String, StudyGuideKnowledgeCacheDTO> knowledgeId2InfoCache,
                                          Map<String, StudyGuideKnowledgeCacheDTO> knowledgeTreeCode2InfoCache) {
        // 没有缓存的知识点id
        List<String> nocacheKnowledgeIdList = knowledgeIdList.stream()
                .filter(knowledgeId2InfoCache::containsKey)
                .collect(Collectors.toList());
        // 没有缓存的知识点ObjectId
        List<ObjectId> nocacheKnowledgeObjectIdList = nocacheKnowledgeIdList.stream()
                .map(MongoUtil::tryGetMongoId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 获取知识点信息
        List<Document> knowledgeInfoListByIdList = ownKnowledgeService.getKnowledgeByObjectIds(nocacheKnowledgeObjectIdList);
        Map<String, Document> knowledgeId2KnowledgeInfo = knowledgeInfoListByIdList.stream()
                .collect(toMap(knowledge -> MapUtil.getTrim(knowledge, "_id"), Function.identity()));
        // 收集未被缓存的treeCode知识点信息
        Set<String> nocacheTreeCodeSet = new HashSet<>();
        for (String nocacheKnowledgeId : nocacheKnowledgeIdList) {
            Document knowledgeInfo = knowledgeId2KnowledgeInfo.get(nocacheKnowledgeId);
            StudyGuideKnowledgeCacheDTO knowledgeCacheDTO = new StudyGuideKnowledgeCacheDTO();
            knowledgeId2InfoCache.put(nocacheKnowledgeId, knowledgeCacheDTO);
            
            if (MapUtils.isEmpty(knowledgeInfo)) {
                knowledgeCacheDTO.setDeleted(true);  // 已经被删除了 查不到知识点信息了
                continue;
            }
            
            knowledgeCacheDTO.setDeleted(false);
            knowledgeCacheDTO.setKnowledgeId(nocacheKnowledgeId);
            knowledgeCacheDTO.setKnowledgeName(MapUtil.getTrim(knowledgeInfo, "knowledgeName"));
            String treeCode = MapUtil.getTrim(knowledgeInfo, "treeCode");
            knowledgeCacheDTO.setTreeCode(treeCode);
            knowledgeTreeCode2InfoCache.put(treeCode, knowledgeCacheDTO);
            
            String[] treeCodeSplit = treeCode.split("\\.");
            if (treeCodeSplit.length <= 3) {
                knowledgeCacheDTO.setAnalysisTreeCode(treeCode);
                continue;  // 小于三级知识点的不处理
            }
            // 留三级
            String analysisTreeCode = Arrays.stream(treeCodeSplit)
                    .limit(3)
                    .collect(Collectors.joining("."));
            knowledgeCacheDTO.setAnalysisTreeCode(analysisTreeCode);
            if (!knowledgeTreeCode2InfoCache.containsKey(analysisTreeCode)) {
                nocacheTreeCodeSet.add(analysisTreeCode);  // 需要后续获取treeCode对应的知识点信息
            }
            
        }
        // 处理 treeCode -> knowledgeInfo
        List<Document> knowledgeInfoListByTreeCodes = ownKnowledgeService.getKnowledgeByTreeCodes(nocacheTreeCodeSet);
        Map<String, Document> treeCode2KnowledgeInfo = knowledgeInfoListByTreeCodes.stream()
                .collect(toMap(knowledge -> MapUtil.getTrim(knowledge, "treeCode"), Function.identity()));
        for (String nocacheTreeCode : nocacheTreeCodeSet) {
            Document knowledgeInfo = treeCode2KnowledgeInfo.get(nocacheTreeCode);
            StudyGuideKnowledgeCacheDTO knowledgeCacheDTO = new StudyGuideKnowledgeCacheDTO();
            knowledgeId2InfoCache.put(nocacheTreeCode, knowledgeCacheDTO);
            if (MapUtils.isEmpty(knowledgeInfo)) {
                knowledgeCacheDTO.setDeleted(true);
            } else {
                knowledgeCacheDTO.setDeleted(false);
                knowledgeCacheDTO.setKnowledgeId(MapUtil.getTrim(knowledgeInfo, "_id"));
                knowledgeCacheDTO.setKnowledgeName(MapUtil.getTrim(knowledgeInfo, "knowledgeName"));
                knowledgeCacheDTO.setTreeCode(nocacheTreeCode);
                knowledgeCacheDTO.setAnalysisTreeCode(nocacheTreeCode);
            }
        }
    }
    
}
