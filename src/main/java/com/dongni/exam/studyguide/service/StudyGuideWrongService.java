package com.dongni.exam.studyguide.service;

import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongItemGetParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/9/15 周一 下午 05:15
 * @Version 1.0.0
 */
@Service
public class StudyGuideWrongService {
    @Autowired
    private ExamRepository examRepository;

    /**
     * 获取教辅作业错题明细
     */
    public List<StudyGuideWrongItemDTO> getStudyGuideWrongItem(StudyGuideWrongItemGetParam param) {
        param.verify();

        return examRepository.selectList("StudyGuideWrongMapper.getStudyGuideWrongItem", param);
    }
}
