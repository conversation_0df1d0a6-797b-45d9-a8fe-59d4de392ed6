package com.dongni.exam.studyguide.service;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.commons.entity.BaseRequestParams;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.verify.Verify2;
import com.dongni.exam.newcard.parse.enumeration.ExamResultStatusEnum;
import com.dongni.exam.newcard.service.AnswerCardServiceV3;
import com.dongni.exam.studyguide.bean.dto.ExamInfoDTO;
import com.dongni.exam.studyguide.bean.dto.StuStudyGuideDTO;
import com.dongni.exam.studyguide.bean.dto.StudyGuideWrongItemDTO;
import com.dongni.exam.studyguide.bean.param.StudyGuideStudentParam;
import com.dongni.exam.studyguide.bean.param.StudyGuideWrongItemGetParam;
import com.dongni.exam.studyguide.bean.vo.StuStudyGuideDetailVO;
import com.dongni.exam.studyguide.bean.vo.StuStudyGuidePageVO;
import com.dongni.exam.studyguide.bean.vo.StuStudyGuideQuestionVO;
import com.dongni.exam.studyguide.bean.vo.StuStudyGuideVO;
import com.dongni.exam.studyguide.manager.StudyGuideStudentManager;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.common.util.PaperUtil;
import com.dongni.tiku.manager.impl.PaperManager;
import com.dongni.tiku.own.service.OwnQuestionService;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Sorts;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static java.util.stream.Collectors.toMap;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@Service
public class StudyGuideStudentService {

	/**
	 * 获取Mongodb数据库对象
	 */
	private final MongoDatabase mongo;

	@Autowired
	private StudyGuideStudentManager studyGuideStudentManager;
	@Autowired
	private PaperManager paperManager;
	@Autowired
	private AnswerCardServiceV3 answerCardServiceV3;
	@Autowired
	private OwnQuestionService ownQuestionService;
	@Autowired
	private StudyGuideWrongService studyGuideWrongService;

	@Autowired
	public StudyGuideStudentService(AnalysisMongodb analysisMongodb) {
		this.mongo = analysisMongodb.getMongoDatabase();
	}

	/**
	 * 获取最近三场教辅作业信息
	 * @param param studentId
	 */
	public List<StuStudyGuideVO> getStuStudyGuideHomeworkLatest(StudyGuideStudentParam param) {
		Verify2.of(param)
				.isValidId(StudyGuideStudentParam::getStudentId)  // quark 由nzyToken鉴权后设置
				.verify();
		param.setCurrentIndex(0);
		param.setPageSize(3);      // 获取最后三场考试
		List<ExamInfoDTO> examList = studyGuideStudentManager.getExamListByQuery(param);
		return buildStuStudyGuideVOList(param.getStudentId(), examList);
	}

	/**
	 * 获取教辅作业列表信息(分页)
	 * @param param studentId currentIndex pageSize
	 */
	public StuStudyGuidePageVO getStuStudyGuideHomeworkList(StudyGuideStudentParam param) {
		Verify2.of(param)
				.isValidId(StudyGuideStudentParam::getStudentId)  // quark 由nzyToken鉴权后设置
				.isPositive(BaseRequestParams::getPageSize)
				.isNatural(BaseRequestParams::getCurrentIndex)
				.verify();
		int totalCount = studyGuideStudentManager.getExamCountByQuery(param);
		List<ExamInfoDTO> examList = totalCount == 0 || param.getCurrentIndex() >= totalCount
				? new ArrayList<>()
				: studyGuideStudentManager.getExamListByQuery(param);
		List<StuStudyGuideVO> stuStudyGuideVOList = buildStuStudyGuideVOList(param.getStudentId(), examList);;
		
		StuStudyGuidePageVO stuStudyGuidePageVO = new StuStudyGuidePageVO();
		stuStudyGuidePageVO.setTotalCount(totalCount);
		stuStudyGuidePageVO.setList(stuStudyGuideVOList);
		return stuStudyGuidePageVO;
	}

	/**
	 * 获取学生的单场作业报告详情
	 * @param param studentId examId
	 */
	public StuStudyGuideDetailVO getStuStudyGuideHomeworkDetail(StudyGuideStudentParam param) {
		param.verify();

		ExamInfoDTO examInfoDTO = studyGuideStudentManager.getExamByQuery(param);
		if (examInfoDTO == null) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "作业不存在或还未公布，无法查看！examId：" + param.getExamId());
		}
		long examId = param.getExamId();
		Long studentId = param.getStudentId();
		List<StuStudyGuideDTO> stuStudyGuideList = studyGuideStudentManager.getStuStudyGuideListByQuery(Collections.singletonList(examId),
			studentId);
		if (stuStudyGuideList.isEmpty()) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业数据出现异常，请联系管理员处理！examId：" + examId);
		}
		StuStudyGuideDTO stuStudyGuideDTO = stuStudyGuideList.get(0);
		Bson baseFilter = and(eq("examId", examId), eq("statId", 0), eq("courseId", stuStudyGuideDTO.getCourseId()),
			eq("paperId", stuStudyGuideDTO.getPaperId()));

		Document stuCourseStatDoc = mongo.getCollection("examStudentCourseStat")
			.find(and(baseFilter, eq("studentId", studentId)))
			.projection(fields(include("studentName", "totalScore", "fullMark", "scoreRate", "classRanking", "paperQuestionCount",
					"paperWrongQuestionCount"), excludeId()))
			.first();
		Document classCourseStatDoc = mongo.getCollection("examClassCourseStat")
			.find(and(baseFilter, eq("classId", stuStudyGuideDTO.getClassId())))
			.projection(fields(include("totalStudent", "participationNumber", "absentNumber", "paperWrongQuestionCount", "averageScore"),
				excludeId()))
			.first();

		if (stuCourseStatDoc == null || classCourseStatDoc == null) {
			// 没有统计数据，直接抛异常
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！examId：" + examId);
		}
		// 班级最后一名
		Document classLastStuCourseStatDoc = mongo.getCollection("examStudentCourseStat")
			.find(and(baseFilter, eq("classId", stuStudyGuideDTO.getClassId()), eq("resultStatus", ExamResultStatusEnum.NORMAL.getValue())))
			.projection(fields(include("classRanking"), excludeId()))
			.sort(Sorts.descending("classRanking"))
			.first();

		StuStudyGuideDetailVO vo = new StuStudyGuideDetailVO();
		vo.setExamId(examId);
		vo.setExamName(examInfoDTO.getExamName());
		vo.setScanTime(examInfoDTO.getScanTime());
		vo.setStudentId(studentId);
		vo.setStudentName(MapUtils.getString(stuCourseStatDoc, "studentName"));
		vo.setQuestionCount(MapUtils.getInteger(stuCourseStatDoc, "paperQuestionCount"));
		vo.setFullMark(MapUtils.getDouble(stuCourseStatDoc, "fullMark"));
		vo.setResultStatus(stuStudyGuideDTO.getResultStatus());
		vo.setClassTotalStudent(MapUtils.getInteger(classCourseStatDoc, "totalStudent"));
		vo.setClassParticipationNumber(MapUtils.getInteger(classCourseStatDoc, "participationNumber"));
		vo.setClassAbsentNumber(MapUtils.getInteger(classCourseStatDoc, "absentNumber"));
		vo.setStudentWrongQuestionCount(MapUtils.getInteger(stuCourseStatDoc, "paperWrongQuestionCount"));
		vo.setTotalScore(MapUtils.getDouble(stuCourseStatDoc, "totalScore"));
		vo.setScoreRate(MapUtils.getDouble(stuCourseStatDoc, "scoreRate"));
		vo.setClassRanking(MapUtils.getInteger(stuCourseStatDoc, "classRanking"));
		vo.setClassWrongQuestionCount(MapUtils.getDouble(classCourseStatDoc, "paperWrongQuestionCount"));
		vo.setClassAverageScore(MapUtils.getDouble(classCourseStatDoc, "averageScore"));
		vo.setClassScoreRate(vo.getClassAverageScore() / vo.getFullMark());
		// todo 作业的原卷图片不知道咋提供，如果成绩是补录的呢？
		vo.setAnswerCardList(Collections.emptyList());
		vo.setClassLastRanking(classLastStuCourseStatDoc == null ? 0 : MapUtils.getInteger(classLastStuCourseStatDoc, "classRanking"));
		vo.setScoreMode(answerCardServiceV3.getPaperScoreMissMode(examId, stuStudyGuideDTO.getPaperId()));
		vo.setCourseId(stuStudyGuideDTO.getCourseId());
		vo.setCourseName(stuStudyGuideDTO.getCourseName());
		return vo;
	}

	private List<StuStudyGuideVO> buildStuStudyGuideVOList(Long studentId, List<ExamInfoDTO> examList) {
		if (examList.isEmpty()) {
			return Collections.emptyList();
		}
		List<Long> examIds = examList.stream().map(ExamInfoDTO::getExamId).collect(Collectors.toList());
		List<StuStudyGuideDTO> stuStudyGuideList = studyGuideStudentManager.getStuStudyGuideListByQuery(examIds, studentId);
		if (stuStudyGuideList.isEmpty()) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业数据出现异常，请联系管理员处理！");
		}
		List<Bson> examFilter = stuStudyGuideList.stream()
			.map(x -> and(eq("examId", x.getExamId()), eq("courseId", x.getCourseId()), eq("paperId", x.getPaperId())))
			.collect(Collectors.toList());
		Map<Long, Document> examId2CourseStat = mongo.getCollection("examStudentCourseStat")
			.find(and(or(examFilter), eq("statId", 0), eq("studentId", studentId)))
			.projection(fields(include("examId", "totalScore", "fullMark", "scoreRate", "paperQuestionCount", "paperWrongQuestionCount"),
				excludeId()))
			.into(new ArrayList<>())
			.stream().collect(toMap(x -> MapUtils.getLong(x, "examId"), x -> x, (v1, v2) -> {
				throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！");
			}));
		Map<Long, StuStudyGuideDTO> examId2StuStudyGuide = stuStudyGuideList.stream()
			.collect(toMap(StuStudyGuideDTO::getExamId, x -> x, (v1, v2) -> {
				throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！");
			}));
		return examList.stream().map(x -> {
			long examId = x.getExamId();
			StuStudyGuideDTO stuStudyGuideDTO = Optional.ofNullable(examId2StuStudyGuide.get(examId))
				.orElseThrow(() -> new CommonException(ResponseStatusEnum.DATA_ERROR, "作业报告数据出现异常，请联系管理员处理！examId：" + examId));
			StuStudyGuideVO stuStudyGuideVO = new StuStudyGuideVO();
			stuStudyGuideVO.setExamId(examId);
			stuStudyGuideVO.setExamName(x.getExamName());
			stuStudyGuideVO.setScanTime(x.getScanTime());
			stuStudyGuideVO.setResultStatus(stuStudyGuideDTO.getResultStatus());
			stuStudyGuideVO.setScoreMode(answerCardServiceV3.getPaperScoreMissMode(examId, stuStudyGuideDTO.getPaperId()));
			stuStudyGuideVO.setCourseId(stuStudyGuideDTO.getCourseId());
			stuStudyGuideVO.setCourseName(stuStudyGuideDTO.getCourseName());
			Document courseStat = examId2CourseStat.get(examId);
			if (courseStat == null) {
				// 没查询到统计数据，则不填充学生得分、错题相关数据，试卷的试题数、满分数据从别处获取
				stuStudyGuideVO.setQuestionCount(PaperUtil.getPaperStructure(paperManager.getPaper(stuStudyGuideDTO.getPaperId())).size());
				stuStudyGuideVO.setFullMark(stuStudyGuideDTO.getFullMark());
				stuStudyGuideVO.setResultStatus(ExamResultStatusEnum.ABSENT.getCode());
				return stuStudyGuideVO;
			}
			stuStudyGuideVO.setQuestionCount(MapUtils.getInteger(courseStat, "paperQuestionCount"));
			stuStudyGuideVO.setFullMark(MapUtils.getDouble(courseStat, "fullMark"));
			stuStudyGuideVO.setStudentWrongQuestionCount(MapUtils.getInteger(courseStat, "paperWrongQuestionCount"));
			stuStudyGuideVO.setTotalScore(MapUtils.getDouble(courseStat, "totalScore"));
			stuStudyGuideVO.setScoreRate(MapUtils.getDouble(courseStat, "scoreRate"));
			return stuStudyGuideVO;
		}).collect(Collectors.toList());
	}

	/**
	 * 获取学生的单场作业试题详情 - 试卷大题结构
	 */
	public List<StuStudyGuideQuestionVO> getStuStudyGuideHomeworkQuestionList(StudyGuideStudentParam param) {
		param.verify();

		ExamInfoDTO examInfoDTO = studyGuideStudentManager.getExamByQuery(param);
		if (examInfoDTO == null) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "作业不存在或还未公布，无法查看！examId：" + param.getExamId());
		}
		long examId = param.getExamId();
		Long studentId = param.getStudentId();
		List<StuStudyGuideDTO> stuStudyGuideList = studyGuideStudentManager.getStuStudyGuideListByQuery(Collections.singletonList(examId),
			studentId);
		if (stuStudyGuideList.isEmpty()) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业数据出现异常，请联系管理员处理！examId：" + examId);
		}
		StuStudyGuideDTO stuStudyGuideDTO = stuStudyGuideList.get(0);
		long courseId = stuStudyGuideDTO.getCourseId();
		Bson baseFilter = and(eq("examId", examId), eq("statId", 0), eq("courseId", courseId),
			eq("paperId", stuStudyGuideDTO.getPaperId()));

		Document stuCourseStatDoc = mongo.getCollection("examStudentCourseStat")
			.find(and(baseFilter, eq("studentId", studentId)))
			.projection(fields(include("paperQuestions"), excludeId()))
			.first();

		if (stuCourseStatDoc == null) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "获取作业报告数据出现异常，请联系管理员处理！examId：" + examId);
		}
		List<Document> paperQuestions = MapUtil.getCast(stuCourseStatDoc, "paperQuestions");
		if (paperQuestions == null) {
			throw new CommonException(ResponseStatusEnum.DATA_ERROR, "试题数据为空！examId：" + examId);
		}

		// 获取试题详情
		List<ObjectId> questionIds = paperQuestions.stream()
				.map(i -> MapUtil.getString(i, "questionId"))
				.map(ObjectId::new)
				.collect(Collectors.toList());
		List<Document> questions = ownQuestionService.getQuestions(questionIds);
		Map<String, Document> questionMap = questions.stream()
				.collect(toMap(i -> MapUtil.getString(i, "_id"), Function.identity()));

		// 查询错题ID
		StudyGuideWrongItemGetParam studyGuideWrongItemGetParam = new StudyGuideWrongItemGetParam(studentId, courseId, examId);
		List<StudyGuideWrongItemDTO> studyGuideWrongItems = studyGuideWrongService.getStudyGuideWrongItem(studyGuideWrongItemGetParam);
		Map<String, Long> studyGuideWrongItemMap = studyGuideWrongItems.stream()
				.collect(toMap(StudyGuideWrongItemDTO::getQuestionId, StudyGuideWrongItemDTO::getStudyGuideWrongItemId));

		// 构造返回数据
		List<StuStudyGuideQuestionVO> result = new ArrayList<>(paperQuestions.size());
		for (Document paperQuestion : paperQuestions) {
			StuStudyGuideQuestionVO stuStudyGuideQuestionVO = new StuStudyGuideQuestionVO();
			String questionId = MapUtil.getString(paperQuestion, "questionId");
			stuStudyGuideQuestionVO.setQuestionId(questionId);
			stuStudyGuideQuestionVO.setQuestionScore(MapUtil.getDouble(paperQuestion, "scoreValue"));
			stuStudyGuideQuestionVO.setStudentScore(MapUtil.getDoubleNullable(paperQuestion, "finallyScore"));
			stuStudyGuideQuestionVO.setStudyGuideWrongItemId(studyGuideWrongItemMap.get(questionId));
			stuStudyGuideQuestionVO.setQuestion(questionMap.get(questionId));
			result.add(stuStudyGuideQuestionVO);
		}
		return result;
	}
}
