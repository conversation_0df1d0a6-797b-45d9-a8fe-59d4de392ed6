package com.dongni.exam.studyguide.controller;

import com.dongni.commons.entity.Response;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.studyguide.bean.param.StudyGuideWeeklyReportParam;
import com.dongni.exam.studyguide.service.StudyGuideWeeklyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2025/9/15
 * @description:
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/study/guide/homework/student/weekly/report")
public class StudyGuideWeeklyReportController {

	@Autowired
	private StudyGuideWeeklyReportService studyGuideWeeklyReportService;

	/**
	 * 获取最新的周报信息
	 */
	@GetMapping("/latest")
	public Response getStuStudyGuideWeeklyReportLatest(StudyGuideWeeklyReportParam param) {
		return new Response(studyGuideWeeklyReportService.getStuStudyGuideWeeklyReportLatest(param));
	}

	/**
	 * 获取周报列表
	 */
	@GetMapping("/list")
	public Response getStuStudyGuideWeeklyReportList(StudyGuideWeeklyReportParam param) {
		return new Response(studyGuideWeeklyReportService.getStuStudyGuideWeeklyReportList(param));
	}

	/**
	 * 获取周报详情
	 */
	@GetMapping("/detail")
	public Response getStuStudyGuideWeeklyReportDetail(StudyGuideWeeklyReportParam param) {
		return new Response(studyGuideWeeklyReportService.getStuStudyGuideWeeklyReportDetail(param));
	}
}
