package com.dongni.exam.studyguide.controller;

import com.dongni.commons.entity.Response;
import com.dongni.exam.config.ExamConfig;
import com.dongni.exam.studyguide.bean.param.StudyGuideStudentParam;
import com.dongni.exam.studyguide.service.StudyGuideStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: hzw
 * @date: 2025/9/10
 * @description:
 */
@RestController
@RequestMapping(ExamConfig.CONTEXT_PATH + "/study/guide/homework/student")
public class StudyGuideStudentController {

	@Autowired
	private StudyGuideStudentService studyGuideStudentService;

	/**
	 * 获取最近三场教辅作业信息
	 * @param param studentId
	 */
	@GetMapping("/latest")
	public Response getStuStudyGuideHomeworkLatest(StudyGuideStudentParam param) {
		return new Response(studyGuideStudentService.getStuStudyGuideHomeworkLatest(param));
	}

	/**
	 * 获取教辅作业列表信息(分页)
	 * @param param studentId currentIndex pageSize
	 */
	@GetMapping("/list")
	public Response getStuStudyGuideHomeworkList(StudyGuideStudentParam param) {
		return new Response(studyGuideStudentService.getStuStudyGuideHomeworkList(param));
	}

	/**
	 * 获取学生的单场作业报告详情
	 * @param param studentId examId
	 */
	@GetMapping("/detail")
	public Response getStuStudyGuideHomeworkDetail(StudyGuideStudentParam param) {
		return new Response(studyGuideStudentService.getStuStudyGuideHomeworkDetail(param));
	}

	/**
	 * 获取学生的单场作业试题详情 - 试卷大题结构
	 */
	@GetMapping("/question/list")
	public Response getStuStudyGuideHomeworkQuestionList(StudyGuideStudentParam param) {
		return new Response(studyGuideStudentService.getStuStudyGuideHomeworkQuestionList(param));
	}
}
