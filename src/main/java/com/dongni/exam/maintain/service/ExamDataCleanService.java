package com.dongni.exam.maintain.service;

import com.dongni.analysis.stat.service.ExamStatCleanService;
import com.dongni.basedata.export.todo.service.CommonTodoService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageCopy;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.redis.util.JedisUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.serivice.mark.IQsClientService;
import com.dongni.exam.common.mark.vo.QuestionStructureVO;
import com.dongni.exam.homework.service.HomeworkSubmitCompleteService;
import com.dongni.exam.homework.service.handle.HomeworkCleanHandle;
import com.dongni.exam.knowledge.service.KnowledgeGraspCleanService;
import com.dongni.exam.mark.manager.ICardReportItemManager;
import com.dongni.exam.mark.service.handle.ExamMarkCleanHandle;
import com.dongni.exam.mark.util.ExamMarkReadUtil;
import com.dongni.exam.plan.service.ExamSchoolPaperService;
import com.dongni.exam.plan.service.ExamService;
import com.dongni.exam.plan.service.NewExamWorkerService;
import com.dongni.newmark.manager.IExamStudentManager;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Heweipo on 2017/2/23.
 *
 * 考试数据清理，包括 Redis，mongodb，MySQL，OSS，应用服务器
 */
@Service
public class ExamDataCleanService{

    private static final Logger log = LogManager.getLogger(ExamDataCleanService.class);

    @Autowired
    private ExamRepository commonRepository;
    @Autowired
    private CommonTodoService todoTaskService;
    @Autowired
    private ExamDataCleanLogService examDataCleanLogService;
    @Autowired
    private ExamMarkCleanHandle examMarkCleanHandle;

    @Autowired
    private ExamStatCleanService examStatCleanService;
    @Autowired
    private KnowledgeGraspCleanService knowledgeGraspCleanService;
    @Autowired
    private HomeworkSubmitCompleteService homeworkSubmitCompleteService;
    @Autowired
    private HomeworkCleanHandle homeworkCleanHandle;
    @Autowired
    private ExamService examService;
    @Autowired
    private NewExamWorkerService newExamWorkerService;

    @Autowired
    private ICardReportItemManager cardReportItemManager;
    @Autowired
    private IQsClientService qsClientService;
    @Autowired
    private ExamSchoolPaperService examSchoolPaperService;
    @Autowired
    private IExamStudentManager examStudentManager;

    /**
     * 清理考试遗留的 Redis、MySQL、mongodb，OSS 数据
     *
     * @param params userId userName examId
     * @return 日志记录ID
     */
    @Transactional(transactionManager = ExamRepository.TRANSACTION,propagation = Propagation.REQUIRED)
    public boolean cleanAll(Map<String, Object> params) {

        // 参数校验
        Verify.of(params).isValidId("examId").verify();

        // 参数包装
        Map<String,Object> exam = commonRepository.selectOne("ExamDataCleanMapper.getExam",params);
        if(MapUtils.isEmpty(exam)){
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS,"考试不存在");
        }
        params.putAll(exam);

        // 解析参数
        checkAndParseParams(params);

        // 参数初始化
        Long start = System.currentTimeMillis();
        Map<String,Object> p = new HashMap<>(params);
        p.put("currentTime", DateUtil.getCurrentDateTime());

        try{
            cleanOss(params);
        }catch (Exception e){
            log.error("清理所有数据时调用清理OSS出现异常:{}",e.getMessage(),e);
            examDataCleanLogService.insertCleanDataLog(p, "oss",start,e);
            return false;
        }

        try{
            cleanRedis(params);
        }catch (Exception e){
            log.error("清理所有数据时调用清理Redis出现异常:{}",e.getMessage(),e);
            examDataCleanLogService.insertCleanDataLog(p, "redis",start,e);
            return false;
        }

        try{
            cleanMysql(params);
        }catch (Exception e){
            log.error("清理所有数据时调用清理MySQL出现异常:{}",e.getMessage(),e);
            examDataCleanLogService.insertCleanDataLog(p, "mysql",start,e);
            return false;
        }

        try{
            cleanMongo(params);
        }catch (Exception e){
            log.error("清理所有数据时调用清理Mongo出现异常:{}",e.getMessage(),e);
            examDataCleanLogService.insertCleanDataLog(p, "mongo",start,e);
            return false;
        }

        examDataCleanLogService.insertCleanDataLog(p, "all",start,null);
        return true;
    }

    /**
     * 清理考试遗留的Redis数据
     *
     * @param params userId userName
     * @return 日志记录ID
     */
    private String cleanRedis(Map<String, Object> params) {

        // 参数校验
        List<String> ids = checkAndParseParams(params);

        if(CollectionUtils.isEmpty(ids)){
            log.info("考试{}的t_exam_paper 已经被清理，不能执行，程序退出",params.get("examId"));
            return "";
        }

        params.put("currentTime",DateUtil.getCurrentDateTime());
        Long start = System.currentTimeMillis();

        // 如果是线上作业，需把线上作业的redis清理
        if (examService.isHomework(params)) {
            homeworkSubmitCompleteService.updateSubmitItemFromRedis(params);
            homeworkCleanHandle.addHomeworkCleanQueue(params);
        }

        // 查询类型
        int correctMode = Integer.parseInt(params.get("correctMode").toString());
        boolean isReadByClass = correctMode == DictUtil.getDictValue("correctMode","readByClass");

        // 备份数据
        //commonRepository.insert("ExamDataCleanMapper.backupExamResultItem",params);

        // 执行清理
        if(isReadByClass){
            List<Long> cids = commonRepository.selectList("ExamDataCleanMapper.getExamClassId",params);
            for (long id : cids){
                processReadRecord(Long.valueOf(ids.get(0)),id);
            }
        }else {
            for (String id : ids){
                processReadRecord(Long.valueOf(id),null);
            }
        }

        // item 状态更新
        params.put("currentTime",DateUtil.getCurrentDateTime());
        commonRepository.update("ExamDataCleanMapper.updateExamForceCompleteItem",params);

        // 记录日志
        return examDataCleanLogService.insertCleanDataLog(params, "redis",start,null);
    }

    /**
     * 强制完成时把Redis记录插入到 mysql
     * @param examPaperId 考考试试卷ID
     */
    private void processReadRecord(Long examPaperId, Long classId) {

        String cacheId = examPaperId.toString() + (classId == null ? "" : ":"+classId);
        log.info("把Redis的数据{}插入到mysql",cacheId);

        // Redis 数据获取
        Long start = System.currentTimeMillis();
        String recordDoneKey = JedisUtil.getKey("readRecordDone", cacheId);
        List<Object> ls = ExamMarkReadUtil.getCacheKeyAllDatas(recordDoneKey);
        if (ls == null || CollectionUtils.isEmpty(ls)) {
            log.info("考试科目{}插入批改记录时，从Redis里面没有获取到任何数据，recordDoneKey：{}" ,cacheId,recordDoneKey);
            return;
        }

        ls.sort(Comparator.comparing(a-> Integer.parseInt(((Map)a).get("choiceStatus").toString())));
        log.info("考试科目{}插入批改记录时，从redis获取record总数{}共耗时:{}",cacheId,ls.size(),System.currentTimeMillis() - start);

        // mysql 数据插入
        start = System.currentTimeMillis();
        int total = commonRepository.batchInsert("ExamDataCleanMapper.insertPaperReadRecordBatch", ls);
        log.info("考试科目{}插入批改记录时,插入总数{}共耗时:{}",cacheId,total,System.currentTimeMillis() - start);

        // 遍历每一个科目，清理 redis 阅卷
        JedisTemplate.execute(jedis -> {
            jedis.sadd(JedisUtil.getKey("examPaperDone"),cacheId);
            log.info("成功加入 examPaperId:{}到定时清理任务中",cacheId);
            return null;
        });
    }


    /**
     * 清理考试遗留的MySQL数据，通过参数 examPaperId
     *
     * @param params userId userName examPaperId
     * @return 日志记录ID
     */
    public String cleanMysql(Map<String, Object> params) {
        // 参数校验
        List<String> ids = checkAndParseParams(params);
        params.put("currentTime",DateUtil.getCurrentDateTime());

        // 参数初始化
        Long start = System.currentTimeMillis();

        // 试卷状态回滚
        if(!ObjectUtil.isValidUser(params)
                || !ObjectUtil.isValidId(params.get("examId"))){
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"无法删除考试数据");
        }

        String examId = params.get("examId").toString();

        // 知识点清理
        try{
            knowledgeGraspCleanService.cleanByExam(MapUtil.of(
                    "examId", examId,
                    "userId", params.get("userId"),
                    "userName", params.get("userName"),
                    "userType", params.get("userType")
            ));
            log.error("删除考试调用知识点清理完成, examId: {}", examId);
        }catch (Exception e){
            log.error("删除考试调用知识点清理异常, examId: {}", examId, e);
        }

        // 删除数据
        deleteMysqlExamData(Long.valueOf(examId));

        log.info("成功删除考试MySQL所有考试数据. examId : {}", examId);

        // 记录日志
        return examDataCleanLogService.insertCleanDataLog(params, "mysql",start,null);
    }

    /**
     * 清理考试遗留的Mongo数据，通过参数 examPaperId
     *
     * @param params userId userName examPaperId
     * @return 日志记录ID
     */
    public String cleanMongo(Map<String, Object> params) {

        // 参数校验
        List<String> ids = checkAndParseParams(params);

        // 参数初始化
        Long start = System.currentTimeMillis();
        Map<String,Object> p = new HashMap<>(params);
        p.put("currentTime",DateUtil.getCurrentDateTime());

        // 清理待办
        Document query = new Document();
        for (String id : ids){
            query.put("displayContent.examPaperId",Long.valueOf(id));
            long count = todoTaskService.deleteAllTodoTask(query);
            log.info("成功删除考试 mongodb 数据. examPaperId : " + id+", 清理总数为 :" + count);
        }

        query = new Document();
        query.put("displayContent.examId",Long.valueOf(params.get("examId").toString()));
        long count = todoTaskService.deleteAllTodoTask(query);
        log.info("成功删除考试mongodb数据. examId : " + params.get("examId")+", 清理总数为 :" + count);

        // 清理报告
        examStatCleanService.forceDeleteExamStat(params);

        return examDataCleanLogService.insertCleanDataLog(p, "mongo", start,null);
    }

    /**
     * 清理考试遗留的Oss数据，通过参数 examPaperId
     *
     * @param params userId userName examPaperId
     * @return 日志记录ID
     */
    private void cleanOss(Map<String, Object> params) {
        // 参数校验
        List<String> ids = checkAndParseParams(params);
        if(CollectionUtils.isEmpty(ids)){
            log.info("考试{}的t_exam_paper 已经被清理，不能执行，程序退出",params.get("examId"));
            return;
        }

        // 参数初始化
        long start = System.currentTimeMillis();
        params.put("currentTime",DateUtil.getCurrentDateTime());

        // 如果是线上作业，需把线上作业的redis清理
        if (examService.isHomework(params)) {
            // 考虑历史数据，没有统一前缀
            List<String> saveFileUrls = commonRepository.selectList("ExamDataCleanMapper.getSaveFileUrls",ids);
            if(CollectionUtils.isNotEmpty(saveFileUrls)){
                FileStorageTemplate.batchCopy(lst->{
                     for (String url : saveFileUrls){
                         lst.add(new FileStorageCopy(url, "expire/"+url));
                     }
                });
                FileStorageTemplate.batchDelete(saveFileUrls);
            }
        }

        List<String> ps = commonRepository.selectList("ExamDataCleanMapper.getAnswerCard", ids);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(ps)) {
            log.info("考试OSS没有数据清理。");
            examDataCleanLogService.insertCleanDataLog(params, "oss", start, null);
        }

        // 数据清理
        for (String answerCardPath : ps) {
            // 切割后的文件
            // todo 这里的文件需要迁移，先不做物理删除 2020年3月19日 @learnmore
//            if (ObjectUtil.isBlank(answerCardPath)) continue;
//            FileStorageTemplate.deleteByPrefix(answerCardPath);
//
//            // 答题卡压缩包
//            String[] ss = answerCardPath.split("/");
//            String s = ss[0] + "/" + ss[1] + "/temp/" + ss[2] + "/" + ss[3] + "/" + ss[5];
//            FileStorageTemplate.deleteByPrefix(s);

//            log.info("成功删除考试OSS数据. 路径前缀 : " + s);
        }

        examDataCleanLogService.insertCleanDataLog(params, "oss", start, null);
    }

    /**
     * 清理掉 mysql exam 相关的所有数据
     * @param examId 考试ID
     */
    @Transactional(ExamRepository.TRANSACTION)
    public void deleteMysqlExamData(Long examId){

        List<Map<String, Object>> examPapers = commonRepository.selectList("ExamPaperMapper.getExamPaper", examId);

        List<QuestionStructureVO> allQuestionStructures = new ArrayList<>();
        for (Map<String, Object> examPaper : examPapers) {
            List<QuestionStructureVO> questionStructure = qsClientService.listQuestionStructure(MapUtil.getLong(examPaper, "paperId"));
            allQuestionStructures.addAll(questionStructure);
        }

        commonRepository.delete("ExamDataDeleteMapper.deleteExam",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamCourse",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamPaper",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchool",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClass",examId);

        newExamWorkerService.deleteAllByExamId(examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamPermission",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamUploader",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamUploaderFile",examId);

        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolPaper",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClassPaper",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemComment",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemMistake",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemReread",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamTag",examId);

        commonRepository.delete("ExamDataDeleteMapper.deleteExamStat",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteClassStatCompare",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamResultEvaluation",examId);

        // 删除wrongItem,数据量太多改成每次删除一道题
        if (CollectionUtils.isNotEmpty(allQuestionStructures)) {
            for (QuestionStructureVO questionStructure : allQuestionStructures) {
                Map<String, Object> deleteWrongItemParams = MapUtil.of(
                        "examId", examId,
                        "paperId", questionStructure.getPaperId(),
                        "questionNumber", questionStructure.getQuestionNumber());
                commonRepository.delete("ExamDataDeleteMapper.deleteWrongItem", deleteWrongItemParams);
            }
        }

        commonRepository.delete("ExamDataDeleteMapper.deleteWrongClass",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteWrongGrade",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteStudyGuideWrongItem", examId);

        cardReportItemManager.delByExamId(examId);

        // 成绩导入模式
        commonRepository.delete("ExamDataDeleteMapper.deleteExamPaperScoreImport", examId);
        
        // 大表删除放到最后，避免事务回滚耗时太长
        commonRepository.delete("ExamDataDeleteMapper.deleteExamStudent",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamResult",examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteExamResultTemp", examId);
        commonRepository.delete("ExamDataDeleteMapper.deleteAnswerCard",examId);
        commonRepository.delete("ExamDataDeleteMapper.deletePaperReadAndRecord",examId);
        examPapers.forEach(examPaper -> {
            int count;
            do {
                count = commonRepository.delete("ExamDataDeleteMapper.deleteExamItem", examPaper);
            } while (count > 0);
        });

        // todo 需要删除学生的知识点统计

    }

    /**
     * 清理考试学校遗留的MySQL数据，通过参数 examId schoolIds
     */
    public void cleanMysqlBySchool(Map<String, Object> params) {
        String examId = params.get("examId").toString();
        // 知识点清理 删除学校后必须要重新执行统计才行，在执行统计时会去重新计算知识点相关数据，在这里就全删了
        try{
            knowledgeGraspCleanService.cleanByExam(MapUtil.of(
              "examId", examId,
              "userId", params.get("userId"),
              "userName", params.get("userName"),
              "userType", params.get("userType")
            ));
            log.info("删除考试调用知识点清理完成, examId: {}", examId);
        }catch (Exception e){
            log.error("删除考试调用知识点清理异常, examId: {}", examId, e);
        }

        // 删除数据
        deleteMysqlExamSchoolData(params);
    }

    /**
     * 清理掉 mysql 考试学校相关的所有数据
     */
    private void deleteMysqlExamSchoolData(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isNotBlank("schoolIds")
          .isNotBlank("keepSchoolIds")
          .verify();

        // t_exam_teacher courseId = 0
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolClassHeader", params);
        // t_exam_teacher courseId != 0
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolClassTeacher", params);
        // t_exam_area
        commonRepository.delete("ExamDataDeleteMapper.deleteExamArea", params);
        // t_exam_school
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolByIds", params);
        // t_exam_class
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClassByIds", params);

        // t_exam_worker [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamWorkerByIds", params);
        // t_exam_uploader t_exam_uploader_file [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamUploaderByIds", params);

        // t_exam_school_paper [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolPaperByIds", params);
        // t_exam_class_paper [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClassPaperByIds", params);

        // t_exam_item_comment [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemCommentByIds", params);
        // t_exam_item_mistake [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemMistakeByIds", params);
        // t_exam_item_reread [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemRereadByIds", params);
        // t_exam_tag  t_exam_student_tag
        commonRepository.delete("ExamDataDeleteMapper.deleteExamTagByIds", params);

        // t_school_exam_stat
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolStatByIds", params);
        //这里不管是删除学校还是删除班级，都把这个报告从所有加了这个对比的地方删除
        // t_class_stat_compare
        commonRepository.delete("ExamDataDeleteMapper.deleteClassStatCompareByIds", params);

        //错题的数据不管是删除学校还是删除班级，都把这些学校相关的数据全部删除，在统计的时候再生成
        List<Map<String, Object>> examSchoolPaperList = examSchoolPaperService.getExamSchoolPaperList(params);
        Long paperId = MapUtil.getLongNullable(params, "paperId");
        List<Long> paperIds = paperId != null ? Collections.singletonList(paperId) :
                examSchoolPaperList.stream().map(i -> MapUtil.getLong(i, "paperId")).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(paperIds)) {
            Map<String, Object> delWrongItemParams = new HashMap<>(params);
            delWrongItemParams.put("paperIds", paperIds);
            // t_wrong_item t_wrong_tag_item
            commonRepository.delete("ExamDataDeleteMapper.deleteWrongItemByIds", delWrongItemParams);
        }
        // t_wrong_class_item
        commonRepository.delete("ExamDataDeleteMapper.deleteWrongClass", params);
        // t_wrong_grade_item
        commonRepository.delete("ExamDataDeleteMapper.deleteWrongGrade", params);

        // t_answer_card [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteAnswerCardByIds", params);
        // t_paper_mark_record [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deletePaperReadAndRecordByIds", params);
        // t_exam_item [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemByIds", params);
        // t_exam_result t_exam_result_temp [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamStudentAndResultByIds", params);
        // t_exam_student
        List<Long> schoolIds = MapUtil.getListLong(params, "schoolIds");
        List<Long> keepSchoolIds = MapUtil.getListLong(params, "keepSchoolIds");
        List<Long> deleteSchoolIds = schoolIds.stream().filter(x -> !keepSchoolIds.contains(x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteSchoolIds)) {
            examStudentManager.deleteByExamAndSchoolAndClass(
                    MapUtil.getLong(params, "examId"),
                    deleteSchoolIds,
                    null
            );
        }
    }


    /**
     * 清理考试班级遗留的MySQL数据，通过参数 examId schoolIds schoolId classIds
     */
    public void cleanMysqlBySchoolClass(Map<String, Object> params) {
        String examId = params.get("examId").toString();
        // 知识点清理 删除班级后必须要重新执行统计才行，在执行统计时会去重新计算知识点相关数据，在这里就全删了
        try{
            knowledgeGraspCleanService.cleanByExam(MapUtil.of(
              "examId", examId,
              "userId", params.get("userId"),
              "userName", params.get("userName"),
              "userType", params.get("userType")
            ));
            log.info("删除考试调用知识点清理完成, examId: {}", examId);
        }catch (Exception e){
            log.error("删除考试调用知识点清理异常, examId: {}", examId, e);
        }

        // 删除数据
        deleteMysqlExamSchoolClassData(params);
    }

    /**
     * 清理掉 mysql 考试班级相关的所有数据
     * @param params examId schoolIds schoolId classIds
     */
    public void deleteMysqlExamSchoolClassData(Map<String, Object> params) {
        Verify.of(params)
          .isValidId("examId")
          .isNotBlank("schoolIds")
          .isValidId("schoolId")
          .isNotBlank("classIds")
          .isNotBlank("keepClassIds")
          .verify();

        // t_exam_teacher course = 0
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolClassHeader", params);
        // t_exam_teacher course != 0
        commonRepository.delete("ExamDataDeleteMapper.deleteExamSchoolClassTeacher", params);
        // t_exam_class
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClassByIds", params);
        // t_exam_worker [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamWorkerByIds", params);

        // t_answer_card [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteAnswerCardByIds", params);
        // t_exam_uploader_class [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamUploaderClassByIds", params);
        // t_exam_uploader t_exam_uploader_file [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamUploaderByUploaderClass", params);

        // t_exam_item_comment [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemCommentByIds", params);
        // t_exam_item_mistake [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemMistakeByIds", params);
        // t_exam_item_reread [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemRereadByIds", params);
        // t_exam_student_tag
        commonRepository.delete("ExamDataDeleteMapper.deleteExamTagByIds", params);

        //这里不管是删除学校还是删除班级，都把这个报告从所有加了这个对比的地方删除
        // t_class_stat_compare
        commonRepository.delete("ExamDataDeleteMapper.deleteClassStatCompareByIds", params);

        //错题的数据不管是删除学校还是删除班级，都把这些学校相关的数据全部删除，在统计的时候再生成
        List<Map<String, Object>> examSchoolPaperList = examSchoolPaperService.getExamSchoolPaperList(params);
        Long paperId = MapUtil.getLongNullable(params, "paperId");
        List<Long> paperIds = paperId != null ? Collections.singletonList(paperId) :
                examSchoolPaperList.stream().map(i -> MapUtil.getLong(i, "paperId")).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(paperIds)) {
            Map<String, Object> delWrongItemParams = new HashMap<>(params);
            delWrongItemParams.put("paperIds", paperIds);
            // t_wrong_tag_item t_wrong_item
            commonRepository.delete("ExamDataDeleteMapper.deleteWrongItemByIds", delWrongItemParams);
        }
        // t_wrong_class_item
        commonRepository.delete("ExamDataDeleteMapper.deleteWrongClass", params);
        // t_wrong_grade_item
        commonRepository.delete("ExamDataDeleteMapper.deleteWrongGrade", params);

        // t_paper_mark_record [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deletePaperReadAndRecordByIds", params);
        // t_exam_item [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamItemByIds", params);
        // t_exam_result t_exam_result_temp [paperId]
        commonRepository.delete("ExamDataDeleteMapper.deleteExamStudentAndResultByIds", params);
        // t_exam_student
        List<Long> classIds = MapUtil.getListLong(params, "classIds");
        List<Long> keepClassIds = MapUtil.getListLong(params, "keepClassIds");
        List<Long> deleteClassIds = classIds.stream().filter(x -> !keepClassIds.contains(x)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteClassIds)) {
            examStudentManager.deleteByExamAndSchoolAndClass(
                    MapUtil.getLong(params, "examId"),
                    MapUtil.getListLong(params, "schoolIds"),
                    deleteClassIds
            );
        }

        // t_exam_class_paper
        commonRepository.delete("ExamDataDeleteMapper.deleteExamClassPaperByResult", params);
    }

    /**
     * 校验参数以及解析参数
     * @param params userId userName examPaperId|endDate,startDate
     * @return examPaperId 集合
     */
    private List<String> checkAndParseParams(Map<String,Object> params){

        if(params.get("ids") != null){
            return (List<String>) params.get("ids");
        }

        List<String> ids = new ArrayList<>();
        if(!ObjectUtil.isBlank(params.get("examPaperId"))){
            // 前端数据
            ids.addAll(Arrays.asList(params.remove("examPaperId").toString().split(",")));
        }else if(!ObjectUtil.isBlank(params.get("examId"))){
            // 数据查询
            ids.addAll(commonRepository.selectList("ExamDataCleanMapper.getExamPaperId",params));
        }else{
            // 校验失败
            throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR,"考试科目ID不存在");
        }

        params.put("ids",ids);

        return ids;
    }


}
