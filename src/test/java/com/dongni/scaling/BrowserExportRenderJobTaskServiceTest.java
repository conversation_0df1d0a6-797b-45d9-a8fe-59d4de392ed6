package com.dongni.scaling;

import com.hqjl.ess.JobConfig;
import com.hqjl.ess.JobNodesState;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/7/24 周三 下午 02:30
 * @Version 1.0.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class BrowserExportRenderJobTaskServiceTest {
    @Autowired
    private BrowserExportRenderJobTaskService browserExportRenderJobTaskService;

    private JobConfig jobConfig;
    private JobNodesState jobNodesState;

    @Before
    public void init() {
        jobConfig = new JobConfig();

        jobNodesState = new JobNodesState();
        jobNodesState.setCurrentNodeNum(2);
    }

    @Test
    public void testEstimateEcsCount() {
        int count = browserExportRenderJobTaskService.estimateEcsCount(jobConfig, jobNodesState);
        System.out.println(count);
    }
}