package com.dongni.tiku.entrust;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.entrust.service.assist.EntrustAssistService;
import com.dongni.tiku.entrust.service.dongni.EntrustStatusService;
import org.apache.commons.collections4.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/06/08 <br/>
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EntrustPushTo17Test {
    
    @Autowired
    private EntrustStatusService entrustStatusService;
    
    @Autowired
    private EntrustAssistService entrustAssistService;
    
    @Test
    public void status() {
        Map<String, Object> params = MapUtil.of(
                "entrustId", 4059,
                "userId", 1,
                "userName", "手动调用",
                "userType", 7
        );
        // 更新状态
        Map<String, Object> statusChangeResult = entrustStatusService.toYiqiProofreading(params);
        if (MapUtils.isNotEmpty(statusChangeResult)) {
            Map<String, Object> logInfo = entrustAssistService.insertLogAndTimeout(statusChangeResult,
                    EntrustAssistService.Type.ENTRUST_STATUS_CHANGE, "进入校对中");
        }
    }
}
