package com.dongni.tiku.wrong.book.paper;

import cn.hutool.json.JSONUtil;
import com.dongni.exam.plan.service.ExamSchoolPaperService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>错题本试卷测试类</p>
 *
 * <AUTHOR>
 * @className WrongBookPaperTest
 * @since 2023/8/23 11:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WrongBookPaperTest {

    @Autowired
    private ExamSchoolPaperService examSchoolPaperService;

    @Test
    public void getExamSchoolPaperByExamIds() {
        Map<String, Object> params = new HashMap<>(4);
        params.put("schoolId", 548);
        params.put("examIdList", Lists.newArrayList(6162505L, 6162552L));

        List<Map<String, Object>> examSchoolPaperList = examSchoolPaperService.getExamSchoolPaperListByExamIds(params);
        System.out.println("试卷列表" + JSONUtil.toJsonStr(examSchoolPaperList));
    }

}
