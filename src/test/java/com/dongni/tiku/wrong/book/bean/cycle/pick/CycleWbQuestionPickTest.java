package com.dongni.tiku.wrong.book.bean.cycle.pick;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.similar.enumeration.Difficulty;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaper;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaperQuestionRate;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbQuestion;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbQuestionQuantity;
import com.dongni.tiku.wrong.book.bean.cycle.knowledge.CycleWbGraspHelper;
import org.junit.Test;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * @date 2023/07/10
 */
public class CycleWbQuestionPickTest {
    
    String studentOrgName = "studentId";
    long studentOrgId = 101;
    
    String classOrgName = "classId";
    long classOrgId = 11;
    
    CycleWbGraspHelper studentGraspHelper = new CycleWbGraspHelper();
    CycleWbGraspHelper classGraspHelper = new CycleWbGraspHelper();
    
    Map<Long, CycleWbExamPaper> examId2ExamPaper = new HashMap<>();
    Map<Long, CycleWbQuestion> questionId2Question = new HashMap<>();
    public CycleWbQuestionPickTest() {
        examId2ExamPaper.put(101L, examPaper101);
        examId2ExamPaper.put(102L, examPaper102);
        examId2ExamPaper.put(103L, examPaper103);
        examId2ExamPaper.put(104L, examPaper104);
        examId2ExamPaper.put(105L, examPaper105);
        examId2ExamPaper.put(106L, examPaper106);
        examId2ExamPaper.put(107L, examPaper107);
        examId2ExamPaper.put(108L, examPaper108);
        
        questionId2Question.put(1000101L, question1000101);
        questionId2Question.put(1000102L, question1000102);
        questionId2Question.put(1000103L, question1000103);
        questionId2Question.put(1000104L, question1000104);
        questionId2Question.put(1000105L, question1000105);
        questionId2Question.put(1000201L, question1000201);
        questionId2Question.put(1000202L, question1000202);
        questionId2Question.put(1000203L, question1000203);
        questionId2Question.put(1000204L, question1000204);
        questionId2Question.put(1000205L, question1000205);
        questionId2Question.put(1000301L, question1000301);
        questionId2Question.put(1000302L, question1000302);
        questionId2Question.put(1000303L, question1000303);
        questionId2Question.put(1000304L, question1000304);
        questionId2Question.put(1000305L, question1000305);
        questionId2Question.put(1000401L, question1000401);
        questionId2Question.put(1000402L, question1000402);
        questionId2Question.put(1000403L, question1000403);
        questionId2Question.put(1000404L, question1000404);
        questionId2Question.put(1000405L, question1000405);
        questionId2Question.put(1000501L, question1000501);
        questionId2Question.put(1000502L, question1000502);
        questionId2Question.put(1000503L, question1000503);
        questionId2Question.put(1000504L, question1000504);
        questionId2Question.put(1000505L, question1000505);
        questionId2Question.put(1000601L, question1000601);
        questionId2Question.put(1000602L, question1000602);
        questionId2Question.put(1000603L, question1000603);
        questionId2Question.put(1000604L, question1000604);
        questionId2Question.put(1000605L, question1000605);
        
    }
    
    
    @Test
    public void test() {
        
        add(101, 1000101, 0.9, 0.70  );
        add(101, 1000102, 0.9, 0.45  );
        add(101, 1000103, 0.9, 0.60  );
        add(101, 1000104, 0.9, 1.00  );
        add(101, 1000105, 0.9, 0.99  );
        add(102, 1000201, 0.9, 0.25  );
        add(102, 1000202, 0.9, 0.80  );
        add(102, 1000203, 0.9, 0.58  );
        add(102, 1000204, 0.9, 0.35  );
        add(102, 1000205, 0.9, 0.74  );
        add(103, 1000301, 0.9, 0.59  );
        add(103, 1000302, 0.9, 0.16  );
        add(103, 1000303, 0.9, 0.67  );
        add(103, 1000304, 0.9, 0.82  );
        add(103, 1000305, 0.9, 0.88  );
        add(104, 1000401, 0.9, 0.54  );
        add(104, 1000402, 0.9, 0.06  );
        add(104, 1000403, 0.9, 0.67  );
        add(104, 1000404, 0.9, 0.54  );
        add(104, 1000405, 0.9, 0.06  );
        add(105, 1000501, 0.9, 0.67  );
        add(105, 1000502, 0.9, 0.54  );
        add(105, 1000503, 0.9, 0.72  );
        add(105, 1000504, 0.9, 0.91  );
        add(105, 1000505, 0.9, 1.00  );
        add(106, 1000601, 0.9, 0.53  );
        add(106, 1000602, 0.9, 0.53  );
        add(106, 1000603, 0.9, 0.51  );
        add(106, 1000604, 0.9, 0.13  );
        add(106, 1000605, 0.9, 0.07  );
        add(107, 1000101, 0.9, 0.79  );
        add(107, 1000202, 0.9, 0.66  );
        add(107, 1000303, 0.9, 0.54  );
        add(107, 1000404, 0.9, 0.89  );
        add(107, 1000505, 0.9, 0.70  );
        add(108, 1000101, 0.9, null  );
        add(108, 1000202, 0.9, null  );
        add(108, 1000303, 0.9, null  );
        add(108, 1000404, 0.9, null  );
        add(108, 1000505, 0.9, null  );
        
        
        
        
        CycleWbQuestionQuantity cycleWbQuestionQuantity = new CycleWbQuestionQuantity(3)
                .setWrongQuantity(Difficulty.EASY, 2)
                .setWrongQuantity(Difficulty.MIDDLE, 2)
                .setWrongQuantity(Difficulty.DIFFICULT, 2)
                .setSimilarQuantity(Difficulty.EASY, 2)
                .setSimilarQuantity(Difficulty.MIDDLE, 6)
                .setSimilarQuantity(Difficulty.DIFFICULT, 6)
                ;
        List<CycleWbSourceCandidate> sourceCandidateList = new ArrayList<>();
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_01", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_02", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_03", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_04", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_05", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_06", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_07", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_08", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_09", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_10", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_11", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_12", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_13", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_14", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_15", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_16", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_17", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_18", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_19", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_20", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_21", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_22", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_23", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_24", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_25", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_26", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_27", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_28", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_29", Difficulty.EASY)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_EASY_30", Difficulty.EASY)));
        
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_01", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_02", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_03", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_04", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_05", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_06", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_07", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_08", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_09", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_10", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_11", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_12", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_13", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_14", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_15", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_16", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_17", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_18", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_19", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_20", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_21", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_22", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_23", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_24", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_25", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_26", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_27", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_28", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_29", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000502, CycleWbQuestion.ofCandidate("类题1000502_MIDDLE_30", Difficulty.MIDDLE)));
        
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_01", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_02", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_03", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_04", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_05", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_06", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_07", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_08", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_09", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_10", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_11", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_12", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_13", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_14", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_15", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_16", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_17", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_18", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_19", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_20", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_21", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_22", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_23", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_24", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_25", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_26", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_27", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_28", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_29", Difficulty.MIDDLE)));
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000205, CycleWbQuestion.ofCandidate("类题1000205_MIDDLE_30", Difficulty.MIDDLE)));
        
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000401, CycleWbQuestion.ofCandidate("类题1000401_DIFFICULT_01", Difficulty.DIFFICULT)));
        
        sourceCandidateList.add(new CycleWbSourceCandidate(question1000104, CycleWbQuestion.ofCandidate("类题1000104_DIFFICULT_01", Difficulty.DIFFICULT)));
        
        CycleWbQuestionPick questionPick = new CycleWbQuestionPick(
                cycleWbQuestionQuantity,
                studentGraspHelper,
                classGraspHelper,
                sourceCandidateList,
                new HashSet<>(),
                true
        )
                .debugEnabled()
                .pick();
        
        System.out.println(" ================================= 挑错题");
        List<Map<Difficulty, List<CycleWbPickedQuestion>>> pickPeriodsWrongQuestionList = questionPick.getPickPeriodsWrongQuestionList();
        System.out.println(JSONUtil.toJson(pickPeriodsWrongQuestionList));
        System.out.println(" ================================= 挑类题");
        List<Map<Difficulty, Set<CycleWbPickedQuestion>>> pickPeriodsStudentSimilarQuestionList = questionPick.getPickPeriodsStudentSimilarQuestionSet();
        System.out.println(JSONUtil.toJson(pickPeriodsStudentSimilarQuestionList));
        System.out.println(" ================================= 挑班级共性错题的类题");
        List<Map<Difficulty, Set<CycleWbPickedQuestion>>> pickPeriodsClassSimilarQuestionList = questionPick.getPickPeriodsClassSimilarQuestionSet();
        System.out.println(JSONUtil.toJson(pickPeriodsClassSimilarQuestionList));
    }
    
    private void add(long examId, long questionId, double classRate, Double studentRate) {
        CycleWbExamPaper cycleWbExamPaper = examId2ExamPaper.get(examId);
        CycleWbQuestion question = questionId2Question.get(questionId);
        classGraspHelper.addRate(CycleWbExamPaperQuestionRate.of(cycleWbExamPaper, question, classOrgName, classOrgId, classRate));
        if (studentRate != null) {
            studentGraspHelper.addRate(CycleWbExamPaperQuestionRate.of(cycleWbExamPaper, question, studentOrgName, studentOrgId, studentRate));
        }
    }
    
    private Set<String> knowledge(String... knowledge) {
        if (knowledge == null || knowledge.length == 0) {
            return new HashSet<>();
        }
        return Stream.of(knowledge).collect(Collectors.toSet());
    }
    
    private final CycleWbExamPaper examPaper101 = CycleWbExamPaper.of(101, 100001, 10001);
    private final CycleWbExamPaper examPaper102 = CycleWbExamPaper.of(102, 100002, 10002);
    private final CycleWbExamPaper examPaper103 = CycleWbExamPaper.of(103, 100003, 10003);
    private final CycleWbExamPaper examPaper104 = CycleWbExamPaper.of(104, 100004, 10004);
    private final CycleWbExamPaper examPaper105 = CycleWbExamPaper.of(105, 100005, 10005);
    private final CycleWbExamPaper examPaper106 = CycleWbExamPaper.of(106, 100006, 10006);
    private final CycleWbExamPaper examPaper107 = CycleWbExamPaper.of(107, 100005, 10007);
    private final CycleWbExamPaper examPaper108 = CycleWbExamPaper.of(108, 100008, 10008);
    
    
    private final CycleWbQuestion question1000101 = CycleWbQuestion.of("试题1000101", 0.75, knowledge("知识点1", "知识点2"));
    private final CycleWbQuestion question1000102 = CycleWbQuestion.of("试题1000102", 0.30, knowledge("知识点3", "知识点4", "知识点5"));
    private final CycleWbQuestion question1000103 = CycleWbQuestion.of("试题1000103", 0.66, knowledge("知识点6"));
    private final CycleWbQuestion question1000104 = CycleWbQuestion.of("试题1000104", 0.30, knowledge("知识点7"));
    private final CycleWbQuestion question1000105 = CycleWbQuestion.of("试题1000105", 0.99, knowledge("知识点1", "知识点4"));
    private final CycleWbQuestion question1000201 = CycleWbQuestion.of("试题1000201", 0.85, knowledge("知识点1", "知识点3"));
    private final CycleWbQuestion question1000202 = CycleWbQuestion.of("试题1000202", 0.54, knowledge("知识点9", "知识点6"));
    private final CycleWbQuestion question1000203 = CycleWbQuestion.of("试题1000203", 0.92, knowledge("知识点4", "知识点7"));
    private final CycleWbQuestion question1000204 = CycleWbQuestion.of("试题1000204", 0.22, knowledge("知识点6", "知识点8"));
    private final CycleWbQuestion question1000205 = CycleWbQuestion.of("试题1000205", 0.70, knowledge("知识点5", "知识点2"));
    private final CycleWbQuestion question1000301 = CycleWbQuestion.of("试题1000301", 0.88, knowledge("知识点5", "知识点1"));
    private final CycleWbQuestion question1000302 = CycleWbQuestion.of("试题1000302", 0.54, knowledge("知识点8", "知识点5"));
    private final CycleWbQuestion question1000303 = CycleWbQuestion.of("试题1000303", 0.92, knowledge("知识点3", "知识点6"));
    private final CycleWbQuestion question1000304 = CycleWbQuestion.of("试题1000304", 0.22, knowledge("知识点9", "知识点7"));
    private final CycleWbQuestion question1000305 = CycleWbQuestion.of("试题1000305", 0.70, knowledge("知识点9", "知识点2"));
    private final CycleWbQuestion question1000401 = CycleWbQuestion.of("试题1000401", 0.90, knowledge("知识点1", "知识点2"));
    private final CycleWbQuestion question1000402 = CycleWbQuestion.of("试题1000402", 0.98, knowledge("知识点8", "知识点6"));
    private final CycleWbQuestion question1000403 = CycleWbQuestion.of("试题1000403", 0.83, knowledge("知识点2", "知识点6"));
    private final CycleWbQuestion question1000404 = CycleWbQuestion.of("试题1000404", 0.92, knowledge("知识点3", "知识点2"));
    private final CycleWbQuestion question1000405 = CycleWbQuestion.of("试题1000405", 0.34, knowledge("知识点2", "知识点9"));
    private final CycleWbQuestion question1000501 = CycleWbQuestion.of("试题1000501", 0.81, knowledge("知识点1", "知识点6"));
    private final CycleWbQuestion question1000502 = CycleWbQuestion.of("试题1000502", 0.52, knowledge("知识点1", "知识点5"));
    private final CycleWbQuestion question1000503 = CycleWbQuestion.of("试题1000503", 0.55, knowledge("知识点1", "知识点7"));
    private final CycleWbQuestion question1000504 = CycleWbQuestion.of("试题1000504", 0.36, knowledge("知识点5", "知识点2"));
    private final CycleWbQuestion question1000505 = CycleWbQuestion.of("试题1000505", 0.10, knowledge("知识点6", "知识点1"));
    private final CycleWbQuestion question1000601 = CycleWbQuestion.of("试题1000601", 0.83, knowledge("知识点1", "知识点2"));
    private final CycleWbQuestion question1000602 = CycleWbQuestion.of("试题1000602", 0.03, knowledge("知识点4", "知识点6"));
    private final CycleWbQuestion question1000603 = CycleWbQuestion.of("试题1000603", 0.34, knowledge("知识点2", "知识点4"));
    private final CycleWbQuestion question1000604 = CycleWbQuestion.of("试题1000604", 0.20, knowledge("知识点8", "知识点6"));
    private final CycleWbQuestion question1000605 = CycleWbQuestion.of("试题1000605", 0.26, knowledge("知识点2", "知识点8"));
    
}
