package com.dongni.tiku.wrong.book.bean.cycle.knowledge;

import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.similar.enumeration.Difficulty;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaper;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbExamPaperQuestionRate;
import com.dongni.tiku.wrong.book.bean.cycle.CycleWbQuestion;
import org.bson.Document;
import org.junit.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 *
 * <AUTHOR>
 * @date 2023/06/29
 */
public class CycleWbGraspHelperTest {
    
    private Set<String> knowledge(String... knowledge) {
        if (knowledge == null || knowledge.length == 0) {
            return new HashSet<>();
        }
        return Stream.of(knowledge).collect(Collectors.toSet());
    }
    
    @Test
    public void test() {
        
        CycleWbExamPaper examPaper101 = CycleWbExamPaper.of(101, 100001, 10001);
        CycleWbExamPaper examPaper102 = CycleWbExamPaper.of(102, 100002, 10002);
        CycleWbExamPaper examPaper103 = CycleWbExamPaper.of(103, 100003, 10003);
        CycleWbExamPaper examPaper104 = CycleWbExamPaper.of(104, 100004, 10004);
        CycleWbExamPaper examPaper105 = CycleWbExamPaper.of(105, 100005, 10005);
        CycleWbExamPaper examPaper106 = CycleWbExamPaper.of(106, 100006, 10006);
        CycleWbExamPaper examPaper107 = CycleWbExamPaper.of(107, 100005, 10007);
        
        CycleWbQuestion question1000101 = CycleWbQuestion.of("试题1000101", 0.75, knowledge("知识点1", "知识点2"));
        CycleWbQuestion question1000102 = CycleWbQuestion.of("试题1000102", 0.30, knowledge("知识点3", "知识点4", "知识点5"));
        CycleWbQuestion question1000103 = CycleWbQuestion.of("试题1000103", 0.66, knowledge("知识点6"));
        CycleWbQuestion question1000104 = CycleWbQuestion.of("试题1000104", 0.90, knowledge());
        CycleWbQuestion question1000105 = CycleWbQuestion.of("试题1000105", 0.99, knowledge("知识点1", "知识点4"));
        
        CycleWbQuestion question1000201 = CycleWbQuestion.of("试题1000201", 0.85, knowledge("知识点1", "知识点3"));
        CycleWbQuestion question1000202 = CycleWbQuestion.of("试题1000202", 0.54, knowledge("知识点9", "知识点6"));
        CycleWbQuestion question1000203 = CycleWbQuestion.of("试题1000203", 0.92, knowledge("知识点4", "知识点7"));
        CycleWbQuestion question1000204 = CycleWbQuestion.of("试题1000204", 0.22, knowledge("知识点6", "知识点8"));
        CycleWbQuestion question1000205 = CycleWbQuestion.of("试题1000205", 0.70, knowledge("知识点5", "知识点2"));
        
        CycleWbQuestion question1000301 = CycleWbQuestion.of("试题1000301", 0.88, knowledge("知识点5", "知识点1"));
        CycleWbQuestion question1000302 = CycleWbQuestion.of("试题1000302", 0.54, knowledge("知识点8", "知识点5"));
        CycleWbQuestion question1000303 = CycleWbQuestion.of("试题1000303", 0.92, knowledge("知识点3", "知识点6"));
        CycleWbQuestion question1000304 = CycleWbQuestion.of("试题1000304", 0.22, knowledge("知识点9", "知识点7"));
        CycleWbQuestion question1000305 = CycleWbQuestion.of("试题1000305", 0.70, knowledge("知识点9", "知识点2"));
        
        CycleWbQuestion question1000401 = CycleWbQuestion.of("试题1000401", 0.90, knowledge("知识点1", "知识点2"));
        CycleWbQuestion question1000402 = CycleWbQuestion.of("试题1000402", 0.98, knowledge("知识点8", "知识点6"));
        CycleWbQuestion question1000403 = CycleWbQuestion.of("试题1000403", 0.83, knowledge("知识点2", "知识点6"));
        CycleWbQuestion question1000404 = CycleWbQuestion.of("试题1000404", 0.92, knowledge("知识点3", "知识点2"));
        CycleWbQuestion question1000405 = CycleWbQuestion.of("试题1000405", 0.34, knowledge("知识点2", "知识点9"));
        
        CycleWbQuestion question1000501 = CycleWbQuestion.of("试题1000501", 0.81, knowledge("知识点1", "知识点6"));
        CycleWbQuestion question1000502 = CycleWbQuestion.of("试题1000502", 0.52, knowledge("知识点1", "知识点5"));
        CycleWbQuestion question1000503 = CycleWbQuestion.of("试题1000503", 0.55, knowledge("知识点1", "知识点7"));
        CycleWbQuestion question1000504 = CycleWbQuestion.of("试题1000504", 0.36, knowledge("知识点5", "知识点2"));
        CycleWbQuestion question1000505 = CycleWbQuestion.of("试题1000505", 0.10, knowledge("知识点6", "知识点1"));
        
        CycleWbQuestion question1000601 = CycleWbQuestion.of("试题1000601", 0.83, knowledge("知识点1", "知识点2"));
        CycleWbQuestion question1000602 = CycleWbQuestion.of("试题1000602", 0.03, knowledge("知识点4", "知识点6"));
        CycleWbQuestion question1000603 = CycleWbQuestion.of("试题1000603", 0.34, knowledge("知识点2", "知识点4"));
        CycleWbQuestion question1000604 = CycleWbQuestion.of("试题1000604", 0.20, knowledge("知识点8", "知识点6"));
        CycleWbQuestion question1000605 = CycleWbQuestion.of("试题1000605", 0.26, knowledge("知识点2", "知识点8"));
        
        String orgName = "studentId";
        long orgId = 101;
        
        
        CycleWbGraspHelper cycleWbGraspHelper = new CycleWbGraspHelper()
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper101, question1000101, orgName, orgId, 0.70))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper101, question1000102, orgName, orgId, 0.45))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper101, question1000103, orgName, orgId, 0.60))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper101, question1000104, orgName, orgId, 1.00))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper101, question1000105, orgName, orgId, 0.99))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper102, question1000201, orgName, orgId, 0.25))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper102, question1000202, orgName, orgId, 0.80))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper102, question1000203, orgName, orgId, 0.58))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper102, question1000204, orgName, orgId, 0.35))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper102, question1000205, orgName, orgId, 0.74))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper103, question1000301, orgName, orgId, 0.59))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper103, question1000302, orgName, orgId, 0.16))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper103, question1000303, orgName, orgId, 0.67))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper103, question1000304, orgName, orgId, 0.82))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper103, question1000305, orgName, orgId, 0.88))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper104, question1000401, orgName, orgId, 0.54))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper104, question1000402, orgName, orgId, 0.06))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper104, question1000403, orgName, orgId, 0.67))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper104, question1000404, orgName, orgId, 0.54))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper104, question1000405, orgName, orgId, 0.06))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper105, question1000501, orgName, orgId, 0.67))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper105, question1000502, orgName, orgId, 0.54))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper105, question1000503, orgName, orgId, 0.72))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper105, question1000504, orgName, orgId, 0.91))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper105, question1000505, orgName, orgId, 1.00))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper106, question1000601, orgName, orgId, 0.53))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper106, question1000602, orgName, orgId, 0.53))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper106, question1000603, orgName, orgId, 0.51))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper106, question1000604, orgName, orgId, 0.13))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper106, question1000605, orgName, orgId, 0.07))
                
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper107, question1000101, orgName, orgId, 0.49))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper107, question1000202, orgName, orgId, 0.92))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper107, question1000303, orgName, orgId, 0.67))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper107, question1000404, orgName, orgId, 0.04))
                .addRate(CycleWbExamPaperQuestionRate.of(examPaper107, question1000505, orgName, orgId, 0.72));
        
        System.out.println("============= GraspCompositeList ===========");
        List<CycleWbGraspComposite> graspCompositeList = cycleWbGraspHelper.getGraspCompositeList().stream()
                .sorted(ComparatorEx
                        .asc(CycleWbGraspComposite::getKnowledgeId)
                        .thenAsc(item -> item.getDifficulty().getValue())
                )
                .collect(toList());
        System.out.println(graspCompositeList);
        System.out.println("============= KnowledgeId2Difficulty2GraspComposite ===========");
        System.out.println(cycleWbGraspHelper.getKnowledgeId2Difficulty2GraspComposite());
        
        System.out.println("============= Difficulty2KnowledgeId2GraspComposite ===========");
        System.out.println(cycleWbGraspHelper.getDifficulty2KnowledgeId2GraspComposite());
        
        System.out.println("============= to document ===========");
        List<Document> graspCompositeDocumentList = CycleWbGraspComposite.toDocument(graspCompositeList);
        System.out.println(JSONUtil.toJson(graspCompositeDocumentList));
        
        System.out.println("============= from document ===========");
        List<CycleWbGraspComposite> graspCompositeListFromDocument = CycleWbGraspComposite.fromDocument(graspCompositeDocumentList);
        System.out.println(graspCompositeListFromDocument);
        
        CycleWbGraspHelper cycleWbGraspHelper2 = new CycleWbGraspHelper()
                .addGraspComposite(graspCompositeListFromDocument);
        System.out.println("============= GraspCompositeList2 ===========");
        List<CycleWbGraspComposite> graspCompositeList2 = cycleWbGraspHelper2.getGraspCompositeList().stream()
                .sorted(ComparatorEx
                        .asc(CycleWbGraspComposite::getKnowledgeId)
                        .thenAsc(item -> item.getDifficulty().getValue())
                )
                .collect(toList());
        System.out.println(graspCompositeList2);
        System.out.println(cycleWbGraspHelper2.getDifficulty2KnowledgeId2GraspComposite());
        System.out.println(cycleWbGraspHelper2.getKnowledgeId2Difficulty2GraspComposite());
        
        System.out.println();
        for (Difficulty difficulty : Difficulty.values()) {
            System.out.println("============= sorted ===========" + difficulty);
            System.out.println(cycleWbGraspHelper.getSortedGraspCompositeList(difficulty));
        }
        
    }
    
}
