package com.dongni.tiku.wrong.book.util;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.wrong.book.bean.cycle.level.CycleWbLevelStudentScore;
import com.dongni.tiku.wrong.book.bean.cycle.level.CycleWbLevelStudent;
import com.dongni.tiku.wrong.book.bean.cycle.level.CycleWbLevelRankPercent;
import com.dongni.tiku.wrong.book.utils.CycleWbStudentLevelUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/06/28
 */
public class CycleWbStudentLevelUtilTest {
    
    @Test
    public void computeStudentLevelTest() {
        List<CycleWbLevelStudent> studentScoreList = new ArrayList<>();
        
        studentScoreList.add(new CycleWbLevelStudent(101, 1001, 100101, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 80.00, true),
                                new CycleWbLevelStudentScore(102, 10002, 80.00, true),
                                new CycleWbLevelStudentScore(103, 10003, 80.00, true),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1001, 100102, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 80.00, true),
                                new CycleWbLevelStudentScore(102, 10002, 80.00, true),
                                new CycleWbLevelStudentScore(103, 10003, 80.00, false),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1001, 100103, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 60.00, true),
                                new CycleWbLevelStudentScore(102, 10002, 60.00, true),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1001, 100104, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 60.00, true),
                                new CycleWbLevelStudentScore(102, 10002, 60.00, false),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1002, 100201, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 60.00, true),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1002, 100202, true, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 60.00, false),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1002, 100203, false, Stream.of(
                                new CycleWbLevelStudentScore(101, 10001, 80.00, true),
                                new CycleWbLevelStudentScore(102, 10002, 80.00, true),
                                new CycleWbLevelStudentScore(103, 10003, 80.00, true),
                                null
                        )
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
        );
        studentScoreList.add(new CycleWbLevelStudent(101, 1002, 100204, false, null));
        
        System.out.println(JSONUtil.toJson(studentScoreList));
        
        List<CycleWbLevelRankPercent> levelRankPercentList = Stream.of(
                new CycleWbLevelRankPercent("A", "[0.0, 0.2]"),
                new CycleWbLevelRankPercent("B", "(0.2, 0.4]"),
                new CycleWbLevelRankPercent("C", "(0.4, 0.6]"),
                new CycleWbLevelRankPercent("D", "(0.6, 0.8]"),
                new CycleWbLevelRankPercent("E", "(0.8, 1.0]")
        ).collect(Collectors.toList());
        
        CycleWbStudentLevelUtil.computeStudentLevel(studentScoreList, levelRankPercentList);
        System.out.println(JSONUtil.toJson(studentScoreList));
    }
}
