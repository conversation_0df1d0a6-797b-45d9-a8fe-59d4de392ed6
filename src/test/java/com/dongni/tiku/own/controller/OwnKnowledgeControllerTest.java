package com.dongni.tiku.own.controller;

import com.dongni.common.auth.DongniClient;
import com.dongni.common.auth.impl.DongniClientAuthNicezhuanyeImpl;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

/**
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
public class OwnKnowledgeControllerTest {
    
    @Test
    public void testGetKnowledgeByCourseIdForNicezhuanye() {
        DongniClient dongniClient = new DongniClient();
        dongniClient.setRestTemplate(new RestTemplate());
        Object result = dongniClient.post(
                "https://dalao.dongni100.com/api/tiku/own/knowledge/1f742681-9534-4cee-b3f2-92d6dcfc39f2/course",
                null,
                MapUtil.of("courseId", 2),
                new DongniClientAuthNicezhuanyeImpl());
        System.out.println(JSONUtil.toJson(result));
    }
    
    
}
