package com.dongni.tiku.own.service;

import cn.hutool.crypto.digest.BCrypt;
import com.dongni.common.mongo.IManager;
import com.dongni.commons.mongodb.MongoClientManager;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import net.sf.json.JSON;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @date 2024/07/29
 */
public class TikuQuestionTest {
    
    @Test
    public void test2() {
        System.out.println(BCrypt.hashpw("123456"));
        System.out.println(BCrypt.hashpw("123456"));
        System.out.println(BCrypt.hashpw("123456"));
        System.out.println(BCrypt.hashpw("123456"));
        System.out.println(BCrypt.checkpw("123456", "$2a$10$x1K5pSPjIzUr0Xob0XYXtuHOF2jfABNaxraAO9Z5bNeilk0QCJInK"));
        System.out.println(BCrypt.checkpw("admin123", "$2a$10$XTBg.o5w5/cHtvjRe5yy7eqSgvjWBKgztwp67wmWSx6ITKhN40rEq"));
        
        
    }
    
    @Test
    public void test0() {
        MongoClientManager tikuMongodb = new MongoClientManager();
        tikuMongodb.setHost("localhost");
        tikuMongodb.setPort("62037");
        tikuMongodb.setDatabase("tiku");
        tikuMongodb.setUserName("tiku");
        tikuMongodb.setPassword("Tiku2015");
        IManager questionManager = new IManager(tikuMongodb, "question", "试题") {
        };
        
        Document document = questionManager.get(new ObjectId("684a31a3eeaeb100179e5eca"));
        System.out.println(JSONUtil.toJson(document));
    }
    
    @Test
    public void test1() {
        MongoClientManager tikuMongodb = new MongoClientManager();
        tikuMongodb.setHost("************");
        tikuMongodb.setPort("27017");
        tikuMongodb.setDatabase("tiku");
        tikuMongodb.setUserName("tiku");
        tikuMongodb.setPassword("Tiku2015");
        IManager testManager = new IManager(tikuMongodb, "test20250624", "测试") {
        };
        
        List<Document> list = new ArrayList<>();
        list.add(new Document().append("name", "123"));
        list.add(new Document().append("name", "456"));
        
        System.out.println(JSONUtil.toJson(list));
        testManager.insertMany(list);
        for (Document document : list) {
            if (document.get("_id") != null) {
                document.put("_id", document.get("_id").toString());
            }
        }
        System.out.println(JSONUtil.toJson(list));
        
    }
    
    @Test
    public void test() {
        MongoClientManager tikuMongodb = new MongoClientManager();
        tikuMongodb.setHost("************");
        tikuMongodb.setPort("34352");
        tikuMongodb.setDatabase("tiku");
        tikuMongodb.setUserName("tiku");
        tikuMongodb.setPassword("Tiku#!@2019");
        
        IManager questionManager = new IManager(tikuMongodb, TikuMongodb.COLLECTION_QUESTION, "试题") {
        };
        
        List<String> questionIdStrList = Stream.of(
                        "611f10cc7f9e6e57c3d14d15",  // 1-3
                        "611f10ae7f9e6e57c3d14ca0",  // 1-4
                        "611f104bee588a3a583a2f60",  // 1-5
                        "611f1038ee588a3a583a2ef2",  // 1-6
                        "611f0ef7ee588a3a583a289b",  // 1-7
                        "611f0ed67f9e6e57c3d140c4",  // 1-8
                        "611f0ec5ee588a3a583a27bc",  // 1-9
                        "611f0eabee588a3a583a274d",  // 1-10
                        "5f84914624b13c1de8dd558f",  // 2-1
                        "5f8490ea24b13c1de8dcd72e",  // 2-2
                        "5f8490c77f9e6e42b6c20992",  // 2-3
                        "5ecb2a0f24b13c5bfbe7ae9e",  // 2-4
                        "5ec7e1697f9e6e603deb1e82",  // 2-5
                        "5e673f265de1716038bb8fff",  // 2-6
                        "5e673d177f9e6e03e8343ccf",  // 2-7
                        "5d6d0cd37f9e6e6f64b1a628",  // 2-8
                        "5d6cf6ed7f9e6e6f64b1a4e7",  // 2-9
                        "5d6cf1f87f9e6e6f64b1a4a8",  // 2-10
                        "61a5dda27f9e6e6e4a4bbc52",  // 1-1 挪到第二页
                        "61a5db6892c3727c19c073e6",  // 1-2 挪到第二页
                        "5d677ba67f9e6e4e4cd37cf4",  // 3-1
                        "5cf1ebd60f28b4131c08c145",  // 3-2
                        null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        long now = System.currentTimeMillis();
        for (int i = 0, iLen = questionIdStrList.size(); i < iLen; i++) {
            // questionManager.updateOne(
            //         eq("_id", new ObjectId(questionIdStrList.get(i))),
            //         set("modifyDateTime", new Date(now - i))
            // );
            Document first = questionManager.getFirst(eq("_id", new ObjectId(questionIdStrList.get(i))));
            Object id = MapUtil.getCast(first, "_id");
            Date modifyDateTime = MapUtil.getCast(first, "modifyDateTime");
            
            System.out.println(id + "    " + modifyDateTime  + "   " + modifyDateTime.getTime()  + "  " + (now - modifyDateTime.getTime()));
        }
        
    }
}
