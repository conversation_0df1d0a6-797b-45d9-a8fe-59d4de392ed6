//package com.dongni.tiku.own.service;
//
//import com.dongni.basedata.bean.BaseDataRepository;
//import com.dongni.common.threadpool.MyAsyncConfigurer;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CountDownLatch;
//
//import static java.util.stream.Collectors.groupingBy;
//
///**
// * <AUTHOR>
// * @date 2019/05/06 16:15
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest
//public class OwnEntrustAppointServiceTest {
//
//    @Autowired
//    private MyAsyncConfigurer myAsyncConfigurer;
//
//    @Autowired
//    private OwnEntrustAppointService ownEntrustAppointService;
//
//    @Autowired
//    private BaseDataRepository repository;
//
//    @Test
//    public void startAppoint() {
//
//        List<Map<String, Object>> typistList = repository.selectList("TypistMapper.getAllTypist");
//
//        CountDownLatch countDownLatch = new CountDownLatch(typistList.size());
//
//        List<Map<String, Object>> result = new ArrayList<>();
//
//        for (Map<String, Object> typist : typistList) {
//
//
//            Runnable runnable = () -> {
//                try {
//                    List<Map<String, Object>> list = ownEntrustAppointService.startAppoint(typist);
//                    result.addAll(list);
//                } catch (Exception e) {
//                    LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
//                }
//
//                countDownLatch.countDown();
//            };
//
//            runnable.run();
//        }
//
//        try {
//            countDownLatch.await();
//        } catch (InterruptedException e) {
//            LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
//        }
//
//        Map<Long, List<Map<String, Object>>> typistGroup = result.stream()
//                .collect(groupingBy(item -> Long.valueOf(item.get("entrustTypistId").toString())));
//
//        Map<Long, Object> duplicateData = new HashMap<>();
//
//        typistGroup.forEach((typistId, items) -> {
//            if (items.size() > 1) {
//                duplicateData.put(typistId, items.size());
//            }
//        });
//
//        System.out.println("----------------重复数据---------------");
//        System.out.println(duplicateData);
//        System.out.println("----------------重复数据---------------");
//
//    }
//}