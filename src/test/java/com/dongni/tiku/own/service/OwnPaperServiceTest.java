package com.dongni.tiku.own.service;

import com.dongni.commons.mvc.context.DongniUserInfoContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @date 2025/04/01
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class OwnPaperServiceTest {
    
    @Autowired
    private OwnPaperService ownPaperService;
    
    @Test
    public void copyFromStudyGuidePaper() {
        DongniUserInfoContext.set(new DongniUserInfoContext(1L, "test", 1));
        // ownPaperService.copyFromStudyGuidePaper(1536035601368592L, null, null);
        ownPaperService.copyFromStudyGuidePaper(1536035601368592L, 3L, 110000L);
    }
    
}
