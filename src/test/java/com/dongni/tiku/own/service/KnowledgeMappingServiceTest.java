package com.dongni.tiku.own.service;

import com.alibaba.fastjson.JSON;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.JinjuanQuestionManager;
import com.dongni.tiku.manager.impl.KnowledgeMappingMasterManager;
import com.dongni.tiku.manager.impl.KnowledgeMasterManager;
import com.dongni.tiku.own.enumeration.KnowledgeMappingType;
import com.dongni.tiku.own.service.impl.KnowledgeMasterXkwServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/9/19 上午 11:21
 * @Version 1.0.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class KnowledgeMappingServiceTest {
    
    private static final Logger log = LoggerFactory.getLogger(KnowledgeMappingServiceTest.class);
    
    @Autowired
    private KnowledgeMappingService knowledgeMappingService;
    
    @Test
    public void getDongNi2XkwMapping() {
        Map<String, Object> dongNi2XkwMapping = knowledgeMappingService.getDongNi2OtherMapping(10L,
                Arrays.asList("641ab7c5680d3f0c60f7ef38", "641ab7c5680d3f0c60f7ef4e",
                        "641ab770680d3f0c60f7c77f"  // 数学的知识点
                ), 100002);
        System.out.println(JSON.toJSON(dongNi2XkwMapping));
        
        dongNi2XkwMapping = knowledgeMappingService.getDongNi2OtherMapping(3L,
                new ArrayList<>(0), 100002);
        System.out.println(JSON.toJSON(dongNi2XkwMapping));
    }
    
    
    @Autowired
    private KnowledgeMappingMasterManager knowledgeMappingMasterManager;
    @Autowired
    private KnowledgeMasterManager knowledgeMasterManager;
    
    /**
     * 检查知识点及映射关系是否正常
     */
    @Test
    public void checkKnowledgeMasterMapping() {
        for (Long courseId : KnowledgeMasterXkwServiceImpl.SYNC_COURSE_ID2_CONTINUOUS_UPDATE.keySet()) {
            log.error("======================================================== courseId: {}", courseId);
            
            List<Document> knowledgeList = knowledgeMasterManager.getList(courseId);
            List<Document> knowledgeMappingList = knowledgeMappingMasterManager.getList(and(
                    eq("courseId", courseId),
                    eq("fromType", KnowledgeMappingType.XKW.getType()),
                    eq("toType", KnowledgeMappingType.DONGNI.getType())
            ));
            
            // ------------- 以knowledge为基准
            {
                Map<String, Document> fromKey2KnowledgeMapping = knowledgeMappingList.stream()
                        .collect(Collectors.toMap(item -> MapUtil.getTrim(item, "fromKey"), item -> item));
                Map<String, Document> toKey2KnowledgeMapping = knowledgeMappingList.stream()
                        .collect(Collectors.toMap(item -> MapUtil.getTrim(item, "toKey"), item -> item));
                for (Document knowledge : knowledgeList) {
                    String thirdKey = MapUtil.getTrim(knowledge, "thirdKey");
                    if (thirdKey.startsWith("dongni")) {
                        continue;
                    }
                    
                    String courseName = MapUtil.getTrim(knowledge, "courseName");
                    
                    String knowledgeId = MapUtil.getTrim(knowledge, "_id");
                    String knowledgeName = MapUtil.getTrim(knowledge, "knowledgeName");
                    String xkwKnowledgeId = MapUtil.getTrim(knowledge, "xkwKnowledgeId");
                    String xkwKnowledgeName = MapUtil.getTrim(knowledge, "xkwKnowledgeName");
                    
                    Document fromKeyKnowledgeMapping = fromKey2KnowledgeMapping.remove(xkwKnowledgeId);
                    Document toKeyKnowledgeMapping = toKey2KnowledgeMapping.remove(knowledgeId);
                    boolean mappingNotExist = fromKeyKnowledgeMapping == null && toKeyKnowledgeMapping == null;
                    boolean mappingError = false;
                    if (!mappingNotExist) {
                        mappingError = mappingError || !knowledgeId.equals(MapUtil.getTrimNullable(fromKeyKnowledgeMapping, "toKey"));
                        mappingError = mappingError || !knowledgeName.equals(MapUtil.getTrimNullable(fromKeyKnowledgeMapping, "toName"));
                        mappingError = mappingError || !xkwKnowledgeId.equals(MapUtil.getTrimNullable(fromKeyKnowledgeMapping, "fromKey"));
                        mappingError = mappingError || !xkwKnowledgeName.equals(MapUtil.getTrimNullable(fromKeyKnowledgeMapping, "fromName"));
                        
                        mappingError = mappingError || !knowledgeId.equals(MapUtil.getTrimNullable(toKeyKnowledgeMapping, "toKey"));
                        mappingError = mappingError || !knowledgeName.equals(MapUtil.getTrimNullable(toKeyKnowledgeMapping, "toName"));
                        mappingError = mappingError || !xkwKnowledgeId.equals(MapUtil.getTrimNullable(toKeyKnowledgeMapping, "fromKey"));
                        mappingError = mappingError || !xkwKnowledgeName.equals(MapUtil.getTrimNullable(toKeyKnowledgeMapping, "fromName"));
                    }
                    
                    if (mappingNotExist) {
                        log.error("courseId: {}; courseName: {};" +
                                        " knowledgeId: {}; knowledgeName: {};" +
                                        " xkwKnowledgeId: {}; xkwKnowledgeName: {};" +
                                        " fromKeyKnowledgeMapping: {}; toKeyKnowledgeMapping: {}",
                                courseId, courseName,
                                knowledgeId, knowledgeName,
                                xkwKnowledgeId, xkwKnowledgeName,
                                fromKeyKnowledgeMapping, toKeyKnowledgeMapping
                        );
                    } else if (mappingError) {
//                        log.warn("courseId: {}; courseName: {};" +
//                                        " knowledgeId: {}; knowledgeName: {};" +
//                                        " xkwKnowledgeId: {}; xkwKnowledgeName: {};" +
//                                        " fromKeyKnowledgeMapping: {}; toKeyKnowledgeMapping: {}",
//                                courseId, courseName,
//                                knowledgeId, knowledgeName,
//                                xkwKnowledgeId, xkwKnowledgeName,
//                                fromKeyKnowledgeMapping, toKeyKnowledgeMapping
//                        );
                    } else {
//                        log.info("courseId: {}; courseName: {};" +
//                                        " knowledgeId: {}; knowledgeName: {};" +
//                                        " xkwKnowledgeId: {}; xkwKnowledgeName: {};" +
//                                        " fromKeyKnowledgeMapping: {}; toKeyKnowledgeMapping: {}",
//                                courseId, courseName,
//                                knowledgeId, knowledgeName,
//                                xkwKnowledgeId, xkwKnowledgeName,
//                                fromKeyKnowledgeMapping, toKeyKnowledgeMapping
//                        );
                    }
                }
                
                if (MapUtils.isNotEmpty(fromKey2KnowledgeMapping)) {
                    log.error("======================================================== fromKey2KnowledgeMapping");
                    
                    fromKey2KnowledgeMapping.forEach((fromKey, knowledgeMapping) -> {
                        log.info("courseId: {}; courseName: {}; fromKey(xkwKnowledgeId): {}; knowledgeMapping: {}",
                                MapUtil.getLong(knowledgeMapping, "courseId"),
                                MapUtil.getTrim(knowledgeMapping, "courseName"),
                                fromKey, knowledgeMapping
                        );
                    });
                }
                
                if (MapUtils.isNotEmpty(toKey2KnowledgeMapping)) {
                    log.error("======================================================== toKey2KnowledgeMapping");
                    
                    toKey2KnowledgeMapping.forEach((toKey, knowledgeMapping) -> {
                        log.info("courseId: {}; courseName: {}; toKey(knowledgeId): {}; knowledgeMapping: {}",
                                MapUtil.getLong(knowledgeMapping, "courseId"),
                                MapUtil.getTrim(knowledgeMapping, "courseName"),
                                toKey, knowledgeMapping
                        );
                    });
                }
            }
        }
    }
    
    @Test
    public void checkKnowledgeMasterMapping2() {
        Set<String> toKeySet = new HashSet<>();
        Bson query = eq("toType", KnowledgeMappingType.DONGNI.getType());
        knowledgeMappingMasterManager.getFindIterable(query).forEach((Consumer<? super Document>) knowledgeMappingMaster -> {
            knowledgeMappingMaster.put("_id", MapUtil.getTrim(knowledgeMappingMaster, "_id"));
            String knowledgeIdStr = MapUtil.getTrim(knowledgeMappingMaster, "toKey");
            ObjectId knowledgeId = MongoUtil.getMongoId(knowledgeIdStr);
            Document knowledgeMaster = knowledgeMasterManager.getNullable(knowledgeId);
            if (MapUtils.isEmpty(knowledgeMaster)) {
                toKeySet.add(knowledgeIdStr);
                log.error("映射关系的知识点找不到知识点信息: {}", JSONUtil.toJson(knowledgeMappingMaster));
            }
        });
        
        log.warn("映射关系的知识点找不到知识点信息: toKeySize: {}; toKey: {}", toKeySet.size(), toKeySet);
        toKeySet.forEach(System.out::println);
    }
    
    /**
     * 修复映射关系 执行完如果有修复的数据，需要更改版本信息
     * {
     *     "_id" : ObjectId("643903bf0ded7c3b9e7b2008"),
     *     "stage" : NumberInt(3),
     *     "courseId" : NumberLong(2),
     *     "courseName" : "语文",
     *     "fromType" : NumberInt(1001),
     *     "fromTypeName" : "dongni-old-20230320",
     *     "fromKey" : "5bdfbb63cb0f5130240222c6",
     *     "fromName" : "词性",
     *     "toType" : NumberInt(0),
     *     "toTypeName" : "dongni",
     *     "toKey" : "6438d6c8cff47e000edf7936",
     *     "toName" : "词性"
     * }
     *
     * 的toKey映射到了一个错误的信息，而xkw对应的数据已经修复过了，可以获取到其对的数据
     *
     * {
     *     "_id" : ObjectId("6439045b0ded7c3b9e7bad19"),
     *     "stage" : NumberInt(3),
     *     "courseId" : NumberLong(2),
     *     "courseName" : "语文",
     *     "fromType" : NumberInt(100002),
     *     "fromTypeName" : "xkw",
     *     "fromKey" : "82225",
     *     "fromName" : "词性",
     *     "toType" : NumberInt(0),
     *     "toTypeName" : "dongni",
     *     "toKey" : "641ab760680d3f0c60f7c039",
     *     "toName" : "词性"
     * }
     *
     * 需要将 ObjectId("643903bf0ded7c3b9e7b2008") 的toKey 由 6438d6c8cff47e000edf7936 修改为 641ab760680d3f0c60f7c039
     *
     */
    @Test
    public void fixKnowledgeMappingMasterTest() {
        List<Map<String, Object>> fixList = new ArrayList<>();
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46165", "toKeyError", "6438d6c9cff47e000edf7951"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46164", "toKeyError", "6438d6c9cff47e000edf7950"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46161", "toKeyError", "6438d6c8cff47e000edf794d"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46162", "toKeyError", "6438d6c8cff47e000edf794e"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46159", "toKeyError", "6438d6c8cff47e000edf794b"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46160", "toKeyError", "6438d6c8cff47e000edf794c"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46167", "toKeyError", "6438d6c8cff47e000edf7940"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46168", "toKeyError", "6438d6c8cff47e000edf7941"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81288", "toKeyError", "6438d6c9cff47e000edf795e"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81338", "toKeyError", "6438d6c9cff47e000edf795d"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "82243", "toKeyError", "6438d6c8cff47e000edf7937"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "43198", "toKeyError", "6438d6c8cff47e000edf7935"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "82225", "toKeyError", "6438d6c8cff47e000edf7936"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46046", "toKeyError", "6438d6c8cff47e000edf7933"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46047", "toKeyError", "6438d6c8cff47e000edf7934"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "65768", "toKeyError", "6438d6c8cff47e000edf7932"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46158", "toKeyError", "6438d6c8cff47e000edf794a"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46051", "toKeyError", "6438d6c8cff47e000edf7939"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46148", "toKeyError", "6438d6c8cff47e000edf793f"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "82226", "toKeyError", "6438d6c8cff47e000edf793c"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46132", "toKeyError", "6438d6c8cff47e000edf793d"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "82173", "toKeyError", "6438d6c8cff47e000edf793a"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "82174", "toKeyError", "6438d6c8cff47e000edf793b"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46163", "toKeyError", "6438d6c9cff47e000edf794f"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46156", "toKeyError", "6438d6c8cff47e000edf7948"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46157", "toKeyError", "6438d6c8cff47e000edf7949"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46154", "toKeyError", "6438d6c8cff47e000edf7946"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46155", "toKeyError", "6438d6c8cff47e000edf7947"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46140", "toKeyError", "6438d6c8cff47e000edf7944"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46153", "toKeyError", "6438d6c8cff47e000edf7945"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46169", "toKeyError", "6438d6c8cff47e000edf7942"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46133", "toKeyError", "6438d6c8cff47e000edf7943"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46177", "toKeyError", "6438d6c9cff47e000edf7957"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46175", "toKeyError", "6438d6c9cff47e000edf7956"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46174", "toKeyError", "6438d6c9cff47e000edf7955"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46172", "toKeyError", "6438d6c9cff47e000edf7954"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46171", "toKeyError", "6438d6c9cff47e000edf7953"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46170", "toKeyError", "6438d6c9cff47e000edf7952"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81289", "toKeyError", "6438d6c9cff47e000edf7960"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81285", "toKeyError", "6438d6c9cff47e000edf7959"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "80736", "toKeyError", "6438d6c9cff47e000edf7958"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "46173", "toKeyError", "6438d6c9cff47e000edf795f"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81337", "toKeyError", "6438d6c9cff47e000edf795c"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81287", "toKeyError", "6438d6c9cff47e000edf795b"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "81286", "toKeyError", "6438d6c9cff47e000edf795a"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "71961", "toKeyError", "6438d6c8cff47e000edf793e"));
        fixList.add(MapUtil.of("courseId", 2L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "43199", "toKeyError", "6438d6c8cff47e000edf7938"));
        fixList.add(MapUtil.of("courseId", 8L, "fromType", KnowledgeMappingType.XKW.getType(), "fromKey", "87248", "toKeyError", "6437773acff47e000d1a3664"));
        
        
        for (Map<String, Object> fix : fixList) {
            long courseId = MapUtil.getLong(fix, "courseId");
            int fromType = MapUtil.getInt(fix, "fromType");
            String fromKey = MapUtil.getTrim(fix, "fromKey");
            List<Document> xkwKnowledgeMappingList = knowledgeMappingMasterManager.getList(and(
                    eq("courseId", courseId),
                    eq("toType", KnowledgeMappingType.DONGNI.getType()),
                    eq("fromType", fromType),
                    eq("fromKey", fromKey)
            ));
            if (CollectionUtils.isEmpty(xkwKnowledgeMappingList)) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点映射关系-toDongni-不存在: fromType: " + fromType + "; fromKey: " + fromKey);
            }
            if (xkwKnowledgeMappingList.size() > 1) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点映射关系-toDongni-数量异常: fromType: " + fromType + "; fromKey: " + fromKey);
            }
            Document xkwKnowledgeMapping = xkwKnowledgeMappingList.get(0);
            String toKey = MapUtil.getTrim(xkwKnowledgeMapping, "toKey");
            String toName = MapUtil.getTrim(xkwKnowledgeMapping, "toName");
            
            ObjectId knowledgeId = MongoUtil.getMongoId(toKey);
            Document knowledgeMaster = knowledgeMasterManager.getNullable(knowledgeId);
            if (knowledgeMaster == null) {
                throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "知识点数据不存在: " + toKey);
            }
            boolean deleted = MapUtil.getBoolean(knowledgeMaster, "deleted");
            
            if (deleted) {
                log.warn("xkwKnowledgeMapping: {}", JSONUtil.toJson(xkwKnowledgeMapping));
            } else {
                log.info("xkwKnowledgeMapping: {}", JSONUtil.toJson(xkwKnowledgeMapping));
            }
            
            String toKeyError = MapUtil.getTrim(fix, "toKeyError");
            if (toKey.equals(toKeyError)) {
                continue;
            }
            
            List<Document> errorKnowledgeMappingList = knowledgeMappingMasterManager.getList(and(
                    eq("courseId", courseId),
                    eq("toType", KnowledgeMappingType.DONGNI.getType()),
                    eq("toKey", toKeyError)
            ));
            for (Document errorKnowledgeMapping : errorKnowledgeMappingList) {
                log.error("              error: {}", JSONUtil.toJson(errorKnowledgeMapping));
                String idStr = MapUtil.getTrim(errorKnowledgeMapping, "_id");
                ObjectId objectId = MongoUtil.getMongoId(idStr);
                knowledgeMappingMasterManager.update(objectId, combine(
                        set("toKey", toKey),
                        set("toName", toName)
                ));
            }
            
            log.info("");
        }
        
        
    }
    
    @Autowired(required = false)
    private KnowledgeMappingFixService knowledgeMappingFixService;
    @Autowired
    private JinjuanQuestionManager jinjuanQuestionManager;
    
    // @Test
    public void fixQuestionKnowledge() {
        Map<String, Object> fixInfo = knowledgeMappingFixService.getFixInfoList()
                .stream()
                .filter(item -> "82243".equals(MapUtil.getTrim(item, "fromKey")))
                .findFirst()
                .orElse(null);
        if (fixInfo == null) {
            throw new CommonException(ResponseStatusEnum.DATA_NOT_EXISTS, "未发现82243");
        }
        knowledgeMappingFixService.knowledgeMappingFix(
                jinjuanQuestionManager,
                fixInfo,
                eq("_id", new ObjectId("6548a68194430e653bb808cf"))
        );
    }
    
    @Test
    public void fixQuestionKnowledgeAll() {
        knowledgeMappingFixService.knowledgeMappingFix();
    }
}
