package com.dongni.tiku.own.service;

import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TikuPaperServiceTest {
    
    @Autowired
    private TikuPaperService tikuPaperService;
    
    @Test
    public void test() {
        System.out.println(tikuPaperService.getSchoolTikuPaperDetails(MapUtil.of("schoolId", 7)));
    }
}
