package com.dongni.tiku.init;

import com.dongni.common.mongo.IManager;
import com.dongni.common.utils.AppendHashMap;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.enumeration.KnowledgeMappingType;
import com.dongni.tiku.own.service.KnowledgeMappingMasterServiceImpl;
import com.dongni.tiku.own.service.KnowledgeMasterServiceImpl;
import com.dongni.tiku.own.service.TikuBaseDataVersionService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static com.mongodb.client.model.Filters.eq;
import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * 2023/03/22
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class T20230322_KnowledgeMapMasterTest {

    @Autowired
    private TikuMongodb tikuMongodb;
    @Autowired
    private KnowledgeMasterServiceImpl knowledgeMasterService;
    @Autowired
    private KnowledgeMappingMasterServiceImpl knowledgeMappingMasterService;
    @Autowired
    private TikuBaseDataVersionService tikuBaseDataVersionService;
    
    @Test
    public void initKnowledgeMapMaster() {
        Map<Long, KnowledgeMappingType> courseId2Type = new LinkedHashMap<>();
        courseId2Type.put(2L, KnowledgeMappingType.YIQI);
        courseId2Type.put(3L, KnowledgeMappingType.YIQI);
        courseId2Type.put(4L, null);
        courseId2Type.put(5L, KnowledgeMappingType.YIQI);
        courseId2Type.put(6L, KnowledgeMappingType.YIQI);
        courseId2Type.put(7L, KnowledgeMappingType.YIQI);
        courseId2Type.put(8L, KnowledgeMappingType.YIQI);
        courseId2Type.put(9L, KnowledgeMappingType.YIQI);
        courseId2Type.put(10L, KnowledgeMappingType.YIQI);
        courseId2Type.put(1605L, null);
        
        Map<Long, List<Document>> courseId2KnowledgeMapTempList = new HashMap<>();
        Map<Long, List<Map<String, Object>>> courseId2KnowledgeMasterList = new HashMap<>();
        Map<Long, Map<String, Map<String, Object>>> courseId2ThirdKey2KnowledgeMaster = new HashMap<>();
        
        IManager knowledgeMapTempManager = new IManager(tikuMongodb, "knowledgeMapTemp", "知识点映射关系-临时") {};
        for (Long courseId : courseId2Type.keySet()) {
            List<Document> knowledgeMapTempList = knowledgeMapTempManager.getList(eq("courseId", courseId));
            List<Map<String, Object>> knowledgeMasterList = knowledgeMasterService.getKnowledgeMasterList(MapUtil.of("courseId", courseId));
            Map<String, Map<String, Object>> thirdKey2KnowledgeMaster = knowledgeMasterList.stream()
                    .collect(toMap(item -> MapUtil.getTrim(item, "thirdKey"), item -> item));
            courseId2KnowledgeMapTempList.put(courseId, knowledgeMapTempList);
            courseId2KnowledgeMasterList.put(courseId, knowledgeMasterList);
            courseId2ThirdKey2KnowledgeMaster.put(courseId, thirdKey2KnowledgeMaster);
        }
    
        // dongni-old -> dongni
        for (Long courseId : courseId2Type.keySet()) {
            List<Document> knowledgeMapTempList = courseId2KnowledgeMapTempList.get(courseId);
            if (CollectionUtils.isEmpty(knowledgeMapTempList)) { continue; }
            
            masterUpdate(courseId, () -> {
                // List<Map<String, Object>> knowledgeMasterList = courseId2KnowledgeMasterList.get(courseId);
                Map<String, Map<String, Object>> thirdKey2KnowledgeMaster = courseId2ThirdKey2KnowledgeMaster.get(courseId);
                for (Document knowledgeMapTemp : knowledgeMapTempList) {
                    int stage = MapUtil.getInt(knowledgeMapTemp, "stage");
                    String courseName = MapUtil.getTrim(knowledgeMapTemp, "courseName");
                    String fromKey = MapUtil.getTrim(knowledgeMapTemp, "fromKey");
                    String fromName = MapUtil.getTrim(knowledgeMapTemp, "fromName");
                    String toTypeTemp = MapUtil.getTrim(knowledgeMapTemp, "toType");
                    String toKeyTemp = MapUtil.getTrim(knowledgeMapTemp, "toKey");
                    Map<String, Object> knowledgeMaster = thirdKey2KnowledgeMaster.get(toTypeTemp + "-" + toKeyTemp);
                    String toKey = MapUtil.getTrim(knowledgeMaster, "_id");
                    String toName = MapUtil.getTrim(knowledgeMaster, "knowledgeName");
        
                    knowledgeMappingMasterService.insertKnowledgeMapping(new AppendHashMap<String, Object>()
                            .append("stage", stage)
                            .append("courseId", courseId)
                            .append("courseName", courseName)
                            .append("fromType", KnowledgeMappingType.DONGNI_OLD_20230320.getType())
                            .append("fromTypeName", KnowledgeMappingType.DONGNI_OLD_20230320.getTypeName())
                            .append("fromKey", fromKey)
                            .append("fromName", fromName)
                            .append("toType", KnowledgeMappingType.DONGNI.getType())
                            .append("toTypeName", KnowledgeMappingType.DONGNI.getTypeName())
                            .append("toKey", toKey)
                            .append("toName", toName)
                    );
                }
                return true;
            });
        }
        
        // yiqi -> dongni
        for (Long courseId : courseId2Type.keySet()) {
            KnowledgeMappingType knowledgeMappingType = courseId2Type.get(courseId);
            if (knowledgeMappingType == null) { continue; }
            List<Document> knowledgeMapTempList = courseId2KnowledgeMapTempList.get(courseId);
            if (CollectionUtils.isEmpty(knowledgeMapTempList)) { continue; }
            
            masterUpdate(courseId, () -> {
                // List<Map<String, Object>> knowledgeMasterList = courseId2KnowledgeMasterList.get(courseId);
                Map<String, Map<String, Object>> thirdKey2KnowledgeMaster = courseId2ThirdKey2KnowledgeMaster.get(courseId);
    
                for (Document knowledgeMapTemp : knowledgeMapTempList) {
                    int stage = MapUtil.getInt(knowledgeMapTemp, "stage");
                    String courseName = MapUtil.getTrim(knowledgeMapTemp, "courseName");
                    String fromKey = MapUtil.getTrim(knowledgeMapTemp, "fromKey");
                    String fromName = MapUtil.getTrim(knowledgeMapTemp, "fromName");
                    String toTypeTemp = MapUtil.getTrim(knowledgeMapTemp, "toType");
                    String toKeyTemp = MapUtil.getTrim(knowledgeMapTemp, "toKey");
                    Map<String, Object> knowledgeMaster = thirdKey2KnowledgeMaster.get(toTypeTemp + "-" + toKeyTemp);
                    String toKey = MapUtil.getTrim(knowledgeMaster, "_id");
                    String toName = MapUtil.getTrim(knowledgeMaster, "knowledgeName");
        
                    knowledgeMappingMasterService.insertKnowledgeMapping(new AppendHashMap<String, Object>()
                            .append("stage", stage)
                            .append("courseId", courseId)
                            .append("courseName", courseName)
                            .append("fromType", knowledgeMappingType.getType())
                            .append("fromTypeName", knowledgeMappingType.getTypeName())
                            .append("fromKey", fromKey)
                            .append("fromName", fromName)
                            .append("toType", KnowledgeMappingType.DONGNI.getType())
                            .append("toTypeName", KnowledgeMappingType.DONGNI.getTypeName())
                            .append("toKey", toKey)
                            .append("toName", toName)
                    );
                }
                return true;
            });
        }
        

        // xkw -> dongni
        for (Long courseId : courseId2Type.keySet()) {
            List<Map<String, Object>> knowledgeMasterList = courseId2KnowledgeMasterList.get(courseId);
            if (CollectionUtils.isEmpty(knowledgeMasterList)) { continue; }
            
            masterUpdate(courseId, () -> {
                for (Map<String, Object> knowledgeMaster : knowledgeMasterList) {
                    int stage = MapUtil.getInt(knowledgeMaster, "stage");
                    String courseName = MapUtil.getTrim(knowledgeMaster, "courseName");
                    String fromKey = MapUtil.getTrim(knowledgeMaster, "xkwKnowledgeId");
                    String fromName = MapUtil.getTrim(knowledgeMaster, "xkwKnowledgeName");
                    String toKey = MapUtil.getTrim(knowledgeMaster, "_id");
                    String toName = MapUtil.getTrim(knowledgeMaster, "knowledgeName");
                    knowledgeMappingMasterService.insertKnowledgeMapping(new AppendHashMap<String, Object>()
                            .append("stage", stage)
                            .append("courseId", courseId)
                            .append("courseName", courseName)
                            .append("fromType", KnowledgeMappingType.XKW.getType())
                            .append("fromTypeName", KnowledgeMappingType.XKW.getTypeName())
                            .append("fromKey", fromKey)
                            .append("fromName", fromName)
                            .append("toType", KnowledgeMappingType.DONGNI.getType())
                            .append("toTypeName", KnowledgeMappingType.DONGNI.getTypeName())
                            .append("toKey", toKey)
                            .append("toName", toName)
                    );
                }
                return true;
            });
        }
    }
    
    private void masterUpdate(long courseId, Supplier<Boolean> supplier) {
        String dataVersionBusinessType = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessType();
        String dataVersionBusinessKey = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessKey(courseId);
        String dataVersionBusinessDesc = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessDesc(courseId);
        tikuBaseDataVersionService.masterUpdating(dataVersionBusinessType, dataVersionBusinessKey, dataVersionBusinessDesc);
        Boolean versionIncrement = supplier.get();
        tikuBaseDataVersionService.masterUpdated(dataVersionBusinessType, dataVersionBusinessKey, versionIncrement);
    }



}
