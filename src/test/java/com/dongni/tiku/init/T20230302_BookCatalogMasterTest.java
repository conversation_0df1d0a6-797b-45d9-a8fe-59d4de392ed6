package com.dongni.tiku.init;

import com.dongni.tiku.catalog.service.TextbookCatalogMasterServiceImpl;
import com.dongni.tiku.catalog.service.TextbookCatalogMasterThirdFactory;
import com.dongni.tiku.common.util.MapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> <br>
 * 2023/03/02 <br>
 *
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class T20230302_BookCatalogMasterTest {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(T20230302_BookCatalogMasterTest.class);
    
    @Autowired
    private TextbookCatalogMasterThirdFactory textbookCatalogMasterThirdFactory;
    @Autowired
    private TextbookCatalogMasterServiceImpl textbookCatalogMasterService;
    
    // @Test
    public void init() {
        for (long courseId : textbookCatalogMasterThirdFactory.getKeySet()) {
            try {
                textbookCatalogMasterService.syncByCourseId(MapUtil.of(
                        "userId", 2L,
                        "userName", "系统:题库管理员",
                        "userType", 7,
                        "courseId", courseId
                ));
            } catch (Exception e) {
                LOGGER.warn("{}", courseId, e);
            }
        }
    }
    
}
