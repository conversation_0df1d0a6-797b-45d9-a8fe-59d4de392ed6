package com.dongni.tiku.init;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.service.KnowledgeMasterServiceImpl;
import com.dongni.tiku.own.service.impl.KnowledgeMasterXkwServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> <br>
 * 2022/03/07 <br>
 * 用于生成母版知识点
 *
 * 存储到mongo.tiku.knowledgeMaster
 * 学科网 {@link KnowledgeMasterXkwServiceImpl#getKeySet() }
 *
 * 由于知识点引用的是_id，所有的系统必须保持_id一致
 * 但是第三方的数据无法生成唯一的_id
 * 该程序执行成功之后，保留唯一数据，所有系统的上面的课程都是该处生成的数据的拷贝
 *
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class T20220307_KnowledgeMaster {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(T20220307_KnowledgeMaster.class);
    
    @Autowired
    private KnowledgeMasterServiceImpl knowledgeMasterService;
    
//    @Test
    public void initKnowledge() {
        knowledgeMasterService.syncKnowledgeAll();
    }
    
//    @Test
    public void syncKnowledge() {
        knowledgeMasterService.syncKnowledgeByCourseId(MapUtil.of(
                "userId", 2L,
                "userName", "系统:题库管理员",
                "userType", 7,
                "courseId", 2L
        ));
    }
}
