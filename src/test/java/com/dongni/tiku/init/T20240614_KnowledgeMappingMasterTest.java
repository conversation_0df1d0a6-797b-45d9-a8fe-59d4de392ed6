package com.dongni.tiku.init;

import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.own.enumeration.KnowledgeMappingType;
import com.dongni.tiku.own.service.KnowledgeMappingMasterServiceImpl;
import com.dongni.tiku.own.service.KnowledgeMasterServiceImpl;
import com.dongni.tiku.own.service.OwnKnowledgeService;
import com.dongni.tiku.own.service.TikuBaseDataVersionService;
import com.pugwoo.wooutils.string.RegexUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toMap;

/**
 *
 * <AUTHOR>
 * 2023/03/22
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class T20240614_KnowledgeMappingMasterTest {
    
    private static final Logger log = LoggerFactory.getLogger(T20240614_KnowledgeMappingMasterTest.class);

    @Autowired
    private TikuMongodb tikuMongodb;
    @Autowired
    private OwnKnowledgeService ownKnowledgeService;
    @Autowired
    private KnowledgeMasterServiceImpl knowledgeMasterService;
    @Autowired
    private KnowledgeMappingMasterServiceImpl knowledgeMappingMasterService;
    @Autowired
    private TikuBaseDataVersionService tikuBaseDataVersionService;
    
    /**
     *
     * 新的知识点体系
     *    全部采用学科网，已经同步到knowledgeMaster
     *    并产生knowledgeMappingMaster数据，即 XKW -> DN
     *
     * 原来的知识点信息:
     *   courseId knowledge 17(knowledge) jyeoo(bak_20240614_knowledgeMaster)
     *     12        1328       1328
     *     13        1177       1177
     *     14         301                      301
     *     15         559        559
     *     16         518        518
     *     17         816        816
     *     18         934        934
     *     19         670        670
     *     20        1497       1497
     *     22         199                      199
     *     23         764                      764
     *     24         251                      251
     *  * 一起作业网的知识点直接使用的一起作业网的知识点id，所以没有母版数据
     *  * 菁优网的知识点同步到主库母版数据，已经备份到bak_20240614_knowledgeMaster，
     *    之后被同步到各个环境的knowledge，比对过数据，可以直接用
     *
     * 原来的知识点映射到学科网的知识点关系 excel提供
     *   DONGNI_OLD_20240614 -> XKW
     *
     *  对于现有的数据，整理mapping数据
     *  courseId  fromType                fromKey         toType  toKey
     *   12        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   13        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   14        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   15        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   16        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   17        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   18        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   19        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   20        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   22        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   23        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   24        XKW                     xkw.id         DONGNI  knowledgeMaster._id   同步母版数据时已经生产好
     *   12        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   13        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   14        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   15        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   16        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   17        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   18        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   19        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   20        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   22        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   23        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   24        DONGNI_OLD_20240614     knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   12        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   13        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   15        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   16        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   17        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   18        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   19        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     *   20        YIQI                    knowledge._id  DONGNI  knowledgeMaster._id   通过excel关系(DONGNI_OLD_20240614 -> XKW)转
     */
    @Test
    public void initKnowledgeMapMaster() {
        List<Long> courseIdList = Stream.of(12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24)
                .map(MapUtil::getLongNullable)
                .filter(Objects::nonNull)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
                
        // 读取映射关系excel
        String dirPath  = "/home/<USER>/data/knowledgeMapping";
        Map<Long, File> courseId2ExcelFile = getCourseId2ExcelFile(dirPath);
        for (Long courseId : courseIdList) {
            File file = courseId2ExcelFile.get(courseId);
            if (file == null) {
                throw new RuntimeException("没有找到对应的excel文件：" + courseId);
            }
            System.out.println(courseId + " -> " + file.getName());
        }
        
        int sheetIndexInExcel = 3;
        Map<Long, List<Map<String, Object>>> courseId2ExcelMappingInfoList = new HashMap<>();
        for (Long courseId : courseIdList) {
            int totalCount = 0;
            int deleteCount = 0;
            int removeCount = 0;
            int normalCount = 0;
            int suspiciousCount = 0;
            File file = courseId2ExcelFile.get(courseId);
            // System.out.println(courseId + " -> " + file.getName());
            List<String> header = ExcelUtil.getHeader(file, sheetIndexInExcel, 0);
            // System.out.println(header);
            
            List<Map<String, Object>> excelMappingInfoList = new ArrayList<>();
            List<Map<String, String>> mappingSourceList = ExcelUtil.getBody(file, header, sheetIndexInExcel);
            for (Map<String, String> mappingSource : mappingSourceList) {
                totalCount++;
                String oldKnowledgeId = MapUtil.getTrimNullable(mappingSource.get("知识点编号"));
                String oldKnowledgeName = MapUtil.getTrimNullable(mappingSource.get("知识点名称"));
                String xkwKnowledgeId = MapUtil.getTrimNullable(mappingSource.get("匹配学科网知识点编号"));
                String xkwKnowledgeName = MapUtil.getTrimNullable(mappingSource.get("学科网知识点名称"));
                String comment = MapUtil.getTrimNullable(mappingSource.get("最终结论"));
                if (StringUtils.isNotBlank(comment)) {
                    if ("删除".equals(comment)) {
                        // log.warn("【删除】{}", JSONUtil.toJson(mappingSource));
                        deleteCount++;
                        continue;
                    }
                    log.error("【未知】{}", JSONUtil.toJson(mappingSource));
                    return;
                }
                if (StringUtils.isBlank(xkwKnowledgeId)) {
                    log.warn("【移除】{}", JSONUtil.toJson(mappingSource));
                    removeCount++;
                    continue;
                }
                if (MapUtil.getLongNullable(xkwKnowledgeId) == null) {
                    log.warn("【可疑】{}", JSONUtil.toJson(mappingSource));
                    suspiciousCount++;
                    continue;
                }
                // log.info("【正常】{}", JSONUtil.toJson(mappingSource));
                normalCount++;
                excelMappingInfoList.add(new Document()
                        .append("courseId", courseId)
                        .append("oldKnowledgeId", oldKnowledgeId)
                        .append("oldKnowledgeName", oldKnowledgeName)
                        .append("xkwKnowledgeId", xkwKnowledgeId)
                        .append("xkwKnowledgeName", xkwKnowledgeName)
                        .append("mappingSource", JSONUtil.toJson(mappingSource))
                );
                
            }
            log.info("courseId: {}; fileName: {}; totalCount: {}; normalCount: {}; deleteCount: {}; removeCount: {}; suspiciousCount: {}",
                    courseId, file.getName(), totalCount, normalCount, deleteCount, removeCount, suspiciousCount);
            courseId2ExcelMappingInfoList.put(courseId, excelMappingInfoList);
        }
        
        Map<Long, List<Map<String, Object>>> courseId2KnowledgeList = new HashMap<>();
        Map<Long, Map<String, Map<String, Object>>> courseId2KnowledgeId2Knowledge = new HashMap<>();
        
        Map<Long, List<Map<String, Object>>> courseId2KnowledgeMasterList = new HashMap<>();
        Map<Long, Map<String, Map<String, Object>>> courseId2ThirdKey2KnowledgeMaster = new HashMap<>();
        
        for (Long courseId : courseIdList) {
            List<Map<String, Object>> knowledgeList = ownKnowledgeService.getKnowledgeByCourseId(MapUtil.of("courseId", courseId));
            Map<String, Map<String, Object>> knowledgeId2Knowledge = knowledgeList.stream()
                    .collect(toMap(item -> MapUtil.getTrim(item, "_id"), item -> item));
            courseId2KnowledgeList.put(courseId, knowledgeList);
            courseId2KnowledgeId2Knowledge.put(courseId, knowledgeId2Knowledge);
            
            List<Map<String, Object>> knowledgeMasterList = knowledgeMasterService.getKnowledgeMasterList(MapUtil.of("courseId", courseId));
            Map<String, Map<String, Object>> thirdKey2KnowledgeMaster = knowledgeMasterList.stream()
                    .collect(toMap(item -> MapUtil.getTrim(item, "thirdKey"), item -> item));
            courseId2KnowledgeMasterList.put(courseId, knowledgeMasterList);
            courseId2ThirdKey2KnowledgeMaster.put(courseId, thirdKey2KnowledgeMaster);
        }
        
        Set<Long> yiqiCourseIdSet = Stream.of(12, 13, 15, 16, 17, 18, 19, 20)
                .map(MapUtil::getLongNullable)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Map<Long, List<Map<String, Object>>> courseId2KnowledgeMappingMasterList = new HashMap<>();
        
        // dongni-old -> dongni     即 old -> xkw -> dongni
        // [17 -> dongni]           即 17 -> xkw -> dongni
        for (Long courseId : courseIdList) {
            List<Map<String, Object>> knowledgeMappingMasterList = new ArrayList<>();
            List<Map<String, Object>> excelMappingInfoList = courseId2ExcelMappingInfoList.get(courseId);
            Map<String, Map<String, Object>> oldKnowledgeId2Knowledge = courseId2KnowledgeId2Knowledge.get(courseId);
            Map<String, Map<String, Object>> thirdKey2KnowledgeMaster = courseId2ThirdKey2KnowledgeMaster.get(courseId);
            if (CollectionUtils.isEmpty(excelMappingInfoList) || MapUtils.isEmpty(oldKnowledgeId2Knowledge) || MapUtils.isEmpty(thirdKey2KnowledgeMaster)) {
                return;
            }
            
            for (Map<String, Object> excelMappingInfo : excelMappingInfoList) {
                // 旧的知识点信息
                String oldKnowledgeId = MapUtil.getTrim(excelMappingInfo, "oldKnowledgeId");
                Map<String, Object> oldKnowledgeInfo = oldKnowledgeId2Knowledge.get(oldKnowledgeId);
                String oldKnowledgeName = MapUtil.getTrimNullable(oldKnowledgeInfo, "knowledgeName");
                if (StringUtils.isBlank(oldKnowledgeName)) {
                    oldKnowledgeName = MapUtil.getTrimNullable(excelMappingInfo, "oldKnowledgeName");
                }
                if (StringUtils.isBlank(oldKnowledgeName)) {
                    log.error("知识点名称为空: courseId: {}; excelMappingInfo: {}", courseId, excelMappingInfo);
                    return;
                }
                
                // 映射到的学科网信息
                String xkwKnowledgeId = MapUtil.getTrim(excelMappingInfo, "xkwKnowledgeId");
                String thirdKey = "xkw-" + xkwKnowledgeId;
                Map<String, Object> knowledgeMaster = thirdKey2KnowledgeMaster.get(thirdKey);
                if (MapUtils.isEmpty(knowledgeMaster)) {
                    log.warn("找不到学科网知识点信息,丞文说不要了: courseId: {}; excelMappingInfo: {}", courseId, excelMappingInfo);
                    continue;
                }
                
                // 找到学科网信息，则可以在母版文件中找到新的知识点信息
                int stage = MapUtil.getInt(knowledgeMaster, "stage");
                String courseName = MapUtil.getTrim(knowledgeMaster, "courseName");
                String knowledgeId = MapUtil.getTrim(knowledgeMaster, "_id");
                String knowledgeName = MapUtil.getTrim(knowledgeMaster, "knowledgeName");
                
                // DONGNI_OLD_20240614 -> DONGNI
                knowledgeMappingMasterList.add(new Document()
                        .append("stage", stage)
                        .append("courseId", courseId)
                        .append("courseName", courseName)
                        .append("fromType", KnowledgeMappingType.DONGNI_OLD_20240614.getType())
                        .append("fromTypeName", KnowledgeMappingType.DONGNI_OLD_20240614.getTypeName())
                        .append("fromKey", oldKnowledgeId)
                        .append("fromName", oldKnowledgeName)
                        .append("toType", KnowledgeMappingType.DONGNI.getType())
                        .append("toTypeName", KnowledgeMappingType.DONGNI.getTypeName())
                        .append("toKey", knowledgeId)
                        .append("toName", knowledgeName)
                );
                if (yiqiCourseIdSet.contains(courseId)) {
                    // YIQI -> DONGNI
                    knowledgeMappingMasterList.add(new Document()
                            .append("stage", stage)
                            .append("courseId", courseId)
                            .append("courseName", courseName)
                            .append("fromType", KnowledgeMappingType.YIQI.getType())
                            .append("fromTypeName", KnowledgeMappingType.YIQI.getTypeName())
                            .append("fromKey", oldKnowledgeId)
                            .append("fromName", oldKnowledgeName)
                            .append("toType", KnowledgeMappingType.DONGNI.getType())
                            .append("toTypeName", KnowledgeMappingType.DONGNI.getTypeName())
                            .append("toKey", knowledgeId)
                            .append("toName", knowledgeName)
                    );
                }
            }
            courseId2KnowledgeMappingMasterList.put(courseId, knowledgeMappingMasterList);
        }
        
        for (Long courseId : courseIdList) {
            List<Map<String, Object>> knowledgeMappingMasterList = courseId2KnowledgeMappingMasterList.get(courseId);
            knowledgeMappingMasterList.sort(ComparatorEx
                    .<Map<String, Object>, Long>asc(item -> MapUtil.getLong(item, "courseId"))
                    .thenAsc(item -> MapUtil.getInt(item, "fromType"))
            );
            
            masterUpdate(courseId, () -> {
                for (Map<String, Object> knowledgeMappingMaster : knowledgeMappingMasterList) {
                    knowledgeMappingMasterService.insertKnowledgeMapping(knowledgeMappingMaster);
                }
                return true;
            });
        }
    }
    
    private void masterUpdate(long courseId, Supplier<Boolean> supplier) {
        String dataVersionBusinessType = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessType();
        String dataVersionBusinessKey = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessKey(courseId);
        String dataVersionBusinessDesc = KnowledgeMappingMasterServiceImpl.getDataVersionBusinessDesc(courseId);
        tikuBaseDataVersionService.masterUpdating(dataVersionBusinessType, dataVersionBusinessKey, dataVersionBusinessDesc);
        Boolean versionIncrement = supplier.get();
        tikuBaseDataVersionService.masterUpdated(dataVersionBusinessType, dataVersionBusinessKey, versionIncrement);
    }
    
    private Map<Long, File> getCourseId2ExcelFile(String dirPath) {
        Map<Long, File> courseId2ExcelFile = new HashMap<>();
        File dir = new File(dirPath);
        File[] files = dir.listFiles();
        if (files == null) {
            return courseId2ExcelFile;
        }
        for (File file : files) {
            Long courseId = getCourseId(file);
            if (courseId == null) {
                continue;
            }
            courseId2ExcelFile.put(courseId, file);
        }
        return courseId2ExcelFile;
    }
    
    private Long getCourseId(File excelFile) {
        String fileName = excelFile.getName();
        fileName = fileName.replace(".xls", "");
        String[] split = fileName.split("_");
        if (split.length != 3) {
            return null;
        }
        String courseInfo = split[2];
        String courseIdStr = RegexUtils.getFirstMatchStr(courseInfo, "(\\d+)");
        return MapUtil.getLongNullable(courseIdStr);
    }



}
