package com.dongni.tiku.init;

import com.dongni.tiku.catalog.service.TextbookCatalogMasterServiceImpl;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.KnowledgeMasterManager;
import com.dongni.tiku.manager.impl.TextbookCatalogMasterManager;
import com.dongni.tiku.manager.impl.TextbookMasterManager;
import com.dongni.tiku.manager.impl.TextbookVersionMasterManager;
import com.dongni.tiku.own.service.KnowledgeMasterServiceImpl;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 * 2023/03/14
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class T20230314_FixMasterDataTest {
    
    @Autowired
    private KnowledgeMasterManager knowledgeMasterManager;
    @Autowired
    private KnowledgeMasterServiceImpl knowledgeMasterService;
    
    @Test
    public void fixKnowledgeMaster() {
        knowledgeMasterManager.getFindIterable().forEach((Consumer<? super Document>) knowledge -> {
            if (MapUtil.getLongNullable(knowledge, "courseId") == null) { return; }
            Document document = knowledgeMasterService.convertDocumentKnowledge(knowledge);
            // knowledgeMasterManager.replace(document);
        });
    }
    
    @Autowired
    private TextbookVersionMasterManager textbookVersionMasterManager;
    @Autowired
    private TextbookMasterManager textbookMasterManager;
    @Autowired
    private TextbookCatalogMasterManager textbookCatalogMasterManager;
    @Autowired
    private TextbookCatalogMasterServiceImpl textbookCatalogMasterService;
    
    @Test
    public void fixTextbookCatalogMaster() {
        textbookVersionMasterManager.getFindIterable().forEach((Consumer<? super Document>) textbookVersion -> {
            if (MapUtil.getLongNullable(textbookVersion, "courseId") == null) { return; }
            Document document = textbookCatalogMasterService.convertDocumentTextbookVersion(textbookVersion);
            // textbookVersionMasterManager.replace(document);
        });
    
        textbookMasterManager.getFindIterable().forEach((Consumer<? super Document>) textbook -> {
            if (MapUtil.getLongNullable(textbook, "courseId") == null) { return; }
            Document document = textbookCatalogMasterService.convertDocumentTextbook(textbook);
            // textbookMasterManager.replace(document);
        });
    
        textbookCatalogMasterManager.getFindIterable().forEach((Consumer<? super Document>) textbookCatalog -> {
            if (MapUtil.getLongNullable(textbookCatalog, "courseId") == null) { return; }
            Document document = textbookCatalogMasterService.convertDocumentTextbookCatalog(textbookCatalog);
            // textbookCatalogMasterManager.replace(document);
        });
    }
}

