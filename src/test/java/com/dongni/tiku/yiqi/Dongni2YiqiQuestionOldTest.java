package com.dongni.tiku.yiqi;

import com.dongni.common.utils.CdnUtil;
import com.dongni.common.utils.FileUtil;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.utils.Base64Util;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.RegexUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.tree.DefaultAttribute;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/11 <br/>
 *
 */
public class Dongni2YiqiQuestionOldTest {
    
    /**
     * TODO 处理文本
     *
     * @param text
     * @return
     */
    private static String handleText(Object text) {
        if (ObjectUtil.isBlank(text)) {
            return "";
        }
        
        text = text.toString()
                .replaceAll("<br([\\s\\S]*?)>", "")
                .replaceAll("<p([\\s\\S]*?)>", "<p>")
                .replaceAll("<p>", "<p><str>")
                .replaceAll("</p>", "</str></p>")
                .replaceAll("\\\\ce", "")
                .replaceAll("\\[\\[nn]]", "<blank/>")
                .replaceAll("\\\\\\[", "\\\\\\(")
                .replaceAll("\\\\]", "\\\\)")
                .replaceAll("\\n", "")
                .replaceAll("<bdo([\\s\\S]*?)</bdo>", "<blank/>")
                .replaceAll("&nbsp;", " ")
                .replaceAll("<a([\\s\\S]*?)>", "")
                .replaceAll("</a([\\s\\S]*?)>", "")
                .replaceAll("<iframe([\\s\\S]*?)>", "")
                .replaceAll("</iframe([\\s\\S]*?)>", "")
                .replaceAll("<o:p([\\s\\S]*?)>", "")
                .replaceAll("</o:p>", "")
                .replaceAll("<nobr([\\s\\S]*?)>", "")
                .replaceAll("</nobr>", "");
        
        try {
            org.dom4j.Document document = DocumentHelper.parseText("<root>" + text.toString() + "</root>");
            text = transferTagStyle(document.getRootElement(), text.toString());
        } catch (Exception e) {
//            log.info("解析xml失败：e={}", e);
        }
        
        //        // 加粗
        //        List<String> strongList = RegexUtils.getAllMatchStr(text.toString(), "<strong[\\s\\S]*?>([\\s\\S]*?)</strong>");
        //        for (String strong : strongList) {
        //            text = text.toString().replaceAll("<strong[\\s\\S]*?>" + strong + "</strong>", "<str font-weight=\"bold\">" + strong + "</str>");
        //        }
        //
        //        // 斜体
        //        List<String> emList = RegexUtils.getAllMatchStr(text.toString(), "<em[\\s\\S]*?>([\\s\\S]*?)</em>");
        //        for (String em : emList) {
        //            text = text.toString().replaceAll("<em[\\s\\S]*?>" + em + "</em>", "<str font-style=\"italic\">" + em + "</str>");
        //        }
        //
        //        // 下划线
        //        List<String> underlineList = RegexUtils.getAllMatchStr(text.toString(), "<span[\\s\\S]*?style=\"text-decoration:[\\s\\S]*?underline;[\\s\\S]*?\"[\\s\\S]*?>([\\s\\S]*?)</span>");
        //        for (String underline : underlineList) {
        //            text = text.toString().replaceAll("<span[\\s\\S]*?style=\"text-decoration:[\\s\\S]*?underline;[\\s\\S]*?\"[\\s\\S]*?>" + underline + "</span>", "<str underline-style=\"solid\">" + underline + "</str>");
        //        }
        
        // 去除多余样式
        text = text.toString()
                .replaceAll("<span[\\s\\S]*?>", "")
                .replaceAll("</span[\\s\\S]*?>", "");
        
        String cdnUrlHttp = CdnUtil.getCdnHttp();
        
        // 处理<img>
        List<String> allImgTag = RegexUtils.getAllMatchStr(text.toString(), "<img([\\s\\S]+?)>");
        for (String imgTag : allImgTag) {
            imgTag = imgTag.trim();
            
            // style
            String newStyle = "";
            Map<String, Object> styleMap = new HashMap<>();
            String styleStr = RegexUtils.getFirstMatchStr(imgTag, "style=\"([\\s\\S]+?)\"");
            if (!ObjectUtil.isBlank(styleStr)) {
                String[] items = styleStr.split(";");
                for (String item : items) {
                    if (ObjectUtil.isBlank(item)) {
                        continue;
                    }
                    
                    if (item.split(":").length == 2) {
                        String key = item.split(":")[0];
                        String value = item.split(":")[1];
                        if (ObjectUtil.isValueEquals(key, "width") || ObjectUtil.isValueEquals(key, "height")) {
                            styleMap.put(key, value);
                        }
                    }
                }
                
                if (styleMap.size() > 0) {
                    List<String> styleList = new ArrayList<>();
                    styleMap.forEach((key, value) -> {
                        styleList.add(key + ":" + value);
                    });
                    newStyle = "style" + "=" + "\"" + StringUtils.join(styleList, ";") + "\"";
                }
            }
            
            // src
            String newSrc = "";
            String url = RegexUtils.getFirstMatchStr(imgTag, "src=\"([\\s\\S]+?)\"");
            if (url.contains("../../filer")) {
                url = cdnUrlHttp + url.replace("../../filer/", "");
            }
            if (!ObjectUtil.isBlank(url)) {
                url = url.trim();
                
                if (url.contains("base64")) {
                    // base64图片
                    String tempUrl = url;
                    String pictureUrl = FileStorageTemplate.put(fileStoragePut -> {
                        File tempFile = new File(fileStoragePut.getRootPath() +
                                UUID.randomUUID().toString().replaceAll("-", "") + ".png");
                        File file = Base64Util.base64ToImage(tempUrl, tempFile.getAbsolutePath());
                        fileStoragePut.setAutoExpire(true);
                        fileStoragePut.setLocalFile(file);
                    });
                    
                    
                    String newUrl = cdnUrlHttp + pictureUrl;
                    newSrc = "src=\"" + newUrl + "\"";

                    /*try {
                        tempFile.deleteOnExit();
                        file.deleteOnExit();
                    } catch (Exception e) {
                        LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                    }*/
                    
                } else if (!url.contains(cdnUrlHttp)) {
                    // 不是懂你cdn的图片，先上传到懂你cdn
                    if (!url.contains("http") && !url.contains("https")) {
                        url = "http:" + url;
                    }
                    String tempUrl = url;
                    String pictureUrl = FileStorageTemplate.put(fileStoragePut -> {
                        File tempFile = new File(fileStoragePut.getRootPath()+
                                UUID.randomUUID().toString().replaceAll("-", "") + ".png");
                        File file = FileUtil.downFile(tempUrl,tempFile);
                        fileStoragePut.setAutoExpire(true);
                        fileStoragePut.setLocalFile(file);
                    });
                    String newUrl = cdnUrlHttp + pictureUrl;
                    newSrc = "src=\"" + newUrl + "\"";

                   /* try {
                        file.deleteOnExit();
                    } catch (Exception e) {
                        LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                    }*/
                    
                } else if (!url.contains("http") && !url.contains("https")) {
                    String newUrl = "http:" + url;
                    newSrc = "src=\"" + newUrl + "\"";
                } else {
                    newSrc = "src=\"" + url + "\"";
                }
            }
            
            if (!ObjectUtil.isBlank(newSrc)) {
                text = text.toString().replace(imgTag, " " + newSrc + " " + newStyle + " /");
            }
        }
        
        text = text.toString().replaceAll("</img>", "");
        text = text.toString().replaceAll("<img[ ]*/>", "");
        
        // 转换[[img]]
        List<String> allImgStr = RegexUtils.getAllMatchStr(text.toString(), "\\[\\[img]]([\\s\\S]+?)\\[\\[\\/img]]");
        for (String item : allImgStr) {
            
            Map<String, Object> styleMap = new HashMap<>();
            
            Map<String, Object> map = JSON.parseToMap(item);
            List<String> arr = new ArrayList<>();
            map.forEach((key, value) -> {
                if (ObjectUtil.isValueEquals(key, "width") || ObjectUtil.isValueEquals(key, "height")) {
                    styleMap.put(key, value);
                } else if (ObjectUtil.isValueEquals(key, "src")) {
                    if (!value.toString().contains("http:")) {
                        value = "http:" + value;
                    }
                    arr.add(key + "=" + "\"" + value + "\"");
                }
            });
            
            if (styleMap.size() > 0) {
                List<String> styleList = new ArrayList<>();
                styleMap.forEach((key, value) -> {
                    styleList.add(key + ":" + value);
                });
                arr.add("style" + "=" + "\"" + StringUtils.join(styleList, ";") + "\"");
            }
            
            text = text.toString().replace(item, "<img " + StringUtils.join(arr, " ") + "></img>");
        }
        text = text.toString().replaceAll("\\[\\[img]]", "")
                .replaceAll("\\[\\[/img]]", "");
        
        // 转换公式里的'<''>'
        List<String> mathJaxList = RegexUtils.getAllMatchStr(text.toString(), "\\\\\\(([\\s\\S]+?)\\\\\\)");
        if (CollectionUtils.isNotEmpty(mathJaxList)) {
            for (String item : mathJaxList) {
                String replaceStr = item
                        .replace("[ ]", "")
                        .replace("&", "&amp;")
                        .replace("'", "&apos;")
                        .replace("\"", "&quot;")
                        .replace("<", "&lt;")
                        .replace(">", "&gt;");
                text = text.toString().replace("\\(" + item + "\\)", "\\(" + replaceStr + "\\)");
            }
            
        }
        
        // 没有p标签包裹的字符串使用p标签包裹
        String[] otherTextList = text.toString().replaceAll("<p>([\\s\\S]*?)</p>", "!my!").split("!my!");
        if (otherTextList.length > 0) {
            for (String otherText : otherTextList) {
                if (!ObjectUtil.isBlank(otherText)) {
                    text = text.toString().replace(otherText, "<p><str>" + otherText + "</str></p>");
                }
            }
        }
        
        if (text.toString().indexOf("<p><str>") != 0) {
            text = "<p><str>" + text + "</str></p>";
        }
        
        // <p><str><str ...>...</str></str></p> => <p><str ...>...</str></p>
        List<String> pList = RegexUtils.getAllMatchStr(text.toString(), "<p>([\\s\\S]+?)</p>");
        for (String pStr : pList) {
            if (pStr.indexOf("<str><str") == 0 && pStr.lastIndexOf("</str></str>") == pStr.length() - 12) {
                String newPStr = "<str> " + pStr.substring(5);
                text = text.toString().replace(pStr, newPStr);
            }
        }
        
        return text.toString();
    }
    
    /**
     * 转换标签样式
     *
     * @param element
     * @param text
     * @return
     */
    private static String transferTagStyle(Element element, String text) {
        
        String name = element.getQName().getName();
        String innerText = element.asXML();
        
        if (ObjectUtil.isValueEquals(name, "strong")) {
            // 加粗
            String newInnerText = innerText.replaceAll("<strong[\\s\\S]*?>", "");
            newInnerText = newInnerText.replaceAll("</strong>", "");
            newInnerText = "<str font-weight=\"bold\">" + newInnerText + "</str>";
            text = text.replace(innerText, newInnerText);
            
        } else if (ObjectUtil.isValueEquals(name, "em")) {
            // 斜线
            String newInnerText = innerText.replaceAll("<em[\\s\\S]*?>", "");
            newInnerText = newInnerText.replaceAll("</em>", "");
            newInnerText = "<str font-style=\"italic\">" + newInnerText + "</str>";
            text = text.replace(innerText, newInnerText);
            
        } else if (ObjectUtil.isValueEquals(name, "span")) {
            
            boolean isUnderline = false;
            List attributes = element.attributes();
            for (Object attributeObj : attributes) {
                DefaultAttribute attribute = (DefaultAttribute) attributeObj;
                if (attribute.getValue() != null
                        && attribute.getValue().contains("text-decoration")
                        && attribute.getValue().contains("underline")) {
                    
                    // 下划线
                    isUnderline = true;
                }
            }
            
            if (isUnderline) {
                // 是下划线
                String newInnerText = innerText.replaceAll("<span[\\s\\S]*?>", "");
                newInnerText = newInnerText.replaceAll("</span>", "");
                newInnerText = "<str underline-style=\"solid\">" + newInnerText + "</str>";
                text = text.replace(innerText, newInnerText);
            }
            
        }
        
        List content = element.content();
        for (Object item : content) {
            if (item instanceof Element) {
                text = transferTagStyle((Element)item, text);
            }
        }
        
        return text;
    }
}
