package com.dongni.tiku.yiqi;

import com.dongni.common.mongo.IManager;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.third.yiqi.util.YiqiQuestionSearchUtil;
import com.mongodb.Block;
import com.mongodb.client.FindIterable;
import com.mongodb.client.model.ReplaceOptions;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;

/**
 *
 * <AUTHOR>
 * @date 2025/03/19
 */
public class YiqiQuestionAddSearchFieldTest {
    
    private TikuMongodb tikuMongodb;
    
    private void initTikuMongodbProductAliyun() {
        // tikuMongodb = new TikuMongodb();
        // tikuMongodb.setHost("************");
        // tikuMongodb.setPort("34352");
        // tikuMongodb.setDatabase("tiku");
        // tikuMongodb.setUserName("tiku");
        // tikuMongodb.setPassword("Tiku#!@2019");
    }
    
    
    // @Before
    public void initTikuMongodb() {
        initTikuMongodbProductAliyun();
    }
    
    // @Test
    public void addSearchField() {
        IManager yiqiQuestionManager = new IManager(tikuMongodb, TikuMongodb.COLLECTION_YIQI_QUESTION, "一起试题") {};
        IManager yiqiQuestionBackManager = new IManager(tikuMongodb, "bak_20250319_yiqiQuestion", "一起试题") {};
        List<Long> courseIdList = Stream.of(2,3,4,5,6,7,8,9,10,12,13,14,15,16,17,18,19,20)
                .map(Integer::longValue)
                .collect(Collectors.toList());
        for (Long courseId : courseIdList) {
            Bson query = and(
                    eq("courseId", courseId),
                    exists("allTagIds", false)
            );
            FindIterable<Document> findIterable = yiqiQuestionManager.getFindIterable(query);
            findIterable.forEach((Block<? super Document>) yiqiQuestion -> {
                yiqiQuestionBackManager.replace(yiqiQuestion, new ReplaceOptions().upsert(true));
                System.out.println(yiqiQuestion.get("_id").toString());
                YiqiQuestionSearchUtil.addSearchField(yiqiQuestion);
                Bson updateQuery = eq("_id", new ObjectId(yiqiQuestion.get("_id").toString()));
                Document update = new Document()
                        .append("courseId", yiqiQuestion.get("courseId"))
                        .append("years", yiqiQuestion.get("years"))
                        .append("provinces", yiqiQuestion.get("provinces"))
                        .append("type", yiqiQuestion.get("type"))
                        .append("subtype", yiqiQuestion.get("subtype"))
                        .append("difficulty", yiqiQuestion.get("difficulty"))
                        .append("allTagIds", yiqiQuestion.get("allTagIds"));
                yiqiQuestionManager.updateOne(updateQuery, new Document("$set", update));
            });
        }
    }
    
}
