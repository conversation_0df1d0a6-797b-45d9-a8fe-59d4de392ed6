package com.dongni.tiku.yiqi;

import com.dongni.commons.utils.verify.ObjectUtil;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.RegexUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.tree.DefaultAttribute;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> <br/>
 * @date 2020/05/09 <br/>
 *
 */
public class Dongni2YiqiQuestionTest {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(Dongni2YiqiQuestionTest.class);
    
    public static void main(String[] args) {
        System.out.println("main:10  "
                + handleText(
                "<p><strong>壹</strong><em>贰</em><span style=\"text-decoration: underline;\">叁</span><em><strong>肆</strong></em><span style=\"text-decoration: underline;\"><strong>伍</strong><em>陆</em></span>柒<strong>一<em>二</em>三<span style=\"text-decoration: underline;\">四<img src=\"//cdn.dongni100.com/xxx\" />五</span></strong><span style=\"text-decoration: underline;\">六<em>七</em><em><strong><img src=\"https://cdn.dongni100.com/xxx\" />捌<img src=\"xxx\" /><img src=\"xxx\" />玖零</strong></em></span></p>"
                //                        + "asfsd"
                //                        + "<p><strong>壹</strong><em>贰</em><span style=\"text-decoration: underline;\"> 叁</span><em><strong>肆</strong></em><span style=\"text-decoration: underline;\"><strong>伍</strong><em>陆</em><em><strong>柒<img src=\"xxx\" />捌<img src=\"xxx\" /><img src=\"xxx\" />玖零</strong></em></span></p>"
        ));
        
        System.out.println("main:10  "
                + handleText(
                "<p><img src=\"//cdn.dongni100.com/upload/entrust/79019/1588686662039_038.png\" /></p>\n<p><img src=\"//cdn.dongni100.com/upload/entrust/79019/1588686685701_006.png\" /></p>"));
        
    }
    
    /**
     * 转换文本 懂你一起试题的题干/选项等都需要进行转换
     *
     * @param text 懂你 题干/选项等数据
     * @return 一起的数据
     */
    private static String handleText(String text) {
        if (ObjectUtil.isBlank(text)) {
            return "";
        }
        
        // 去除大部分样式
        text = text
                .replaceAll("<br([\\s\\S]*?)>", "")
                .replaceAll("<p([\\s\\S]*?)>", "<p>")
                .replaceAll("\\\\ce", "")
                .replaceAll("\\[\\[nn]]", "<blank/>")
                .replaceAll("\\\\\\[", "\\\\\\(")
                .replaceAll("\\\\]", "\\\\)")
                .replaceAll("\\n", "")
                .replaceAll("<bdo([\\s\\S]*?)</bdo>", "<blank/>")
                .replaceAll("&nbsp;", " ")
                .replaceAll("<a([\\s\\S]*?)>", "")
                .replaceAll("</a([\\s\\S]*?)>", "")
                .replaceAll("<iframe([\\s\\S]*?)>", "")
                .replaceAll("</iframe([\\s\\S]*?)>", "")
                .replaceAll("<o:p([\\s\\S]*?)>", "")
                .replaceAll("</o:p>", "")
                .replaceAll("<nobr([\\s\\S]*?)>", "")
                .replaceAll("</nobr>", "");
        
        // 没有p标签包裹的字符串使用p标签包裹
        String[] otherTextList = text.replaceAll("<p>([\\s\\S]*?)</p>", "!my!").split("!my!");
        if (otherTextList.length > 0) {
            for (String otherText : otherTextList) {
                if (!ObjectUtil.isBlank(otherText)) {
                    text = text.replace(otherText, "<p><str>" + otherText + "</str></p>");
                }
            }
        }
        
        // 处理img标签 url为公网可直接访问的url base64转为可用的url
        text = transferImageTag(text);
        
        // 处理一起要的自定义的str标签
        text = transferStrTagStyle(text);
        
        // 去除多余样式
        text = text
                .replaceAll("<span[\\s\\S]*?>", "")
                .replaceAll("</span[\\s\\S]*?>", "");
        
        // 处理[[img]]标签
        text = transferImgTag(text);
        
        // 转换公式中其他的字符  比如说大于号 -> &gt;
        text = transferOtherChar(text);
        
        return text;
    }
    
    /**
     * 转换公式中其他的字符  比如说大于号 -> &gt;
     * @param text
     * @return
     */
    private static String transferOtherChar(String text) {
        // 转换公式里的'<''>'
        List<String> mathJaxList = RegexUtils.getAllMatchStr(text, "\\\\\\(([\\s\\S]+?)\\\\\\)");
        if (CollectionUtils.isNotEmpty(mathJaxList)) {
            for (String item : mathJaxList) {
                String replaceStr = item
                        .replace("[ ]", "")
                        .replace("&", "&amp;")
                        .replace("'", "&apos;")
                        .replace("\"", "&quot;")
                        .replace("<", "&lt;")
                        .replace(">", "&gt;");
                text = text.replace("\\(" + item + "\\)", "\\(" + replaceStr + "\\)");
            }
        }
        return text;
    }
    
    /**
     * 处理 html的<img> 标签
     *   处理 style 样式
     *   处理 url / base64
     * @param text
     * @return
     */
    private static String transferImageTag(String text) {
        //        String cdnUrl = SpringContextUtil.getProperty("dongni.file-storage.cdn-url");
        
        String cdnUrl = "//cdn.dongni100.com";
        String cdnUrlHttp = cdnUrl;
        if (!cdnUrlHttp.startsWith("http")) {
            cdnUrlHttp = "http:" + cdnUrl;
        }
        
        // 处理<img>
        List<String> allImgTag = RegexUtils.getAllMatchStr(text, "<img([\\s\\S]+?)>");
        for (String imgTag : allImgTag) {
            imgTag = imgTag.trim();
            
            // style
            String newStyle = "";
            Map<String, Object> styleMap = new HashMap<>();
            String styleStr = RegexUtils.getFirstMatchStr(imgTag, "style=\"([\\s\\S]+?)\"");
            if (!ObjectUtil.isBlank(styleStr)) {
                String[] items = styleStr.split(";");
                for (String item : items) {
                    if (ObjectUtil.isBlank(item)) {
                        continue;
                    }
                    if (item.split(":").length == 2) {
                        String key = item.split(":")[0];
                        String value = item.split(":")[1];
                        if (ObjectUtil.isValueEquals(key, "width") || ObjectUtil.isValueEquals(key, "height")) {
                            styleMap.put(key, value);
                        }
                    }
                }
                if (styleMap.size() > 0) {
                    List<String> styleList = new ArrayList<>();
                    styleMap.forEach((key, value) -> {
                        styleList.add(key + ":" + value);
                    });
                    newStyle = "style" + "=" + "\"" + StringUtils.join(styleList, ";") + "\"";
                }
            }
            
            // src
            String newSrc = "";
            String url = RegexUtils.getFirstMatchStr(imgTag, "src=\"([\\s\\S]+?)\"");
            if (url.contains("../../filer")) {
                url = cdnUrlHttp + url.replace("../../filer", "");
            }
            if (!ObjectUtil.isBlank(url)) {
                url = url.trim();
                
                // base64图片 -> uploadToOss
                if (url.contains("base64")) {
                    String tempUrl = url;
                    //                    String pictureUrl = FileStorageTemplate.put(fileStoragePut -> {
                    //                        File tempFile = new File(fileStoragePut.getRootPath() +
                    //                                UUID.randomUUID().toString().replaceAll("-", "") + ".png");
                    //                        File file = Base64Util.base64ToImage(tempUrl, tempFile.getAbsolutePath());
                    //                        fileStoragePut.setAutoExpire(true);
                    //                        fileStoragePut.setLocalFile(file);
                    //                    });
                    String pictureUrl = "base64";
                    String newUrl = cdnUrlHttp + "/" + pictureUrl;
                    newSrc = "src=\"" + newUrl + "\"";
                }
                
                // 不是懂你cdn的图片，先上传到懂你cdn
                else if (!url.contains(cdnUrl)) {
                    if (!url.startsWith("http") && !url.startsWith("https")) {
                        url = "http:" + url;
                    }
                    String tempUrl = url;
                    //                    String pictureUrl = FileStorageTemplate.put(fileStoragePut -> {
                    //                        File tempFile = new File(fileStoragePut.getRootPath()+
                    //                                UUID.randomUUID().toString().replaceAll("-", "") + ".png");
                    //                        File file = FileUtil.downFile(tempUrl,tempFile);
                    //                        fileStoragePut.setAutoExpire(true);
                    //                        fileStoragePut.setLocalFile(file);
                    //                    });
                    String pictureUrl = "notCdn";
                    String newUrl = cdnUrlHttp + "/" + pictureUrl;
                    newSrc = "src=\"" + newUrl + "\"";
                }
                
                // 不是http或https打头的 添加http   如: //cdn.dongni100.com/xxxx/test.jpg
                else if (!url.startsWith("http") && !url.startsWith("https")) {
                    String newUrl = "http:" + url;
                    newSrc = "src=\"" + newUrl + "\"";
                }
                
                // 正常的url  如: http://cdn.dongni100.com/xxxx/test.jpg
                else {
                    newSrc = "src=\"" + url + "\"";
                }
            }
            
            if (!ObjectUtil.isBlank(newSrc)) {
                text = text.replace(imgTag, " " + newSrc + " " + newStyle + " /");
            }
        }
        
        text = text.replaceAll("</img>", "");
        text = text.replaceAll("<img[ ]*/>", "");
        return text;
    }
    
    /**
     * 处理 [[img]] 标签   不是html的那个<img>
     *      我也不知道是啥 疑似是markdown的标签
     * @param text
     * @return
     */
    private static String transferImgTag(String text) {
        // 转换[[img]]
        List<String> allImgStr = RegexUtils.getAllMatchStr(text, "\\[\\[img]]([\\s\\S]+?)\\[\\[\\/img]]");
        for (String item : allImgStr) {
            
            Map<String, Object> styleMap = new HashMap<>();
            
            Map<String, Object> map = JSON.parseToMap(item);
            List<String> arr = new ArrayList<>();
            map.forEach((key, value) -> {
                if (ObjectUtil.isValueEquals(key, "width") || ObjectUtil.isValueEquals(key, "height")) {
                    styleMap.put(key, value);
                } else if (ObjectUtil.isValueEquals(key, "src")) {
                    if (!value.toString().contains("http:")) {
                        value = "http:" + value;
                    }
                    arr.add(key + "=" + "\"" + value + "\"");
                }
            });
            
            if (styleMap.size() > 0) {
                List<String> styleList = new ArrayList<>();
                styleMap.forEach((key, value) -> {
                    styleList.add(key + ":" + value);
                });
                arr.add("style" + "=" + "\"" + StringUtils.join(styleList, ";") + "\"");
            }
            
            text = text.replace(item, "<img " + StringUtils.join(arr, " ") + "></img>");
        }
        text = text.replaceAll("\\[\\[img]]", "")
                .replaceAll("\\[\\[/img]]", "");
        return text;
    }
    
    /**
     * 转换一起的str标签样式
     */
    private static String transferStrTagStyle(String text) {

/*
<p>
<strong>壹</strong>
<em>贰</em>
<span style=\"text-decoration: underline;\">叁</span>
<em><strong>肆</strong></em>
<span style=\"text-decoration: underline;\">
   <strong>伍</strong>
   <em>陆</em>
</span>
柒
<strong>
    一
    <em>二</em>
    三
    <span style=\"text-decoration: underline;\">
        四
        <img src=\"xxx\" />
        五
    </span>
</strong>
<span style=\"text-decoration: underline;\">
    六
    <em>七</em>
    <em><strong>
        <img src=\"xxx\" />
        捌
        <img src=\"xxx\" />
        <img src=\"xxx\" />
        玖零
    </strong></em>
</span>
</p>
*/
        
        // 标签修正
        text = text.replaceAll("<p>", "<p><str>")
                .replaceAll("</p>", "</str></p>");
    
        System.out.println(text);
        
        // 处理一起作业网要求送 str 样式
        // 使用切割后，利用栈对参数重新拼接
        List<String> pInnerList = RegexUtils.getAllMatchStr(text, "<p>(.*?)</p>");
        Stack<String> tagPrefixStack = new Stack<>();
        StringBuilder newTextSb = new StringBuilder();
        String tempMatch;
        for (String pInner : pInnerList) {
            // 仅利用xml解析进行去除多余的乱七八糟的空格
            try {
                org.dom4j.Document document = DocumentHelper.parseText(pInner);
                String tmpString = document.asXML();
                tmpString = tmpString
                        .replaceAll("\\n", "")
                        .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                ;
                pInner = tmpString;
            } catch (Exception e) {
                LoggerFactory.getLogger(Dongni2YiqiQuestionTest.class).error(e.getMessage(), e);
                LOGGER.warn("懂你试题转一起试题解析xml失败: {}", e.getMessage());
            }
            
            newTextSb.append("<p>");
            if (StringUtils.isNotBlank(pInner)) {
                List<String> tagList = RegexUtils.getAllMatchStr(pInner, "<.*?>");
                List<String> splitList = Stream.of(pInner.split("<.*?>")).collect(toList());
                
                int splitListSize = splitList.size();
                int tagListSize = tagList.size();
                for (int i = 0; i < tagListSize; i++) {
                    String tag = tagList.get(i);
                    
                    // 非标签字符 循环一次标签变了 需要判断是否字符长度>0 如果大于0需要写入
                    String context = i < splitListSize ? splitList.get(i) : null;
                    if (context != null && !context.isEmpty()) {
                        List<String> tagPrefixList = new ArrayList<>(tagPrefixStack);
                        newTextSb.append("<str");
                        tagPrefixList.forEach(tagPrefix -> {
                            if (tagPrefix.contains("strong")) {
                                // 粗体
                                newTextSb.append(" font-weight=\"bold\"");
                            } else if (tagPrefix.contains("em")) {
                                // 斜体
                                newTextSb.append(" font-style=\"italic\"");
                            } else if (tagPrefix.contains("span")
                                    && tagPrefix.contains("text-decoration")
                                    && tagPrefix.contains("underline")) {
                                // 下划线 下划线实体
                                newTextSb.append(" underline-style=\"solid\"");
                            }
                            // // 备用 实心点
                            // newTextSb.append(" em-style=\"dot\"");
                            // // 备用 空心点
                            // newTextSb.append(" em-style=\"open\"");
                            // // 备用 下划线 下划线波浪线
                            // newTextSb.append(" underline-style=\"wave\"");
                            
                        });
                        newTextSb.append(">");
                        newTextSb.append(context);
                        newTextSb.append("</str>");
                    }
    
                    // 匹配 <tagName /> 标签 该标签直接插入
                    tempMatch = RegexUtils.getFirstMatchStr(tag, "<.*?\\/>");
                    if (tempMatch != null) {
                        // 此处与文档描述不符
                        // 文档: 7.转化后<str>标签内不允许再有标签，我认为str内部全部是文本，如果是样式请，提到str的属性里。
                        // 实际还要包一个 <str>
                        newTextSb.append("<str>").append(tag).append("</str>");
                        continue;
                    }
                    
                    // 匹配 </tagName> 结束标签 进行出栈处理
                    tempMatch = RegexUtils.getFirstMatchStr(tag, "<\\/.*?>");
                    if (tempMatch != null) {
                        tagPrefixStack.pop();
                        continue;
                    }
                    
                    // 匹配 <tagName> <tagName xxx="xxx"> 开始标签 最后了不需要匹配了就是他了
                    tagPrefixStack.push(tag);
                }
            }
            newTextSb.append("</p>");
        }
/*
<p>
<str font-weight="bold">壹</str>
<str font-style="italic">贰</str>
<str underline-style="solid">叁</str>
<str font-style="italic" font-weight="bold">肆</str>
<str underline-style="solid" font-weight="bold">伍</str>
<str underline-style="solid" font-style="italic">陆</str>
<str>柒</str>
<str font-weight="bold">一</str>
<str font-weight="bold" font-style="italic">二</str>
<str font-weight="bold">三</str>
<str font-weight="bold" underline-style="solid">四</str>
<img src="xxx" />
<str font-weight="bold" underline-style="solid">五</str>
<str underline-style="solid">六</str>
<str underline-style="solid" font-style="italic">七</str>
<img src="xxx" />
<str underline-style="solid" font-style="italic" font-weight="bold">捌</str>
<img src="xxx" />
<img src="xxx" />
<str underline-style="solid" font-style="italic" font-weight="bold">玖零</str>
</p>
 */
        return newTextSb.toString();
    }
    
    
    /**
     * 转换标签样式
     *
     * @param element
     * @param text
     * @return
     */
    @Deprecated
    private static String transferTagStyleOld(Element element, String text) {
        
        String name = element.getQName().getName();
        String innerText = element.asXML();
        
        if (ObjectUtil.isValueEquals(name, "strong")) {
            // 加粗
            String newInnerText = innerText.replaceAll("<strong[\\s\\S]*?>", "");
            newInnerText = newInnerText.replaceAll("</strong>", "");
            newInnerText = "<str font-weight=\"bold\">" + newInnerText + "</str>";
            text = text.replace(innerText, newInnerText);
            
        } else if (ObjectUtil.isValueEquals(name, "em")) {
            // 斜线
            String newInnerText = innerText.replaceAll("<em[\\s\\S]*?>", "");
            newInnerText = newInnerText.replaceAll("</em>", "");
            newInnerText = "<str font-style=\"italic\">" + newInnerText + "</str>";
            text = text.replace(innerText, newInnerText);
            
        } else if (ObjectUtil.isValueEquals(name, "span")) {
            boolean isUnderline = false;
            List attributes = element.attributes();
            for (Object attributeObj : attributes) {
                DefaultAttribute attribute = (DefaultAttribute) attributeObj;
                if (attribute.getValue() != null
                        && attribute.getValue().contains("text-decoration")
                        && attribute.getValue().contains("underline")) {
                    
                    // 下划线
                    isUnderline = true;
                }
            }
            if (isUnderline) {
                // 是下划线
                String newInnerText = innerText.replaceAll("<span[\\s\\S]*?>", "");
                newInnerText = newInnerText.replaceAll("</span>", "");
                newInnerText = "<str underline-style=\"solid\">" + newInnerText + "</str>";
                text = text.replace(innerText, newInnerText);
            }
            
        }
        List content = element.content();
        for (Object item : content) {
            if (item instanceof Element) {
                text = transferTagStyleOld((Element) item, text);
            }
        }
        return text;
    }
}

//        // 加粗
//        List<String> strongList = RegexUtils.getAllMatchStr(text.toString(), "<strong[\\s\\S]*?>([\\s\\S]*?)</strong>");
//        for (String strong : strongList) {
//            text = text.toString().replaceAll("<strong[\\s\\S]*?>" + strong + "</strong>", "<str font-weight=\"bold\">" + strong + "</str>");
//        }
//
//        // 斜体
//        List<String> emList = RegexUtils.getAllMatchStr(text.toString(), "<em[\\s\\S]*?>([\\s\\S]*?)</em>");
//        for (String em : emList) {
//            text = text.toString().replaceAll("<em[\\s\\S]*?>" + em + "</em>", "<str font-style=\"italic\">" + em + "</str>");
//        }
//
//        // 下划线
//        List<String> underlineList = RegexUtils.getAllMatchStr(text.toString(), "<span[\\s\\S]*?style=\"text-decoration:[\\s\\S]*?underline;[\\s\\S]*?\"[\\s\\S]*?>([\\s\\S]*?)</span>");
//        for (String underline : underlineList) {
//            text = text.toString().replaceAll("<span[\\s\\S]*?style=\"text-decoration:[\\s\\S]*?underline;[\\s\\S]*?\"[\\s\\S]*?>" + underline + "</span>", "<str underline-style=\"solid\">" + underline + "</str>");
//        }
