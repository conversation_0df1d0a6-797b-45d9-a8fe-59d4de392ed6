package com.dongni.tiku.xkw.service.common;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.pugwoo.wooutils.json.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * 2022/07/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class XkwCommonApiTest {
    
    @Autowired
    private XkwCommonApi xkwCommonApi;
    
    @Test
    public void testGetSubjects() {
        System.out.println(JSON.toJson(xkwCommonApi.getSubjects()));
    }
    
    @Test
    public void testGetStages() {
        System.out.println(xkwCommonApi.getStages());
    }
    
    @Test
    public void testGetGrades() {
        
        System.out.println(xkwCommonApi.getGrades(null, null));   // 633
        
        System.out.println(xkwCommonApi.getGrades("G63", null));  // 633
        System.out.println(xkwCommonApi.getGrades("G54", null));  // 543
        
        System.out.println(xkwCommonApi.getGrades("G63", 2));  // 633 小学
        System.out.println(xkwCommonApi.getGrades("G63", 3));  // 633 初中
        System.out.println(xkwCommonApi.getGrades("G63", 4));  // 633 高中
        
        System.out.println(xkwCommonApi.getGrades("G54", 2));  // 543 小学
        System.out.println(xkwCommonApi.getGrades("G54", 3));  // 543 初中
        System.out.println(xkwCommonApi.getGrades("G54", 4));  // 543 高中
    }
    
    @Test
    public void testGetCoursesAll() {
        System.out.println(xkwCommonApi.getCoursesAll());
    }
    
    @Test
    public void testGetCourseKnowledgePoints() {
        System.out.println(xkwCommonApi.getCourseKnowledgePoints(26)); // 高中语文
    }
    
    @Test
    public void testGetQuestionTypes() {
        System.out.println(xkwCommonApi.getQuestionTypes(null));
        System.out.println(xkwCommonApi.getQuestionTypes(26L));         // 高中语文
    }
    
    @Test
    public void testGetCoursesTextbookVersions() {
        System.out.println(xkwCommonApi.getCoursesTextbookVersions(26)); // 高中语文
    }
    
    @Test
    public void testGetTextbooks() {
        // System.out.println(xopBaseApi.getTextbooks(null, null, null, 10000, null));
        // System.out.println(xopBaseApi.getTextbooks(26, null, null, 10000, null));
        System.out.println(xkwCommonApi.getTextbooks(26L, null, 313L, null, 10000));
    }
    
    @Test
    public void testGetTextbooksCatalog() {
        /*
        {
        "id"        : 2800,
        "name"      : "小学语文部编版一年级上册",
        "volume"    : "一年级上册",
        "course_id" : 1,
        "version_id": 539,
        "grade_id"  : 1,
        "term"      : "LAST",
        "ordinal"   : 1
      },
         */
        System.out.println(xkwCommonApi.getTextbooksCatalog(2800));
    }
    
    @Test
    public void getTextbooksCatalogKnowledge() {
        /*
          {
            "direction": "CONTAIN",
            "catalogId": 188071,
            "kpointId": 9211,
            "kpointName": "生物的基本特征及病毒（旧）"
          },
         */
        System.out.println(JSONUtil.toJsonFormatted(xkwCommonApi.getTextbooksCatalogKnowledge(null, 4675L, null)));
    }
    
    @Test
    public void testGetAreasAll() {
        // 行政区域数据
        List<Map<String, Object>> areasAll = xkwCommonApi.getAreasAll();
        
        // 懂你只用到省级
        List<Map<String, Object>> provinceList = areasAll.stream()
                .filter(item -> "PROVINCE".equals(MapUtil.getTrimNullable(item, "level")))
                .collect(Collectors.toList());
        
        System.out.println(JSON.toJson(provinceList));
    }
    
    @Test
    public void testGetQuestionDifficulties() {
        // 获取试题难度等级列表
        List<Map<String, Object>> questionDifficulties = xkwCommonApi.getQuestionDifficulties();
        System.out.println(JSON.toJson(questionDifficulties));
    }
    
    @Test
    public void testGetPaperTypes() {
        // 获取试题难度等级列表
        List<Map<String, Object>> paperTypes = xkwCommonApi.getPaperTypes();
        System.out.println(JSON.toJson(paperTypes));
    }
}
