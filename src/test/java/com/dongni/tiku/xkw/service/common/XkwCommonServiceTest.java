package com.dongni.tiku.xkw.service.common;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.pugwoo.wooutils.json.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * 2022/07/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class XkwCommonServiceTest {
    
    @Autowired
    private XkwCommonService xkwCommonService;
    
    @Test
    public void testGetCourseInfo() {
        xkwCommonService.getRelativeCourseInfoListCacheable().forEach(System.out::println);
    }
    
    @Test
    public void testGetAreaList() {
        xkwCommonService.getRelativeAreaListCacheable().forEach(System.out::println);
    }
    
    @Test
    public void testGetQuestionDifficultyList() {
        xkwCommonService.getQuestionDifficultyListCacheable().forEach(System.out::println);
    }
    
    @Test
    public void testGetQuestionTypeList() {
        xkwCommonService.getQuestionTypeListCacheable(MapUtil.of("courseId", 2)).forEach(System.out::println);
    }
    
    @Test
    public void testGetQuestionTypeTree() {
        System.out.println(JSONUtil.toJson(xkwCommonService.getQuestionTypeTreeCacheable(MapUtil.of("courseId", 2))));
    }
    
    @Test
    public void testGetCourseKnowledgePointList() {
        xkwCommonService.getCourseKnowledgePointListCacheable(MapUtil.of("courseId", 2)).forEach(System.out::println);
    }
    
    @Test
    public void testGetCourseKnowledgePointTree() {
        System.out.println(JSON.toJson(xkwCommonService.getCourseKnowledgePointTreeCacheable(MapUtil.of("courseId", 2))));
    }
    
    @Test
    public void testGetCoursesTextbookVersionList() {
        xkwCommonService.getCoursesTextbookVersionListCacheable(MapUtil.of("courseId", 2)).forEach(System.out::println);
    }
    
    @Test
    public void testGetCoursesTextbookList() {
        xkwCommonService.getTextbookListCacheable(MapUtil.of("courseId", 2, "xkwTextbookVersionId", 313)).forEach(System.out::println);
    }
    
    @Test
    public void testGetTextbooksCatalogList() {
        xkwCommonService.getTextbooksCatalogListCacheable(MapUtil.of("xkwTextbookId", 798)).forEach(System.out::println);
    }
    
    @Test
    public void testGetTextbooksCatalogTree() {
        System.out.println(JSON.toJson(xkwCommonService.getTextbooksCatalogTreeCacheable(MapUtil.of("xkwTextbookId", 798))));
    }
    
    @Test
    public void testGetTextbooksCatalogKnowledge() {
        System.out.println(JSON.toJson(xkwCommonService.getTextbooksCatalogKnowledgeList(4675L)));
    }
    
    @Test
    public void testGetTextbooksCatalogKnowledgeCacheable() {
        System.out.println(JSON.toJson(xkwCommonService.getTextbooksCatalogKnowledgeListCacheable(MapUtil.of("xkwTextbookId", 4675L))));
    }
    
    @Test
    public void getPaperTypeList() {
        System.out.println(JSON.toJson(xkwCommonService.getPaperTypeList()));
    }
}
