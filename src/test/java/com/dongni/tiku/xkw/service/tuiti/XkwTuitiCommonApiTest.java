package com.dongni.tiku.xkw.service.tuiti;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2022/10/19
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest
public class XkwTuitiCommonApiTest {
    
    @Autowired
    private XkwTuitiCommonApi xkwTuitiCommonApi;
    
    @Test
    public void test() {
        
        XkwTuitiCommonApi.XopQuestionsSourcePapersParam param = new XkwTuitiCommonApi.XopQuestionsSourcePapersParam()
                .setQuestionIds(Stream.of(
                                        "2832077713899520",  // 举一反三的 没有试卷来源
                                        "2979243722956800",  // 知识点章节推题的 有试卷来源
                                        "283207771389952"    // 随便输入的，应该是没有这题
                        
                                )
                                .collect(Collectors.toList())
                );
        
        System.out.println(xkwTuitiCommonApi.getQuestionsSourcePapers(param));
        
        /*
[
    {
      "questionId"  : "2979243722956800",
      "sourcePapers": [
        {
          "area" : {
            "id"  : "350403",
            "name": "三元区"
          },
          "title": "福建省三明市一中2021-2022学年高一下学期期中语文试题"
        }
      ]
    },
    {
      "questionId"  : "2832077713899520",
      "sourcePapers": [
        {
          "area" : {
            "id"  : "500103",
            "name": "渝中区"
          },
          "title": "重庆市巴蜀中学2021-2022学年高一上学期第一次月考数学试题"
        }
      ]
    },
    {
      "questionId"  : "283207771389952",
      "sourcePapers": []
    }
  ],
*/
    
    }
}
