package com.dongni.tiku.xkw.service.common;

import com.dongni.common.report.excel.ExcelStyle;
import com.dongni.common.report.excel.ExcelUtil;
import com.dongni.common.report.excel.ExportExcel;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.service.TikuCommonService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.dongni.common.report.excel.ExcelUtil.createCell;

/**
 *
 * <AUTHOR>
 * @date 2023/09/12
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class XkwQuestionTypeTest {
    
    private static final Logger log = LoggerFactory.getLogger(XkwQuestionTypeTest.class);
    
    @Autowired
    private XkwCommonApi xkwCommonApi;
    
    @Autowired
    private XkwCommonService xkwCommonService;
    
    @Autowired
    private TikuCommonService tikuCommonService;
    
    @Test
    public void testQuestionTypeAndRelative() {
        List<Map<String, Object>> relativeCourseInfoList = xkwCommonService.getRelativeCourseInfoList();
        relativeCourseInfoList.sort(ComparatorEx.asc(item -> MapUtil.getLong(item, "courseId")));
        
        List<Map<String, Object>> questionTypeRelationXkwOuterList = tikuCommonService.getQuestionTypeRelationXkwOuterList(null);
        long xkwQuestionTypeId = questionTypeRelationXkwOuterList.stream()
                .map(item -> MapUtil.getLong(item, "xkwQuestionTypeId"))
                .max(Comparator.naturalOrder())
                .orElse(0L);
        Map<Long, Map<String, Map<String, Object>>> courseId2XkwQuestionType2RelativeInfo =
                tikuCommonService.getCourseId2XkwQuestionType2RelativeInfoByList(questionTypeRelationXkwOuterList);
        
        // 新的学科网题型 用于插入到 t_xkw_question_type
        List<String> newXkwQuestionTypeTabList = new ArrayList<>();
        long userId = 2;
        String userName = "题库管理员";
        // String currentTime = DateUtil.getCurrentDateTime();
        String currentTime = "2025-06-18 16:16:16";
        
        Map<Long, Map<String, Object>> xkwQuestionTypeId2Info = new LinkedHashMap<>();
        // 导出的数据
        List<Map<String, Object>> exportDataList = new ArrayList<>();
        
        for (Map<String, Object> relativeCourseInfo : relativeCourseInfoList) {
            log.info(" -------------------------------- {}", relativeCourseInfo);
            
            int stage = MapUtil.getInt(relativeCourseInfo, "stage");
            String stageName = MapUtil.getTrim(relativeCourseInfo, "stageName");
            long courseId = MapUtil.getLong(relativeCourseInfo, "courseId");
            String courseName = MapUtil.getTrim(relativeCourseInfo, "courseName");
            long xkwCourseId = MapUtil.getLong(relativeCourseInfo, "xkwCourseId");
            String xkwCourseName = MapUtil.getTrim(relativeCourseInfo, "xkwCourseName");
            
            Map<String, Map<String, Object>> xkwQuestionType2RelativeInfo = courseId2XkwQuestionType2RelativeInfo.get(courseId);
            if (MapUtils.isEmpty(xkwQuestionType2RelativeInfo)) {
                xkwQuestionType2RelativeInfo = new HashMap<>();
            }
            // 学科网的题型
            List<Map<String, Object>> xkwQuestionTypeInfoList = xkwCommonApi.getQuestionTypes(xkwCourseId);
            for (Map<String, Object> xkwQuestionTypeInfo : xkwQuestionTypeInfoList) {
                // 检查学科网题型映射到dn题型的信息是否完整
                String xkwQuestionType = MapUtil.getTrim(xkwQuestionTypeInfo, "id");
                Map<String, Object> relativeQuestionType = xkwQuestionType2RelativeInfo.remove(xkwQuestionType);
                relativeQuestionType = Optional.ofNullable(relativeQuestionType).orElseGet(HashMap::new);
                String errMsg = "";
                if (MapUtils.isEmpty(relativeQuestionType)) {
                    errMsg = "新题型";
                    xkwQuestionTypeId++;
                    newXkwQuestionTypeTabList.add(xkwQuestionTypeId
                            + "\t" + courseId
                            + "\t" + courseName
                            + "\t" + xkwCourseId
                            + "\t" + xkwCourseName
                            + "\t" + xkwQuestionType
                            + "\t" + MapUtil.getString(xkwQuestionTypeInfo, "name")
                            + "\t" + MapUtil.getString(xkwQuestionTypeInfo, "parentId")
                            + "\t" + (MapUtil.getBoolean(xkwQuestionTypeInfo, "objective") ? 1 : 0)
                            + "\t" + MapUtil.getString(xkwQuestionTypeInfo, "ordinal")
                            + "\t" + userId
                            + "\t" + userName
                            + "\t" + currentTime
                            + "\t" + userId
                            + "\t" + userName
                            + "\t" + currentTime
                    );
                    Map<String, Object> xkwQuestionInfo = new HashMap<>(relativeCourseInfo);
                    xkwQuestionInfo.putAll(xkwQuestionTypeInfo);
                    xkwQuestionTypeId2Info.put(xkwQuestionTypeId, xkwQuestionInfo);
                } else if (relativeQuestionType.get("relationQuestionTypeId") == null){
                    errMsg = "学科网题型未进行关联(relationQuestionTypeId==null)";
                } else if (relativeQuestionType.get("questionTypeId") == null){
                    errMsg = "学科网题型关联的题型不存在(questionTypeId==null)";
                } else if (relativeQuestionType.get("questionTypeCourseId") == null) {
                    errMsg = "学科网题型关联的题型不属于当前课程(questionTypeCourseId==null)";
                } else if (relativeQuestionType.get("questionUnitRelationId") == null){
                    errMsg = "学科网题型关联的题型未配置默认题型所属单元(questionUnitRelationId==null)";
                } else if (relativeQuestionType.get("cardUnitTypeId") == null){
                    errMsg = "学科网题型关联的题型配置的默认题型所属单元不存在(cardUnitTypeId==null)";
                } else if (relativeQuestionType.values().stream().anyMatch(Objects::isNull)) {
                    errMsg = "学科网题型关联的题型出现空值";
                }
                if (StringUtils.isBlank(errMsg)) {
                    String newXkwQuestionTypeName = MapUtil.getString(xkwQuestionTypeInfo, "name");
                    String oldXkwQuestionTypeName = MapUtil.getString(relativeQuestionType, "xkwQuestionTypeName");
                    boolean changed = !newXkwQuestionTypeName.equals(oldXkwQuestionTypeName);
                    if (changed) {
                        errMsg = "(新)" + newXkwQuestionTypeName + ";(旧)" + oldXkwQuestionTypeName;
                        log.warn("更新的学科网题型: xkwQuestionTypeInfo: {}; relativeQuestionType: {}", JSONUtil.toJson(xkwQuestionTypeInfo), JSONUtil.toJson(relativeQuestionType));
                    } else {
                        log.info("正常的学科网题型: xkwQuestionTypeInfo: {}; relativeQuestionType: {}", JSONUtil.toJson(xkwQuestionTypeInfo), JSONUtil.toJson(relativeQuestionType));
                    }
                } else {
                    log.error("{}: xkwQuestionTypeInfo: {}; relativeQuestionType: {}", errMsg, JSONUtil.toJson(xkwQuestionTypeInfo), JSONUtil.toJson(relativeQuestionType));
                }
                
                xkwQuestionTypeInfo.put("stage", stage);
                xkwQuestionTypeInfo.put("stageName", stageName);
                xkwQuestionTypeInfo.put("courseId", courseId);
                xkwQuestionTypeInfo.put("courseName", courseName);
                xkwQuestionTypeInfo.put("xkwCourseId", xkwCourseId);
                xkwQuestionTypeInfo.put("xkwCourseName", xkwCourseName);
                xkwQuestionTypeInfo.put("errMsg", errMsg);
                xkwQuestionTypeInfo.put("relativeQuestionType", relativeQuestionType);
                
                exportDataList.add(xkwQuestionTypeInfo);
            }
            
            // 已删除的 已存在系统但是新的没有返回
            for (Map<String, Object> relativeQuestionType : xkwQuestionType2RelativeInfo.values()) {
                Map<String, Object> xkwQuestionTypeInfo = new HashMap<>();
                // 还原学科网的信息
                xkwQuestionTypeInfo.put("id", MapUtil.getString(relativeQuestionType, "xkwQuestionType"));
                xkwQuestionTypeInfo.put("name", MapUtil.getString(relativeQuestionType, "xkwQuestionTypeName"));
                xkwQuestionTypeInfo.put("objective", MapUtil.getString(relativeQuestionType, "xkwQuestionTypeObjective"));
                xkwQuestionTypeInfo.put("parentId", MapUtil.getString(relativeQuestionType, "xkwQuestionTypeParent"));
                xkwQuestionTypeInfo.put("ordinal", MapUtil.getString(relativeQuestionType, "xkwQuestionTypeOrdinal"));
                // 填充相同的信息
                xkwQuestionTypeInfo.put("stage", stage);
                xkwQuestionTypeInfo.put("stageName", stageName);
                xkwQuestionTypeInfo.put("courseId", courseId);
                xkwQuestionTypeInfo.put("courseName", courseName);
                xkwQuestionTypeInfo.put("xkwCourseId", xkwCourseId);
                xkwQuestionTypeInfo.put("xkwCourseName", xkwCourseName);
                xkwQuestionTypeInfo.put("errMsg", "已删除");
                xkwQuestionTypeInfo.put("relativeQuestionType", relativeQuestionType);
                
                exportDataList.add(xkwQuestionTypeInfo);
            }
        }
        
        System.out.println(" -------------------------- t_xkw_question_type ");
        newXkwQuestionTypeTabList.forEach(System.out::println);
        System.out.println(" -------------------------- ");
        
        exportDataList.sort(ComparatorEx
                .<Map<String, Object>, Integer>asc(item -> MapUtil.getInt(item, "stage"))
                .thenAsc(item -> MapUtil.getLong(item, "courseId"))
                .thenAscNullLast(item -> MapUtil.getTrimNullable(item, "id"))
        );
        
        XkwQuestionTypeExportExcel exportExcel = new XkwQuestionTypeExportExcel("xkwQuestionType", exportDataList);
        exportExcel.exportToLocalPath(
                "xkwQuestionType_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()),
                "/data/fat-service/test/statistics/tiku/xkwQuestionType/"
        );
    }
    
    private static class XkwQuestionTypeExportExcel extends ExportExcel {
        private final List<Map<String, Object>> data;
        
        public XkwQuestionTypeExportExcel(String sheetName, List<Map<String, Object>> data) {
            super(sheetName);
            this.data = data;
        }
        
        @Override
        protected int genHeader(Sheet sheet, int currentRow, int currentCol) {
            CellStyle style = ExcelStyle.getHeaderStyle(sheet.getWorkbook());
            Row row1 = ExcelUtil.createRow(sheet, currentRow);
            
            createCell(row1, currentCol++, "序号", style);
            
            createCell(row1, currentCol++, "学段", style);
            createCell(row1, currentCol++, "学段名称", style);
            createCell(row1, currentCol++, "课程id", style);
            createCell(row1, currentCol++, "课程名称", style);
            createCell(row1, currentCol++, "xkw课程id", style);
            createCell(row1, currentCol++, "xkw课程名称", style);
            
            createCell(row1, currentCol++, "题型错误信息", style);
            
            createCell(row1, currentCol++, "xkw题型id", style);
            createCell(row1, currentCol++, "xkw名称", style);
            createCell(row1, currentCol++, "xkw客观题", style);
            createCell(row1, currentCol++, "xkw父级", style);
            createCell(row1, currentCol++, "xkw排序", style);
            
            createCell(row1, currentCol++, "1_relationQuestionTypeId", style);
            createCell(row1, currentCol++, "1_relationQuestionType", style);
            createCell(row1, currentCol++, "1_relationQuestionTypeName", style);
            
            createCell(row1, currentCol++, "2_questionTypeId", style);
            createCell(row1, currentCol++, "2_questionType", style);
            createCell(row1, currentCol++, "2_questionTypeName", style);
            
            createCell(row1, currentCol++, "3_questionTypeCourseId", style);
            
            createCell(row1, currentCol++, "4_questionUnitRelationId", style);
            createCell(row1, currentCol++, "4_questionUnitRelationUnitType", style);
            
            createCell(row1, currentCol++, "5_cardUnitTypeId", style);
            createCell(row1, currentCol++, "5_unitType", style);
            createCell(row1, currentCol++, "5_unitTypeName", style);
            createCell(row1, currentCol++, "5_readType", style);
            createCell(row1, currentCol++, "5_partType", style);
            
            return currentRow + 1;
        }
        
        @Override
        protected int genBody(Sheet sheet, int currentRow, int currentCol) {
            if (data == null || data.isEmpty()) {return currentRow;}
            CellStyle style = ExcelStyle.getBodyStyle(sheet.getWorkbook());
            for (int i = 0, iLen = data.size(); i < iLen; i++) {
                Map<String, Object> xkwQuestionTypeInfo = data.get(i);
                Map<String, Object> relativeQuestionType = MapUtil.getCast(xkwQuestionTypeInfo.remove("relativeQuestionType"));
                Row row = ExcelUtil.createRow(sheet, currentRow);
                int col = currentCol;
                createCell(row, col++, i + 1, style);
                
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "stage"), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "stageName"), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "courseId"), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "courseName"), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "xkwCourseId"), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "xkwCourseName"), style);
                
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "errMsg", ""), style);
                
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "id", ""), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "name", ""), style);
                String objective = MapUtil.getString(xkwQuestionTypeInfo, "objective", "");
                String objectiveDisplay = "true".equals(objective) || "1".equals(objective) ? "客观题" : "";
                createCell(row, col++, objectiveDisplay, style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "parentId", ""), style);
                createCell(row, col++, MapUtil.getString(xkwQuestionTypeInfo, "ordinal", ""), style);
                
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "relationQuestionTypeId", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "relationQuestionType", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "relationQuestionTypeName", ""), style);
                
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionTypeId", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionType", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionTypeName", ""), style);
                
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionTypeCourseId", ""), style);
                
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionUnitRelationId", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "questionUnitRelationUnitType", ""), style);
                
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "cardUnitTypeId", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "unitType", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "unitTypeName", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "readType", ""), style);
                createCell(row, col++, MapUtil.getString(relativeQuestionType, "partType", ""), style);
                
                currentRow++;
            }
            return currentRow;
        }
    }
}
