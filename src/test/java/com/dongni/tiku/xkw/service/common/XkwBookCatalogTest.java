package com.dongni.tiku.xkw.service.common;

import com.dongni.basedata.export.course.service.CommonCourseService;
import com.dongni.commons.utils.JSONUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2022/07/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class XkwBookCatalogTest {
    
    @Autowired
    private XkwCommonService xkwCommonService;
    
    @Autowired
    private CommonCourseService commonCourseService;
    
    @Test
    public void testGetCourseInfo() {
        List<Long> courseIdList = Stream.of(22, 23, 24, 28)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        List<Map<String, Object>> courseInfoList = commonCourseService.getCourseInfoListByCourseIdList(courseIdList);
        List<Map<String, Object>> relativeCourseInfoList = xkwCommonService.getRelativeCourseInfoList(courseInfoList);
        System.out.println(JSONUtil.toJson(relativeCourseInfoList));
    }
}
