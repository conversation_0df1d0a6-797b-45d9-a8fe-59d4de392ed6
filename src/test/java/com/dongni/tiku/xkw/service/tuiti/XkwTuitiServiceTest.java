package com.dongni.tiku.xkw.service.tuiti;

import com.dongni.commons.utils.MD5Util;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.XkwQuestionManager;
import com.dongni.tiku.xkw.utils.XkwQuestionParse2;
import com.mongodb.Block;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;

/**
 *
 * <AUTHOR>
 * @date 2024/03/08
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class XkwTuitiServiceTest {
    
    @Autowired
    private XkwQuestionManager xkwQuestionManager;
    
    private static final Logger log = LoggerFactory.getLogger(XkwTuitiServiceTest.class);
    
    @Test
    public void digestQuestion() {
        List<Long> courseIdList = new ArrayList<>();
        xkwQuestionManager.getCollection()
                .aggregate(
                        // select courseId from xxx group by courseId
                        Arrays.asList(
                                new Document("$group", new Document("_id", new Document("courseId", "$courseId"))),
                                new Document("$project", new Document("courseId", "$_id.courseId").append("_id", 0))
                        )
                )
                .forEach((Consumer<? super Document>) item -> courseIdList.add(MapUtil.getLong(item, "courseId")));
        System.out.println(courseIdList);
        
        courseIdList.stream().forEach(courseId -> {
            log.info("开始处理摘要: courseId: {}", courseId);
            xkwQuestionManager.getFindIterable(eq("courseId", courseId)).forEach((Block<? super Document>) this::digest);
            log.info("==========处理摘要完成: courseId: {}", courseId);
        });
//        xkwQuestionManager.getFindIterable().forEach((Block<? super Document>) this::digest);
    }
    
    private void digest(Document xkwQuestion) {
        if (MapUtils.isEmpty(xkwQuestion)) { return; }
        
        String xkwId = MapUtil.getTrim(xkwQuestion, "id");
        String dynamicId = MapUtil.getTrimNullable(xkwQuestion, "dynamicId");
        String dynamicId22To42 = dynamicId != null && dynamicId.length() == 43 ? dynamicId.substring(22) : null;
        String stem = MapUtil.getTrim(xkwQuestion, "stem");
        String stemMain = XkwQuestionParse2.getStemMain(stem);
        String stemMainMd5 = Optional.ofNullable(stemMain).map(MD5Util::md5).orElse(null);
        
        // String stemMainRegexResult = RegexUtils.getFirstMatchStr(stem, "<div class=\\\"qml-stem\\\".*?>(.*)</div>");
        // String stemMainRegex = MapUtil.getTrimNullable(stemMainRegexResult);
        // String stemMainRegexMd5 = Optional.ofNullable(stemMainRegex).map(MapUtil::getTrimNullable).map(MD5Util::md5).orElse(null);
        // String dynamicId22To42Cache = MapUtil.getTrimNullable(xkwQuestion, "dynamicId22To42");
        // String stemMainMd5Cache = MapUtil.getTrimNullable(xkwQuestion, "stemMainMd5");
//        log.info("id: {} | stemMainMd5: {} | stemMainRegexMd5: {} | stemMainMd5Cache: {} | dynamicId22To42: {} | dynamicId22To42Cache: {}",
//                xkwId, stemMainMd5, stemMainRegexMd5, stemMainMd5Cache, dynamicId22To42, dynamicId22To42Cache
//        );
        
        xkwQuestionManager.updateOne(eq("id", xkwId), combine(
                set("dynamicId22To42", dynamicId22To42),
                set("stemMainMd5", stemMainMd5)
        ));
    }
    
    public static void main(String[] args) {
        String dynamicId = "hdPtAz7rlth256kiPRvu-1GtUcpVK2MsViPReH17X-g";
        String dynamicId22To42 = dynamicId.substring(22);
        String dynamicId0To22 = dynamicId.substring(0, 22);
        System.out.println(dynamicId);
        System.out.println(dynamicId22To42);
        System.out.println(dynamicId0To22);
    }
}
