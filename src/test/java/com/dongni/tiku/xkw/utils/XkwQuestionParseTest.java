package com.dongni.tiku.xkw.utils;

import cn.hutool.core.io.FileUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.xkw.client.XkwJson;
import com.dongni.tiku.xkw.client.XkwResponse;
import com.dongni.tiku.xkw.utils.model.XkwQuestion;
import com.dongni.tiku.xkw.utils.model.answer.An;
import com.dongni.tiku.xkw.utils.model.answer.AnSq;
import com.dongni.tiku.xkw.utils.model.explainnation.ExplanationSeg;
import com.dongni.tiku.xkw.utils.model.stem.Stem;
import com.dongni.tiku.xkw.utils.model.stem.StemOg;
import com.dongni.tiku.xkw.utils.model.stem.StemOgOp;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * 2022/08/01
 */
public class XkwQuestionParseTest {
    
    private String getProjectPath() {
        return System.getProperty("user.dir");
    }
    private String getDocXkwPath() {
        return getProjectPath() + "/docs/tiku/xkw";
    }

    private String readFile(String xkwJsonPath) {
        return FileUtil.readString(getDocXkwPath() + xkwJsonPath, StandardCharsets.UTF_8);
    }
    
    @Test
    public void readQuestionData() {
        String json = readFile("/章节知识点推题/章节知识点推题_1_svg.json");
        Map<String, Object> response = JSONUtil.parseToMap(json);
        List<Map<String, Object>> xkwQuestionList = MapUtil.getCast(response, "data");
        XkwResultUtil.toCamelCase(xkwQuestionList);
    
        System.out.println(json);
    
        System.out.println(XkwJson.parse(json, new TypeReference<XkwResponse<List<Map<String, Object>>>>() {
        }));
        // List<Map<String, Object>> dongniQuestionList = XkwTuitiQuestionUtil.transfer(xkwQuestionList);
        // System.out.println(JSONUtil.toJson(dongniQuestionList));
    
        // System.out.println(JSONUtil.toJson(xkwQuestionList));
        //
        // List<XkwQuestion> xkwQuestionList1 = new ArrayList<>();
        // List<Map<String, Object>> dongniQuestionList1 = new ArrayList<>();
        // for (Map<String, Object> xkwQuestion : xkwQuestionList) {
        //     System.out.println("\n ---------------------------------------- \n");
        //     Map<String, Object> question = new LinkedHashMap<>();
        //     question.put("_id", "插入mongo时自动生成");
        //     question.put("xkwId", MapUtil.getString(xkwQuestion, "id"));
        //
        //     Map<String, Object> xkwCourse = MapUtil.getCast(xkwQuestion, "course");
        //     question.put("courseId", MapUtil.getLong(xkwCourse, "id"));
        //     question.put("courseName", MapUtil.getString(xkwCourse, "name"));
        //
        //     Map<String, Object> xkwQuestionType = MapUtil.getCast(xkwQuestion, "type");
        //     question.put("questionType", MapUtil.getLong(xkwQuestionType, "id"));
        //     question.put("questionTypeName", MapUtil.getString(xkwQuestionType, "name"));
        //
        //     question.put("difficulty", MapUtil.getDouble(xkwQuestion, "difficulty"));
        //
        //     question.put("stem", "从stem中解析");
        //     question.put("questions", "从stem/answer/explanation中解析");
        //
        //     String stem = MapUtil.getString(xkwQuestion, "stem");
        //     String answer = MapUtil.getString(xkwQuestion, "answer");
        //     String explanation = MapUtil.getString(xkwQuestion, "explanation");
        //
        //     XkwQuestion xkwQuestion1 = XkwQuestionParse.splitQuestion(stem, answer, explanation);
        //
        //     transfer(question, xkwQuestion1);
        //     question.put("knowledgeList", MapUtil.getListMap(xkwQuestion,"kpoints"));
        //
        //     // question.put("stemSource", stem);
        //     // question.put("answerSource", answer);
        //     // question.put("explainSource", explanation);
        //
        //     long currentTimeMillis = System.currentTimeMillis();
        //     question.put("creatorId", 2);
        //     question.put("creatorName", "学科网");
        //     question.put("createDateTime", currentTimeMillis);
        //     question.put("modifierId", 2);
        //     question.put("modifierName", "学科网");
        //     question.put("modifyDateTime", currentTimeMillis);
        //
        //     question.put("search", "计算出来的");
        //     question.put("knowledgeIdList", "计算出来的");
        //
        //     // question.put("xkwSource", xkwQuestion);
        //
        //     System.out.println(JSONUtil.toJson(question));
        //     System.out.println(stem);
        //     System.out.println(answer);
        //     System.out.println(explanation);
        //     System.out.println(xkwQuestion1);
        //     dongniQuestionList1.add(question);
        //     xkwQuestionList1.add(xkwQuestion1);
        // }
        //
        //
        // System.out.println(JSONUtil.toJson(dongniQuestionList1));
        // System.out.println(JSONUtil.toJson(xkwQuestionList1));
    }
    
    private static void transfer(Map<String, Object> question, XkwQuestion xkwQuestion) {
        Stem stem = xkwQuestion.getStem();
        List<Stem> sqs = new ArrayList<>(stem.getSqs());
        if (sqs.isEmpty()) {
            sqs.add(stem);
            stem = null;
        }
        
        List<AnSq> anSqs = xkwQuestion.getAnswer().getAnSqs();
        List<ExplanationSeg> explanationSegs = xkwQuestion.getExplanation().getExplanationSegs();
        
        List<Map<String, Object>> questions = new ArrayList<>();
        for (int i = 0; i < sqs.size(); i++) {
            Stem stemSq = sqs.get(i);
            String desc = Optional.of(stemSq).map(Stem::getHtml).orElse("");
            
            StemOg og = stemSq.getOg();
            List<String> options = og == null
                    ? new ArrayList<>()
                    : og.getOgOps().stream().map(StemOgOp::getHtml).collect(Collectors.toList());
            
            String answer = anSqs.get(i).getAns().stream()
                    .map(An::getHtml)
                    .collect(Collectors.joining(";"));
    
            int explanationSegsIndex = i + 1;
            String explain = explanationSegs.stream()
                    .filter(item -> item.getIndex() == null || item.getIndex() == explanationSegsIndex)
                    .map(ExplanationSeg::getSource)
                    .collect(Collectors.joining());
            
            Map<String, Object> subQuestion = new LinkedHashMap<>();
            subQuestion.put("desc", desc);
            subQuestion.put("options", options);
            subQuestion.put("answer", answer);
            subQuestion.put("explain", explain);
            subQuestion.put("questions", new ArrayList<>());
    
            questions.add(subQuestion);
        }
        
        question.put("stem", Optional.ofNullable(stem).map(Stem::getHtml).orElse(""));
        question.put("questions", questions);
    }
    
}
