package com.dongni.tiku.xkw.utils;

import com.dongni.commons.utils.MD5Util;
import com.dongni.tiku.common.util.MapUtil;
import com.pugwoo.wooutils.string.RegexUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Comment;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.parser.Parser;

/**
 *
 * <AUTHOR>
 * @date 2024/03/19
 */
public class XkwQuestionStemParseTest {
    
    public static void main(String[] args) {
        String stem11 = "<!--版权声明：试题（含题干、答案、解析）由学科网授权提供，受版权保护--><div class=\"qml-stem\" data-copyright=\"xkw.com-1710765272-103121680860400800-k1sINK2f5cCdu_IuIFp-ARtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><p style=\"text-align:justify;word-break:break-word\"><span style=\"font-family: 宋体;\">计算．</span></p><div class=\"qml-sq\" id-container=\"question\"><div class=\"qml-stem\" data-copyright=\"xkw.com-1710765272-103121680860400800-k1sINK2f5cCdu_IuIFp-ARtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><span class=\"ques-no\">（1）</span><img  src=\"https://img.xkw.com/dksih/formula/cd8aaa674d7e9c395b5faf296a7bb309.svg\" class=\"xkw-math-img\" /></div></div><div class=\"qml-sq\" id-container=\"question\"><div class=\"qml-stem\" data-copyright=\"xkw.com-1710765272-103121680860400800-k1sINK2f5cCdu_IuIFp-ARtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><span class=\"ques-no\">（2）</span><img  src=\"https://img.xkw.com/dksih/formula/fef46438908ff17ae4af736fd9463a36.svg\" class=\"xkw-math-img\" /></div></div></div>";
        String stem12 = "<!--版权声明：试题（含题干、答案、解析）由学科网授权提供，受版权保护--><div class=\"qml-stem\" data-copyright=\"xkw.com-1710744230-103121680860400800-T3cweW4SlNlC1Gy9Jtf_zxtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><p style=\"text-align:justify;word-break:break-word\"><span style=\"font-family: 宋体;\">计算．</span></p><div class=\"qml-sq\" id-container=\"question\"><div class=\"qml-stem\" data-copyright=\"xkw.com-1710744230-103121680860400800-T3cweW4SlNlC1Gy9Jtf_zxtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><span class=\"ques-no\">（1）</span><img  src=\"https://img.xkw.com/dksih/formula/cd8aaa674d7e9c395b5faf296a7bb309.svg\" class=\"xkw-math-img\" /></div></div><div class=\"qml-sq\" id-container=\"question\"><div class=\"qml-stem\" data-copyright=\"xkw.com-1710744230-103121680860400800-T3cweW4SlNlC1Gy9Jtf_zxtNdPchKQp0Pspdwueckz1BJR00qRx9D_AfMosQudzM\"><span class=\"ques-no\">（2）</span><img  src=\"https://img.xkw.com/dksih/formula/fef46438908ff17ae4af736fd9463a36.svg\" class=\"xkw-math-img\" /></div></div></div>";
        
        String stem21 = "<!--版权声明：试题（含题干、答案、解析）由学科网授权提供，受版权保护--><div class=\"qml-stem\" data-copyright=\"xkw.com-1710751533-103121680860400800-DdVh8oW15Emmyrz3CTktbn3SPNDwLR_JgcnNsJTCeymjBiM6lAxTg5Vh4oygVr2O\"><p style=\"text-align:justify;word-break:break-word\"><span style=\"font-family: 宋体;\">下列说法错误的是（</span><span style=\"font-family: 'Times New Roman';\"><span style=\"font-family: 'Times New Roman'\" qml-space-size=\"3\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span style=\"font-family: 宋体;\">）</span></p><div class=\" qml-og\"><table class=\"qml-og\" style=\"width:100%\"><tr><td>A.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 'Times New Roman';\">0</span><span style=\"font-family: 宋体;\">是绝对值最小的有理数</span></span></td><td>B.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">若</span><span style=\"font-family: 'Times New Roman';font-style: italic;\">x</span><span style=\"font-family: 宋体;\">的相反数是</span><img  src=\"https://img.xkw.com/dksih/formula/3389f53711264b0acba3ba6019f8b908.svg\" class=\"xkw-math-img\" /><span style=\"font-family: 宋体;\">，则</span><img  src=\"https://img.xkw.com/dksih/formula/b650820d7bed48ed67a2869ad8c65ff1.svg\" class=\"xkw-math-img\" /></span></td></tr><tr><td>C.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">若</span><img  src=\"https://img.xkw.com/dksih/formula/a1b30b4696cada3b84ff0b8c6c27e6f8.svg\" class=\"xkw-math-img\" /><span style=\"font-family: 宋体;\">，则</span><img  src=\"https://img.xkw.com/dksih/formula/6fb489e91a4b601413959b0954531df8.svg\" class=\"xkw-math-img\" /></span></td><td colspan=\"1\">D.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">任何非零有理数的平方都大于</span><span style=\"font-family: 'Times New Roman';\">0</span></span></td></tr></table></div></div>";
        String stem22 = "<!--版权声明：试题（含题干、答案、解析）由学科网授权提供，受版权保护--><div class=\"qml-stem\" data-copyright=\"xkw.com-1708932264-103121680860400800-hLxTFXLazmZX1o32uKFgRX3SPNDwLR_JgcnNsJTCeymjBiM6lAxTg5Vh4oygVr2O\"><p style=\"text-align:justify;word-break:break-word\"><span style=\"font-family: 宋体;\">下列说法错误的是（</span><span style=\"font-family: 'Times New Roman';\"><span style=\"font-family: 'Times New Roman'\" qml-space-size=\"3\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span><span style=\"font-family: 宋体;\">）</span></p><div class=\" qml-og\"><table class=\"qml-og\" style=\"width:100%\"><tr><td>A.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 'Times New Roman';\">0</span><span style=\"font-family: 宋体;\">是绝对值最小的有理数</span></span></td><td>B.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">若</span><span style=\"font-family: 'Times New Roman';font-style: italic;\">x</span><span style=\"font-family: 宋体;\">的相反数是</span><img  src=\"https://img.xkw.com/dksih/formula/3389f53711264b0acba3ba6019f8b908.svg\" class=\"xkw-math-img\" /><span style=\"font-family: 宋体;\">，则</span><img  src=\"https://img.xkw.com/dksih/formula/b650820d7bed48ed67a2869ad8c65ff1.svg\" class=\"xkw-math-img\" /></span></td></tr><tr><td>C.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">若</span><img  src=\"https://img.xkw.com/dksih/formula/a1b30b4696cada3b84ff0b8c6c27e6f8.svg\" class=\"xkw-math-img\" /><span style=\"font-family: 宋体;\">，则</span><img  src=\"https://img.xkw.com/dksih/formula/6fb489e91a4b601413959b0954531df8.svg\" class=\"xkw-math-img\" /></span></td><td colspan=\"1\">D.&nbsp;<span class=\"qml-op\"><span style=\"font-family: 宋体;\">任何非零有理数的平方都大于</span><span style=\"font-family: 'Times New Roman';\">0</span></span></td></tr></table></div></div>";
        
        String stemMain11 = getStemMain(stem11);
        String stemMain12 = getStemMain(stem12);
        String stemMain21 = getStemMain(stem21);
        String stemMain22 = getStemMain(stem22);
        
        String stemMainByRegex11 = getStemMainByRegex(stem11);
        String stemMainByRegex12 = getStemMainByRegex(stem12);
        String stemMainByRegex21 = getStemMainByRegex(stem21);
        String stemMainByRegex22 = getStemMainByRegex(stem22);
        
        String stemMainMd511 = MD5Util.md5(stemMain11);
        String stemMainMd512 = MD5Util.md5(stemMain12);
        String stemMainMd521 = MD5Util.md5(stemMain21);
        String stemMainMd522 = MD5Util.md5(stemMain22);
        
        String stemMainByRegexMd511 = MD5Util.md5(stemMainByRegex11);
        String stemMainByRegexMd512 = MD5Util.md5(stemMainByRegex12);
        String stemMainByRegexMd521 = MD5Util.md5(stemMainByRegex21);
        String stemMainByRegexMd522 = MD5Util.md5(stemMainByRegex22);
        
        System.out.println();
        System.out.println(" =========================================== stem11");
        System.out.println(stem11);
        System.out.println(" =========================================== stem12");
        System.out.println(stem12);
        
        System.out.println();
        System.out.println(" =========================================== stemMain11");
        System.out.println(stemMain11);
        System.out.println();
        System.out.println(" =========================================== stemMain12");
        System.out.println(stemMain12);
        
        System.out.println();
        System.out.println(" =========================================== stemMainByRegex11");
        System.out.println(stemMainByRegex11);
        System.out.println();
        System.out.println(" =========================================== stemMainByRegex12");
        System.out.println(stemMainByRegex12);
        
        System.out.println();
        System.out.println(" =========================================== stemMainMd511");
        System.out.println(stemMainMd511);
        System.out.println();
        System.out.println(" =========================================== stemMainMd512");
        System.out.println(stemMainMd512);
        
        System.out.println();
        System.out.println(" =========================================== stemMainByRegexMd511");
        System.out.println(stemMainByRegexMd511);
        System.out.println();
        System.out.println(" =========================================== stemMainByRegexMd512");
        System.out.println(stemMainByRegexMd512);
        
        System.out.println();
        System.out.println(" =========================================== stem21");
        System.out.println(stem21);
        System.out.println(" =========================================== stem22");
        System.out.println(stem22);
        
        System.out.println();
        System.out.println(" =========================================== stemMain21");
        System.out.println(stemMain21);
        System.out.println();
        System.out.println(" =========================================== stemMain22");
        System.out.println(stemMain22);
        
        System.out.println();
        System.out.println(" =========================================== stemMainByRegex21");
        System.out.println(stemMainByRegex21);
        System.out.println();
        System.out.println(" =========================================== stemMainByRegex22");
        System.out.println(stemMainByRegex22);
        
        System.out.println();
        System.out.println(" =========================================== stemMainMd521");
        System.out.println(stemMainMd521);
        System.out.println();
        System.out.println(" =========================================== stemMainMd522");
        System.out.println(stemMainMd522);
        
        System.out.println();
        System.out.println(" =========================================== stemMainByRegexMd521");
        System.out.println(stemMainByRegexMd521);
        System.out.println();
        System.out.println(" =========================================== stemMainByRegexMd522");
        System.out.println(stemMainByRegexMd522);
        
        
    }
    
    private static String getStemMain(String stem) {
        Document document = Jsoup.parse(stem, "", Parser.xmlParser());
        document.outputSettings().prettyPrint(false);
        removeUnlessInfo(document);
        return document.toString();
    }
    
    private static void removeUnlessInfo(Node node) {
        for (Node child : node.childNodes()) {
            if (child instanceof Comment) {
                child.remove();
            } else {
                child.removeAttr("data-copyright");
                removeUnlessInfo(child);
            }
        }
    }
    
    private static final String stemMainRegex = "<div class=\\\"qml-stem\\\".*?>(.*)</div>";
    
    private static String getStemMainByRegex(String stem) {
        String stemMainRegexResult = RegexUtils.getFirstMatchStr(stem, stemMainRegex);
        return MapUtil.getTrimNullable(stemMainRegexResult);
    }
}
