package com.dongni.tiku.holiday.task;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.holiday.task.bean.dto.HolidayCatalogDTO;
import com.dongni.tiku.holiday.task.bean.vo.HolidayCatalogTreeNodeVo;
import com.dongni.tiku.holiday.task.service.IHolidayCatalogKnowledgeService;
import com.dongni.tiku.xkw.service.common.XkwCommonService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/6/17 周一 下午 02:11
 * @Version 1.0.0
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class HolidayCatalogKnowledgeServiceTest {
    @Autowired
    private IHolidayCatalogKnowledgeService holidayCatalogKnowledgeService;

    @Autowired
    private XkwCommonService xkwCommonService;

    @Test
    public void buildCatalogTreeAccordingOrigin() {
        // 学科网某教材完整章节信息
        List<Map<String, Object>> catalogMapList = xkwCommonService.getTextbooksCatalogListCacheable(
                MapUtil.of("xkwTextbookId", 4662L));
        List<HolidayCatalogDTO> catalogDTOList = catalogMapList.stream().map(HolidayCatalogDTO::fromMap).collect(Collectors.toList());

        List<Long> catalogIdList = Arrays.asList(187162L, 187168L, 187186L, 188292L);

        List<HolidayCatalogTreeNodeVo> holidayCatalogTreeNodeVos = holidayCatalogKnowledgeService
                .buildCatalogTreeAccordingOrigin(catalogDTOList, catalogIdList);
        System.out.println();
    }

    @Test
    public void getKnowledgeByCatalog() {
        holidayCatalogKnowledgeService.getKnowledgeByCatalog(2,"4664", Arrays.asList("228589", "194858"));
    }
}
