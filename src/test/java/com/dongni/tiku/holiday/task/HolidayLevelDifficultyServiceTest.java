package com.dongni.tiku.holiday.task;

import com.dongni.tiku.holiday.task.service.IHolidayLevelDifficultyService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/7/2 周二 上午 10:14
 * @Version 1.0.0
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class HolidayLevelDifficultyServiceTest {
    @Autowired
    private IHolidayLevelDifficultyService holidayLevelDifficultyService;

    @Test
    public void testCheckTemplate() {
        long holidayTaskId = 30094;

        String s = holidayLevelDifficultyService.checkTemplate(holidayTaskId);
        System.out.println(s);
    }
}
