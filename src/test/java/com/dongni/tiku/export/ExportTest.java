package com.dongni.tiku.export;

import com.dongni.common.utils.HtmlDocxParser;
import org.docx4j.openpackaging.exceptions.Docx4JException;
import org.docx4j.openpackaging.exceptions.InvalidFormatException;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.*;
import java.net.URL;
import java.util.stream.Collectors;

public class ExportTest {

    /**
     * 导出测试程序
     * 文件读取于resources下 db/migration/tiku/export下所有html文件
     * 文件保存于fat-service下export目录，请自行创建export文件夹
     * @throws FileNotFoundException
     * @throws Docx4JException
     */
    @Test
    public void test() throws FileNotFoundException, Docx4JException {
        String basePath = this.getClass().getClassLoader().getResource("db/migration/tiku/export").getPath();
        String[] list = new File(basePath).list();
        for (String fileName : list) {
            exportTest(basePath + "/" + fileName,fileName);
        }
    }
    public void exportTest(String filePath,String fileName) throws FileNotFoundException, Docx4JException {
        InputStream INPUTSTREAM = new FileInputStream(filePath);
        String htmlAll = new BufferedReader(new InputStreamReader(INPUTSTREAM)).lines()
                .parallel().collect(Collectors.joining("\n"));
        Document parse = Jsoup.parse(htmlAll);
        Element body = parse.getElementsByTag("body").get(0);
        String html = body.toString();
        html = html.replace("<body>", "<p>");
        html = html.replace("</body>", "</p>");
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
        HtmlDocxParser.parseToAdd(wordMLPackage, html);
        String s = removeExtension(fileName);
        wordMLPackage.save(new java.io.File(System.getProperty("user.dir")
                + "/export/" + s  + ".docx"));
    }
    public static String removeExtension(String fileName) {
        int pos = fileName.lastIndexOf('.');
        if(pos > -1){
            return fileName.substring(0, pos);
        } else
            return fileName;
    }
}
