package com.dongni.tiku.develop.baidu.service;

import com.dongni.common.mongo.Order;
import com.dongni.tiku.manager.develop.DevelopQuestionAnalysisManager;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @date 2024/08/21
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest
public class DevelopBaiduQuestionPickService {
    
    @Autowired
    private DevelopQuestionAnalysisManager developQuestionAnalysisManager;
    
    @Test
    public void pick() {
        Map<Long, Integer> courseId2PickQuantity = new HashMap<>();
        courseId2PickQuantity.put(2L, 132);
        courseId2PickQuantity.put(3L, 599);
        courseId2PickQuantity.put(4L, 200);
        courseId2PickQuantity.put(5L, 369);
        courseId2PickQuantity.put(6L, 354);
        courseId2PickQuantity.put(7L, 395);
        courseId2PickQuantity.put(8L, 372);
        courseId2PickQuantity.put(9L, 425);
        courseId2PickQuantity.put(10L, 203);
        courseId2PickQuantity.put(12L, 99);
        courseId2PickQuantity.put(13L, 462);
        courseId2PickQuantity.put(14L, 200);
        courseId2PickQuantity.put(15L, 215);
        courseId2PickQuantity.put(16L, 93);
        courseId2PickQuantity.put(17L, 155);
        courseId2PickQuantity.put(18L, 205);
        courseId2PickQuantity.put(19L, 262);
        courseId2PickQuantity.put(20L, 92);
        courseId2PickQuantity.put(22L, 46);
        courseId2PickQuantity.put(23L, 62);
        courseId2PickQuantity.put(24L, 60);
        
        for (Map.Entry<Long, Integer> entry : courseId2PickQuantity.entrySet()) {
            Long courseId = entry.getKey();
            Integer pickQuantity = entry.getValue();
            if (pickQuantity <= 0) {
                continue;
            }
            List<Document> questionAnalysisList = developQuestionAnalysisManager.getList(
                    eq("courseId", courseId),
                    null, null,
                    Order.Field.desc("createPure"),
                    0, pickQuantity
            );
            for (Document questionAnalysis : questionAnalysisList) {
                System.out.println(questionAnalysis.get("_id").toString());
            }
        }
    }
}
