package com.dongni.tiku.develop.dn.service;

import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.develop.DevelopQuestionAnalysisManager;
import com.dongni.tiku.manager.impl.QuestionManager;
import com.mongodb.Block;
import com.mongodb.client.model.ReplaceOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @date 2024/08/21
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest
public class DevelopQuestionAnalysisTest {
    
    private static final Logger log = LoggerFactory.getLogger(DevelopQuestionAnalysisTest.class);
    
    @Autowired
    private QuestionManager questionManager;
    
    @Autowired
    private DevelopQuestionAnalysisManager developQuestionAnalysisManager;
    
    private static final ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMddHHmmssSSS"));
    
    @Test
    public void analysisQuestion() {
        Map<Long, Integer> courseId2Quantity = new HashMap<>();
        courseId2Quantity.put(2L, 86830);
        courseId2Quantity.put(3L, 393424);
        courseId2Quantity.put(4L, 131361);
        courseId2Quantity.put(5L, 242417);
        courseId2Quantity.put(6L, 232336);
        courseId2Quantity.put(7L, 259428);
        courseId2Quantity.put(8L, 244338);
        courseId2Quantity.put(9L, 279153);
        courseId2Quantity.put(10L, 133230);
        courseId2Quantity.put(12L, 64890);
        courseId2Quantity.put(13L, 303983);
        courseId2Quantity.put(14L, 131452);
        courseId2Quantity.put(15L, 141645);
        courseId2Quantity.put(16L, 61476);
        courseId2Quantity.put(17L, 101674);
        courseId2Quantity.put(18L, 134866);
        courseId2Quantity.put(19L, 172391);
        courseId2Quantity.put(20L, 60372);
        courseId2Quantity.put(22L, 30168);
        courseId2Quantity.put(23L, 40464);
        courseId2Quantity.put(24L, 39628);
        List<Long> courseIdList = courseId2Quantity.keySet().stream()
                .sorted(ComparatorEx.desc(courseId2Quantity::get))
                .collect(Collectors.toList());
        
        System.out.println(courseIdList);
        
        courseIdList
                .stream()
                .forEach(courseId -> {
                    System.out.println(" ======================== 开始: courseId: " + courseId);
                    AtomicInteger questionCount = new AtomicInteger();
                    AtomicInteger validCount = new AtomicInteger();
                    questionManager.getFindIterable(eq("courseId", courseId)).forEach((Block<? super Document>) question -> {
                        int doneCount = questionCount.getAndIncrement();
                        if (doneCount % 10000 == 0) {
                            System.out.println("courseId: " + courseId + "; doneCount: " + doneCount + "; validCount: " + validCount.get());
                        }
                        
                        Object _id = question.get("_id");
                        if (!(_id instanceof ObjectId)) {
                            log.error("questionId: {} : 试题id不合法", _id);
                            return;
                        }
                        Object questionsObj = MapUtil.getCast(question, "questions");
                        if (!(questionsObj instanceof List)) {
                            log.error("questionId: {} : questions不是list", _id);
                            return;
                        }
                        List<Map<String, Object>> questions = MapUtil.getCast(questionsObj);
                        if (CollectionUtils.isEmpty(questions)) {
                            log.error("questionId: {} : questions为空", _id);
                            return;
                        }
                        
                        String questionId = _id.toString();
                        
                        long createDateTime = MapUtil.getLong(question, "createDateTime");
                        long modifyDateTime = MapUtil.getLong(question, "modifyDateTime");
                        long createPure = Long.parseLong(simpleDateFormatThreadLocal.get().format(new Date(createDateTime)));
                        long modifyPure = Long.parseLong(simpleDateFormatThreadLocal.get().format(new Date(modifyDateTime)));
                        
                        String questionJson = JSONUtil.toJson(question);
                        boolean hasImg = questionJson.contains("<img src=");
                        boolean hasNoAnalysis = hasNoAnalysis(questionId, questions);
                        boolean hasNoAnswer = hasNoAnswer(questionId, questions);
                        
                        if (hasNoAnalysis || hasNoAnswer) {
                            return;
                        }
                        Document tags = new Document()
                                .append("hasImg", hasImg)
                                .append("hasAnalysis", !hasNoAnalysis)
                                .append("hasAnswer", !hasNoAnswer);
                        
                        Document analysis = new Document();
                        analysis.put("_id", _id);
                        analysis.put("courseId", MapUtil.getLong(question, "courseId"));
                        analysis.put("courseName", MapUtil.getTrim(question, "courseName"));
                        analysis.put("questionType", MapUtil.getInt(question, "questionType", 0));
                        analysis.put("questionTypeName", MapUtil.getTrim(question, "questionTypeName", "未知"));
                        analysis.put("createDateTime", createDateTime);
                        analysis.put("modifyDateTime", modifyDateTime);
                        analysis.put("createPure", createPure);
                        analysis.put("modifyPure", modifyPure);
                        analysis.put("tags", tags);
                        
                        developQuestionAnalysisManager.replaceOne(eq("_id", _id), analysis, new ReplaceOptions().upsert(true));
                        
                        validCount.incrementAndGet();
                    });
                    System.out.println(" ======================== 结束: courseId: " + courseId
                            + "; questionCount: " + questionCount.get()
                            + "; validCount: " + validCount.get()
                    );
                });
    }
    
    private boolean hasNoAnalysis(String questionId, List<Map<String, Object>> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return false;
        }
        for (Map<String, Object> question : questions) {
            String explain = MapUtil.getTrim(question, "explain", "");
            if (StringUtils.isBlank(explain)) {
                return true;
            }
            if (explain.contains("略")) {
                return true;
            }
            if (lueSet.contains(explain)) {
                return true;
            }
            // if (explain.length() < 128) {
            //     if (explain.contains("<p>略</p>")) {
            //         if (logSet.add(explain)) {
            //             log.warn("questionId: {} : 解析包含 \"<p>略</p>\"; explain: {}", questionId, explain);
            //         }
            //     } else if (explain.contains("略")) {
            //         if (logSet.add(explain)) {
            //             log.warn("questionId: {} : 解析包含 \"略\"; explain: {}", questionId, explain);
            //         }
            //     }
            // }
            
            List<Map<String, Object>> subQuestions = MapUtil.getCast(question, "questions");
            if (hasNoAnalysis(questionId, subQuestions)) {
                return true;
            }
        }
        return false;
    }
    
    
    private boolean hasNoAnswer(String questionId, List<Map<String, Object>> questions) {
        if (CollectionUtils.isEmpty(questions)) {
            return false;
        }
        for (Map<String, Object> question : questions) {
            String answer = MapUtil.getTrim(question, "answer", "");
            if (StringUtils.isBlank(answer)) {
                return true;
            }
            if (answer.contains("略")) {
                return true;
            }
            if (lueSet.contains(answer)) {
                return true;
            }
            // if (answer.length() < 128) {
            //     if (answer.contains("<p>略</p>")) {
            //         if (logSet.add(answer)) {
            //             log.warn("questionId: {} : 答案包含 \"<p>略</p>\"; answer: {}", questionId, answer);
            //         }
            //     } else if (answer.contains("略")) {
            //         if (logSet.add(answer)) {
            //             log.warn("questionId: {} : 答案包含 \"略\"; answer: {}", questionId, answer);
            //         }
            //     }
            // }
            List<Map<String, Object>> subQuestions = MapUtil.getCast(question, "questions");
            if (hasNoAnswer(questionId, subQuestions)) {
                return true;
            }
        }
        return false;
    }
    
    private static final Set<String> logSet = new HashSet<>();
    private static final Set<String> lueSet = Stream.of(
                    "略",
                    "略．",
                    "略；",
                    "（略）",
                    "（略）．",
                    "略，略",
                    "略，略，略",
                    "略，略，略，略",
                    "略，略，略，略，略",
                    "略，略，略，略，略，略",
                    "略，略，略，略，略，略，略",
                    "略，略，略，略，略，略，略，略，略，略",
                    "省略不写",
                    "略（可借助信息技术）．",
                    "证明过程略．",
                    "函数图象略．",
                    "证明略．",
                    "证明略.",
                    "证明略",
                    "描点略",
                    "作图略",
                    "作图略．",
                    "填表略；",
                    "作图题，略．",
                    "作图略（画出一个即可）．",
                    "过程略；",
                    "理由略",
                    "图象略．",
                    "树状图略",
                    "图（略）．",
                    "表略．",
                    "图略；",
                    "散点图略．",
                    "简图略．",
                    "画图略．",
                    "画图略",
                    "图略",
                    "图略．",
                    "证略",
                    "数轴上的点的表示略．",
                    "证略．",
                    "填表（略）；",
                    "因为\n略",
                    "补充条件正确．\n证明过程略．",
                    "答案不唯一，如列表的方法．列表略．",
                    "<p></p>",
                    "<p>略</p>",
                    "<p>略.</p>",
                    "<p>略。</p>",
                    "<p>略 </p>",
                    "<p>略·</p>",
                    "<p>略  </p>",
                    "<p>略，详见答案。</p>",
                    "<p>略，详见答案</p>",
                    "<p>略， 详见答案。</p>",
                    "<p>略，详见答案<br/></p>",
                    "<p>略，详见答案。<br/></p>",
                    "<p>略，推理合情即可.</p>",
                    "<p>略(答案不唯一)</p>",
                    "<p>图略，方案很多，只要合理即可。</p>",
                    "<p>略（绘制符合题目要求的图形）</p>",
                    "<p>略 </p>",
                    "<p> 略</p>",
                    "<p>略略</p>",
                    "<p>略略略</p>",
                    "<p>证法略</p>",
                    "<p>过程略</p>",
                    "<p>；略</p>",
                    "<p>;略</p>",
                    "<p>图略．</p>",
                    "<p>作图略</p>",
                    "<p>作图略（不写结论扣1分）；</p>",
                    "<p>证明略</p>",
                    "<p>画图略</p>",
                    "<p>如图略</p>",
                    "<p>如图，略</p>",
                    "<p>P略</p>",
                    "<p>M略</p>",
                    "<p>L略</p>",
                    "<p>Z略</p>",
                    "<p>l略</p>",
                    "<p>2略</p>",
                    "<p>4略</p>",
                    "<p>lu略</p>",
                    "<p>图略</p>",
                    "<p>略<br/></p>",
                    "<p>略.<br/></p>",
                    "<p>略w</p>",
                    "<p>略\\)</p>",
                    "<p>略</p>\n<p> </p>",
                    "<p> </p>\n<p>略</p>",
                    "<p>略&nbsp;</p>",
                    "<p>&nbsp;略</p>",
                    "<p>略&nbsp;<br/></p>",
                    "<p>略&nbsp;&nbsp;</p>",
                    "<p>略&nbsp;&nbsp;&nbsp;&nbsp;</p>",
                    "<p>略<br /><br /></p>",
                    "<p>略</p><p><br/></p>",
                    "<p>略，没搜到答案</p>",
                    "<p>略（搜不到）</p>",
                    "<p>图略.答案不唯一，只要符合题意即可。</p>",
                    "<p>略       （1分）</p>",
                    "<p>图略-------1分</p>",
                    "<p>图略；………………………1分</p>",
                    "<p>图略  ---------- 2分</p>",
                    "<p>证明略   （4分）</p>",
                    "<p>条形图略;..............6分</p>",
                    "<p>略…………………………………………………………………………… 8分</p>",
                    "<p>略</p>\n<p> </p>\n<p> </p>",
                    "<p>（1）略</p><p>（2）</p>",
                    "<p><ruby></ruby>略</p>",
                    "<p><nobr></nobr></p><p>略</p>",
                    "<p style=\"text-align: left;\">略</p>",
                    "<p style=\"text-align: center;\">略</p>",
                    "<p style=\"text-align: left;\">略略</p>",
                    "<p style=\"text-align: justify;\">略</p>",
                    "<p style=\"text-indent: 0em;\">略</p>",
                    "<p style=\"text-indent: 0em;\">略<br/></p>",
                    "<p style=\"text-indent: 2em;\">略</p>",
                    "<p style=\"text-align: left;\">略</p>\n<p style=\"text-align: left;\"> </p>",
                    "<p><span style=\"line-height: 1.75;\">略</span><br/></p>",
                    "<p>略<br style=\"text-align: left;\"/></p>",
                    "<p>略<span style=\"text-decoration: underline;\"></span></p>",
                    "<p>略<span style=\"border: 1px solid rgb(0, 0, 0);\"></span></p>",
                    "<p>略<span style=\"text-decoration: line-through;\"></span></p>",
                    "<p>略</p>\n<p><img src=\"//cdn.dongni100.com/upload/entrust/271577/1706854838516_009.png\" /></p>",
                    "<p><span style=\"white-space: nowrap;\">略</span></p>",
                    "<p><span style=\"text-decoration: none;\">略</span><br/></p>",
                    "<p><span style=\"text-decoration: underline;\">略</span></p>",
                    "<p><span style=\"text-decoration: underline;\">略</span><br/></p>",
                    "<p><span style=\"text-decoration: line-through;\">略</span><br/></p>",
                    null
            )
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    
}
