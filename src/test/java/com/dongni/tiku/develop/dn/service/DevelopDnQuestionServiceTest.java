package com.dongni.tiku.develop.dn.service;

import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.develop.DevelopDnQuestionManager;
import com.dongni.tiku.manager.impl.QuestionManager;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.ReplaceOptions;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lt;

/**
 * <AUTHOR>
 * @date 2024/08/19
 */
// @RunWith(SpringRunner.class)
// @SpringBootTest
public class DevelopDnQuestionServiceTest {
    
    @Autowired
    private QuestionManager questionManager;
    
    @Autowired
    private DevelopDnQuestionManager developDnQuestionManager;
    
    
    @Test
    public void initDevelopDnQuestion() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        
        List<Map<String, Object>> questionSamplingConfigList = getQuestionSamplingConfigList();
        for (Map<String, Object> questionSamplingConfig : questionSamplingConfigList) {
            int quantity = MapUtil.getInt(questionSamplingConfig, "quantity", 0);
            if (quantity <= 0) { continue; }
            long courseId = MapUtil.getLong(questionSamplingConfig, "courseId");
            Long createDateTimeGte = MapUtil.getLongNullable(questionSamplingConfig, "createDateTimeGte");
            Long createDateTimeLt = MapUtil.getLongNullable(questionSamplingConfig, "createDateTimeLt");
            
            List<Bson> queryList = new ArrayList<>();
            queryList.add(eq("courseId", courseId));
            if (createDateTimeGte != null) {
                queryList.add(gte("createDateTime", createDateTimeGte));
            }
            if (createDateTimeLt != null) {
                queryList.add(lt("createDateTime", createDateTimeLt));
            }
            questionManager.getCollection().aggregate(
                    Arrays.asList(
                            Aggregates.match(and(queryList)),
                            Aggregates.sample(quantity)
                    )
            ).forEach((Consumer<? super Document>) question -> {
                ObjectId id = MongoUtil.getMongoId(question);
                long createDateTime = MapUtil.getLong(question, "createDateTime");
                long modifyDateTime = MapUtil.getLong(question, "modifyDateTime");
                long createPure = Long.parseLong(simpleDateFormat.format(new Date(createDateTime)));
                long modifyPure = Long.parseLong(simpleDateFormat.format(new Date(modifyDateTime)));
                
                Document questionSimple = new Document();
                questionSimple.put("_id", id);
                questionSimple.put("courseId",  MapUtil.getLong(question, "courseId"));
                questionSimple.put("courseName",  MapUtil.getTrim(question, "courseName"));
                questionSimple.put("createDateTime", createDateTime);
                questionSimple.put("modifyDateTime", modifyDateTime);
                questionSimple.put("createPure", createPure);
                questionSimple.put("modifyPure", modifyPure);
                
                developDnQuestionManager.replaceOne(eq("_id", id), questionSimple, new ReplaceOptions().upsert(true));
            });
        }
    }
    
    private List<Map<String, Object>> getQuestionSamplingConfigList() {
        return Stream.of(
                        getQuestionSamplingConfig(2, 12, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(3, 53, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(4, 18, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(5, 32, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(6, 31, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(7, 35, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(8, 33, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(9, 37, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(10, 18, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(12, 9, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(13, 41, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(14, 18, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(15, 19, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(16, 8, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(17, 14, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(18, 18, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(19, 23, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(20, 8, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(22, 4, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(23, 5, null, "2020-01-01 00:00:00"),
                        getQuestionSamplingConfig(24, 5, null, "2020-01-01 00:00:00"),
                        
                        getQuestionSamplingConfig(2, 16, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(3, 71, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(4, 24, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(5, 44, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(6, 42, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(7, 47, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(8, 44, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(9, 50, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(10, 24, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(12, 12, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(13, 55, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(14, 24, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(15, 26, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(16, 11, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(17, 18, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(18, 24, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(19, 31, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(20, 11, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(22, 5, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(23, 7, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        getQuestionSamplingConfig(24, 7, "2020-01-01 00:00:00", "2022-01-01 00:00:00"),
                        
                        getQuestionSamplingConfig(2, 17, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(3, 79, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(4, 26, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(5, 49, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(6, 47, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(7, 52, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(8, 49, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(9, 56, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(10, 27, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(12, 13, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(13, 61, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(14, 26, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(15, 28, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(16, 12, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(17, 20, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(18, 27, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(19, 35, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(20, 12, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(22, 6, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(23, 8, "2022-01-01 00:00:00", null),
                        getQuestionSamplingConfig(24, 8, "2022-01-01 00:00:00", null),
                        
                        null
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    private Map<String, Object> getQuestionSamplingConfig(long courseId, int quantity, String createDateTimeMinInclude, String createDateTimeMaxExclude) {
        Long createDateTimeGte = null;
        if (StringUtils.isNotBlank(createDateTimeMinInclude)) {
            try {
                createDateTimeGte = DateUtil.parseDateTime(createDateTimeMinInclude).getTime();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        
        Long createDateTimeLt = null;
        if (StringUtils.isNotBlank(createDateTimeMaxExclude)) {
            try {
                createDateTimeLt = DateUtil.parseDateTime(createDateTimeMaxExclude).getTime();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return new Document()
                .append("courseId", courseId)
                .append("quantity", quantity)
                .append("createDateTimeGte", createDateTimeGte)
                .append("createDateTimeLt", createDateTimeLt)
                ;
    }
}
