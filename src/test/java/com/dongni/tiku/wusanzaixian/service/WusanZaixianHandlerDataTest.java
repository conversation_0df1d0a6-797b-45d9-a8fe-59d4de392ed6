package com.dongni.tiku.wusanzaixian.service;


import com.dongni.common.mongo.Order;
import com.dongni.common.utils.MongoUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.WusanQuestionManager;
import com.dongni.tiku.manager.impl.WusanTextbookCatalogManager;
import com.pugwoo.wooutils.string.RegexUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonNull;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import static com.dongni.common.mongo.Order.Field.asc;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.toMap;

/**
 * 处理数据用
 * <AUTHOR>
 * @date 2023/09/18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WusanZaixianHandlerDataTest {
    
    @Autowired
    private WusanTextbookCatalogManager wusanTextbookCatalogManager;
    
    @Autowired
    private WusanQuestionManager wusanQuestionManager;
    
    /**
     * 处理五三试题的year年份/publishYear出版年份字段
     */
    @Test
    public void handlerYear() {
        
        List<Document> wusanTextbookCatalogList = wusanTextbookCatalogManager.getList();
        Map<String, Document> textbookCatalogId2Info = wusanTextbookCatalogList.stream()
                .collect(toMap(item -> MapUtil.getTrim(item, "textbookCatalogId"), item -> item));
        
        Bson query = null;
        String[] includes = null;
        String[] excludes = new String[]{"stem", "questions", "search"};
        Order order = asc("_id");
        wusanQuestionManager.getFindIterable(query, includes, excludes, order, null, null)
                .forEach((Consumer<? super Document>) wusanQuestion -> {
                    ObjectId _id = MongoUtil.getMongoId(wusanQuestion.get("_id"));
                    
                    // 处理年份 从source中抽取
                    Integer year = MapUtil.getIntNullable(wusanQuestion, "year");
                    if (year == null) {
                        String source = MapUtil.getString(wusanQuestion, "source", "");
                        if (StringUtils.isNotBlank(source)) {
                            String sourceYearStr = RegexUtils.getFirstMatchStr(source, "\\d{4}");
                            year = MapUtil.getIntNullable(sourceYearStr);
                        }
                        
                        wusanQuestionManager.updateOne(
                                and(
                                        eq("_id", _id),
                                        or(
                                                exists("year", false),
                                                eq("year", new BsonNull())
                                        )
                                ),
                                set("year", year)
                        );
                    }
                    
                    // 出版年份 如果有多个 取大的那一个 最大的那个更能代表该题的重要性
                    List<String> wusanTextbookCatalogIdList = MapUtil.getCast(wusanQuestion, "wusanTextbookCatalogIdList");
                    Integer publishYear = null;
                    for (String wusanTextbookCatalogId : wusanTextbookCatalogIdList) {
                        Document catalogDoc = textbookCatalogId2Info.get(wusanTextbookCatalogId);
                        int catalogYear = MapUtil.getInt(catalogDoc, "year");
                        if (publishYear == null || catalogYear > publishYear) {
                            publishYear = catalogYear;
                        }
                    }
                    if (publishYear != null) {
                        wusanQuestionManager.updateOne(
                                eq("_id", _id),
                                set("publishYear", publishYear)
                        );
                    }
                });
    }
}
