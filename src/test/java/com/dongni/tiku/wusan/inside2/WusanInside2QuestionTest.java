package com.dongni.tiku.wusan.inside2;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.WusanInside2QuestionManager;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolListQuestionsRequest;
import com.dongni.tiku.wusan.inside2.service.WusanInside2ApiTq;
import com.dongni.tiku.wusan.inside2.service.WusanInside2ApiTqTest;
import com.mongodb.client.model.ReplaceOptions;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.BsonValue;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @date 2025/05/20
 */
public class WusanInside2QuestionTest {
    
    private final TikuMongodb tikuMongodb;
    private final WusanInside2QuestionManager wusanInside2QuestionManager;
    private final WusanInside2ApiTq wusanInside2ApiTq;
    
    public WusanInside2QuestionTest() {
        // // dalao测试环境
        tikuMongodb = new TikuMongodb();
        tikuMongodb.setHost("************");
        tikuMongodb.setPort("27017");
        tikuMongodb.setDatabase("tiku");
        tikuMongodb.setUserName("tiku");
        tikuMongodb.setPassword("Tiku2015");
        
        // www测试环境
        // tikuMongodb = new TikuMongodb();
        // tikuMongodb.setHost("************");
        // tikuMongodb.setPort("34352");
        // tikuMongodb.setDatabase("tiku");
        // tikuMongodb.setUserName("tiku");
        // tikuMongodb.setPassword("Tiku#!@2019");
        
        wusanInside2QuestionManager = new WusanInside2QuestionManager(tikuMongodb);
        
        // 五三接口
        wusanInside2ApiTq = new WusanInside2ApiTqTest().wusanInside2ApiTq;
    }
    
    // @Test
    public void test() {
        WusanInside2SchoolListQuestionsRequest request = new WusanInside2SchoolListQuestionsRequest();
        request.setQuestionIds(Stream.of(
                        "1922221997905371142",  // 普普通通填空题
                        "1922221997909565448", // 选词填空 有小题
                        "1922221998018617353",  // 七选五
                        "1922221997968285711",  // 小题选择题
                        "1922221997968285712",  // 1922221997968285711的小题 不是大题 检索不到 我们也不需要这样的题 正常
                        "1750771842355257346",  // 杨老师给的综合题 小题有客观题也有主观题
                        "")
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList()));
        List<Map<String, Object>> schoolQuestionList = wusanInside2ApiTq.schoolListQuestions(request);
        
        for (Map<String, Object> wusanInside2QuestionSource : schoolQuestionList) {
            String tqId = MapUtil.getTrimNullable(wusanInside2QuestionSource, "tqId");
            if (StringUtils.isBlank(tqId)) {
                throw new CommonException(ResponseStatusEnum.FAILURE, "试题缺少tqId");
            }
            Document wusanInside2QuestionDoc = new Document(wusanInside2QuestionSource);
            System.out.println(wusanInside2QuestionDoc.get("_id"));
            UpdateResult updateResult = wusanInside2QuestionManager.replaceOne(eq("tqId", tqId), wusanInside2QuestionDoc, new ReplaceOptions().upsert(true));
            BsonValue upsertedId = updateResult.getUpsertedId();
            String _id;
            if (upsertedId == null) {
                _id = wusanInside2QuestionManager.getFirst(eq("tqId", tqId), new String[]{"_id"}).getString("_id");
            } else {
                _id = upsertedId.asObjectId().getValue().toString();
            }
            wusanInside2QuestionDoc.append("_id", _id);
            System.out.println(wusanInside2QuestionDoc.get("_id"));
        }
    }
    
    /**
     * 获取五三试题中第三方知识点为 zjw_ 打头的知识点信息
     *    这些试题需要重新在五三进行获取，刷新不对的标注
     */
    // @Test
    public void wusanInside2QuestionKnowledgeTagFixTest() {
        wusanInside2QuestionManager.where()
                .select("_id", "tqId", "tagList")
                .forEach(wusanInside2Question -> {
                    List<Map<String, Object>> tagList = MapUtil.getCast(wusanInside2Question, "tagList");
                    if (CollectionUtils.isEmpty(tagList)) {
                        return;
                    }
                    for (Map<String, Object> tag : tagList) {
                        String tagId = MapUtil.getStringNullable(tag, "tagId");
                        if (StringUtils.isNotBlank(tagId)) {
                            if (tagId.startsWith("zjw_")) {
                                System.out.println(MapUtil.getTrim(wusanInside2Question, "tqId"));
                                return;
                            }
                        }
                    }
                });
    }
}
