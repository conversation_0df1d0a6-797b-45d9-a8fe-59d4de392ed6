package com.dongni.tiku.wusan.inside2.service;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2TransferToDongniDTO;
import com.dongni.tiku.wusan.inside2.bean.params.WusanInside2IframeQuestionListParams;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/05/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WusanInside2QuestionServiceTest {
    
    @Autowired
    private WusanInside2QuestionService wusanInside2QuestionService;
    
    @Test
    public void testGetIframeQuestionList() {
        List<String> wusanQuestionIdList = Stream.of(
                        "1922221997905371142",  // 普普通通填空题
                        "1922221997909565448", // 选词填空 有小题
                        "1922221998018617353",  // 七选五
                        "1922221997968285711",  // 小题选择题
                        "1922221997968285712",  // 1922221997968285711的小题 不是大题 检索不到 我们也不需要这样的题 正常
                        "1750771842355257346",  // 杨老师给的综合题 全是填空
                        "1704339480467046403",  // 杨老师给的综合题 第四小题是选择题
                        "")
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        WusanInside2IframeQuestionListParams params = new WusanInside2IframeQuestionListParams();
        params.setQuestionIds(wusanQuestionIdList);
        WusanInside2TransferToDongniDTO result = wusanInside2QuestionService.getIframeQuestionList(params);
        System.out.println(" ------------------------------------------------------ ");
        result.getDongniQuestionList().stream().map(JSONUtil::toJson).forEach(System.out::println);
        System.out.println(" ------------------------------------------------------ ");
        result.getWusanInsideTransferToDongniErrorInfoList().stream().map(JSONUtil::toJson).forEach(System.out::println);
        
    }
}
