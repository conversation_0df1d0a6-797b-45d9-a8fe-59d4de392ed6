package com.dongni.tiku.wusan.inside2;

import com.dongni.common.utils.StreamUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.manager.impl.WusanInside2KnowledgeMappingToXkwManager;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolQueryTagMappingRequest;
import com.dongni.tiku.wusan.inside2.service.WusanInside2ApiTq;
import com.dongni.tiku.wusan.inside2.service.WusanInside2ApiTqTest;
import com.mongodb.client.model.ReplaceOptions;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2025/05/20
 */
public class WusanInside2KnowledgeMappingTest {
    
    private final TikuMongodb tikuMongodb;
    private final WusanInside2KnowledgeMappingToXkwManager wusanInside2KnowledgeMappingToXkwManager;
    private final WusanInside2ApiTq wusanInside2ApiTq;
    
    public WusanInside2KnowledgeMappingTest() {
        // dalao测试环境
        tikuMongodb = new TikuMongodb();
        tikuMongodb.setHost("************");
        tikuMongodb.setPort("27017");
        tikuMongodb.setDatabase("tiku");
        tikuMongodb.setUserName("tiku");
        tikuMongodb.setPassword("Tiku2015");
        
        wusanInside2KnowledgeMappingToXkwManager = new WusanInside2KnowledgeMappingToXkwManager(tikuMongodb);
        
        // 五三接口
        wusanInside2ApiTq = new WusanInside2ApiTqTest().wusanInside2ApiTq;
    }
    
    @Test
    public void initKnowledgeMapping() {
        WusanInside2SchoolQueryTagMappingRequest request = new WusanInside2SchoolQueryTagMappingRequest();
        List<Map<String, Object>> tagMappingList = wusanInside2ApiTq.schoolQueryTagMapping(request);
        tagMappingList = Optional.ofNullable(tagMappingList).orElseGet(ArrayList::new);
        tagMappingList = tagMappingList.stream()
                .filter(StreamUtil.distinctByKey(item -> {
                    String wusanKnowledgeId = MapUtil.getTrim(item, "tagId");
                    String xkwKnowledgeId = MapUtil.getTrim(item, "xkwTagId");
                    return wusanKnowledgeId + "__" + xkwKnowledgeId;
                }))
                .collect(Collectors.toList());
        
        List<Document> existList = wusanInside2KnowledgeMappingToXkwManager.getList();
        Map<String, Document> ukWusanXkw2Mapping = existList.stream()
                .collect(toMap(item -> {
                    String wusanKnowledgeId = MapUtil.getTrim(item, "wusanKnowledgeId");
                    String xkwKnowledgeId = MapUtil.getTrim(item, "xkwKnowledgeId");
                    return wusanKnowledgeId + "__" + xkwKnowledgeId;
                }, item -> item));
        
        for (Map<String, Object> tagMapping : tagMappingList) {
            String wusanKnowledgeId = MapUtil.getTrim(tagMapping, "tagId");
            String xkwKnowledgeId = MapUtil.getTrim(tagMapping, "xkwTagId");
            String xkwKnowledgeName = MapUtil.getTrim(tagMapping, "xkwTagInfoName");
            Document newMappingDoc = new Document();
            newMappingDoc.put("wusanKnowledgeId", wusanKnowledgeId);
            newMappingDoc.put("xkwKnowledgeId", xkwKnowledgeId);
            newMappingDoc.put("xkwKnowledgeName", xkwKnowledgeName);
            newMappingDoc.put("sourceTagMapping", tagMapping);
            String ukWusanXkw = wusanKnowledgeId + "__" + xkwKnowledgeId;
            Document oldMappingDoc = ukWusanXkw2Mapping.remove(ukWusanXkw);
            if (oldMappingDoc != null) {
                oldMappingDoc.remove("_id");
                if (newMappingDoc.equals(oldMappingDoc)) {
                    continue;
                }
            }
            System.out.println("新增知识点映射: " + JSONUtil.toJson(tagMapping));
            Bson query = and(
                    eq("wusanKnowledgeId", wusanKnowledgeId),
                    eq("xkwKnowledgeId", xkwKnowledgeId)
            );
            wusanInside2KnowledgeMappingToXkwManager.replaceOne(query, newMappingDoc, new ReplaceOptions().upsert(true));
        }
        
        for (Document deleteMappingDoc : ukWusanXkw2Mapping.values()) {
            System.out.println("删除知识点映射: " + JSONUtil.toJson(deleteMappingDoc.get("sourceTagMapping")));
            String wusanKnowledgeId = MapUtil.getTrim(deleteMappingDoc, "wusanKnowledgeId");
            String xkwKnowledgeId = MapUtil.getTrim(deleteMappingDoc, "xkwKnowledgeId");
            Bson query = and(
                    eq("wusanKnowledgeId", wusanKnowledgeId),
                    eq("xkwKnowledgeId", xkwKnowledgeId)
            );
            wusanInside2KnowledgeMappingToXkwManager.deleteOne(query);
        }
    }
}
