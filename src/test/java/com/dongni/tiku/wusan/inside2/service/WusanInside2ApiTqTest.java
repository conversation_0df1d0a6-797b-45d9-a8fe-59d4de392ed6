package com.dongni.tiku.wusan.inside2.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.ListUtil;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.commons.utils.TreeUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wusan.inside2.bean.tq.WusanInside2Question;
import com.dongni.tiku.wusan.inside2.bean.tq.WusanInside2SchoolContentTree;
import com.dongni.tiku.wusan.inside2.client.WusanInside2Client;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolBatchQueryTagsRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolListQuestionsByPaperIdRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolListQuestionsByPaperIdResponse;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolListQuestionsRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolQueryContentListByQrcodeRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolQueryTagByLevelSubjectRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolQueryTagMappingRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2SchoolSearchSectionSubjectRequest;
import com.dongni.tiku.wusan.inside2.client.bean.tq.WusanInside2TagInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/05/15
 */
public class WusanInside2ApiTqTest {
    
    public final WusanInside2Client wusanInside2Client;
    
    public final WusanInside2ApiTq wusanInside2ApiTq;
    
    public enum WusanInside2Env {
        TEST,
        PRE_PRODUCT,
        PRODUCT
    }
    
    public WusanInside2ApiTqTest() {
        WusanInside2Env type = WusanInside2Env.PRE_PRODUCT;
        String wusanInside2Host;
        String wusanInside2RequestToken;
        switch (type) {
            case TEST:
                wusanInside2Host = "https://open-test.53inside.com/api";
                wusanInside2RequestToken = "5hbggSo0YZzOQx3ByGjFXB6S4mLaLFHy2GR5XICvrg5A6udjnqKiYIHTadFBEvhW66vdN1rxax6yeSqSVHDcC1CkJxIfjO2Ua4QwHa5JFU6T";
                break;
            case PRE_PRODUCT:
                wusanInside2Host = "https://open-pre.53inside.com/api";
                wusanInside2RequestToken = "iR5caQiT2SuZqx4rRMXqgdixECVDiPriQLJEqzF2zzfNztmURruS4HjTCD7U8OVvO8G2M7ab3uCS5LwHBG2kZiltDcMtMCHGJNdnbh5ZDSqQyyKn86r1XrBDULtzrP6iw";
                break;
            case PRODUCT:
                wusanInside2Host = "https://open.53inside.com/api";
                wusanInside2RequestToken = "iR5caQiT2SuZqx4rRMXqgdixECVDiPriQLJEqzF2zzfNztmURruS4HjTCD7U8OVvO8G2M7ab3uCS5LwHBG2kZiltDcMtMCHGJNdnbh5ZDSqQyyKn86r1XrBDULtzrP6iw";
                break;
            default:
                throw new CommonException(ResponseStatusEnum.PARAMETER_ERROR, "??");
        }
        wusanInside2Client = new WusanInside2Client();
        wusanInside2Client.setRestTemplate(new RestTemplate());
        wusanInside2Client.setWusanInside2Host(wusanInside2Host);
        
        wusanInside2ApiTq = new WusanInside2ApiTq();
        wusanInside2ApiTq.setWusanInsideTqClient(wusanInside2Client);
        wusanInside2ApiTq.setWusanInside2RequestToken(wusanInside2RequestToken);
    }
    
    /** 1.查询学科学段树 */
    @Test
    public void schoolSearchSubjectTest() {
        WusanInside2SchoolSearchSectionSubjectRequest request = new WusanInside2SchoolSearchSectionSubjectRequest();
        System.out.println(JSONUtil.toJson(wusanInside2ApiTq.schoolSearchSubject(request)).toLowerCase());
    }
    
    /** 2.根据维度key集合批量查询标签 */
    @Test
    public void schoolBatchQueryTagsTest() {
        WusanInside2SchoolBatchQueryTagsRequest request = new WusanInside2SchoolBatchQueryTagsRequest();
        request.setDimensionKeyList(Stream.of("level","sysSubject","examType","year","grade").collect(Collectors.toList()));
        System.out.println(JSONUtil.toJson(wusanInside2ApiTq.schoolBatchQueryTags(request)));
    }
    
    /**  3.根据学科学段查询标签 */
    @Test
    public void schoolQueryTagByLevelSubjectTest() {
        WusanInside2SchoolQueryTagByLevelSubjectRequest request = new WusanInside2SchoolQueryTagByLevelSubjectRequest();
        request.setSectionId("2aca38aa-7d38-4f8d-829d-a5d804eab0ab");
        request.setSubjectId("2B0D8944-7209-4C26-B4C2-99E48665D1AD");
        List<String> tagDimKeyList = Stream.of("questionTypeId", "difficultyId", "coreCompetencies",
                "testCharacteristics", "inspectionRequirements", "keyCapabilities"
        ).collect(Collectors.toList());
        Map<String, List<WusanInside2TagInfo>> key2tagList = new LinkedHashMap<>();
        for (String tagDimKey : tagDimKeyList) {
            request.setTagDimKey(tagDimKey);
            List<WusanInside2TagInfo> tagList = wusanInside2ApiTq.schoolQueryTagByLevelSubject(request);
            key2tagList.put(tagDimKey, tagList);
        }
        System.out.println(JSONUtil.toJson(key2tagList));
    }
    
    // /** 6.根据二维码查询章节内容 */
    // @Test
    // public void schoolQueryTqPageByQrcodeNameTest() {
    //     WusanInside2SchoolQueryTqPageByQrcodeNameRequest request = new WusanInside2SchoolQueryTqPageByQrcodeNameRequest();
    //     request.setCurrent(1);
    //     request.setSize(50);
    //     request.setQrcodeName("W0-U021");
    //     System.out.println(JSONUtil.toJson(wusanInside2ApiTq.schoolQueryTqPageByQrcodeName(request)));
    // }
    
    /**
     * 7. 根据二维码查询章节内容(返回试题数据结构与试题分页查询一致)
     *    使用二维码查询章节内容。建议使用每个章节的第一个二维码查询即可。
     */
    @Test
    public void schoolQueryContentListByQrcodeTest() {
        WusanInside2SchoolQueryContentListByQrcodeRequest request = new WusanInside2SchoolQueryContentListByQrcodeRequest();
        // request.setQrcodeName("W0-U021"); // 生产环境
        // request.setQrcodeName("W0-S011"); // 测试环境
        // request.setQrcodeName("W0-S021"); // 测试环境
        // request.setQrcodeName("W0-S031"); // 测试环境
        request.setQrcodeName("W0-U011"); // 测试环境
        List<Map<String, Object>> schoolContentMapList = wusanInside2ApiTq.schoolQueryContentListByQrcode(request);
        System.out.println(JSONUtil.toJson(schoolContentMapList));
        List<Map<String, Object>> schoolContentTreeMapList = TreeUtil.list2Tree(schoolContentMapList, "contentId", "contentParentId", "children");
        System.out.println(JSONUtil.toJson(schoolContentTreeMapList));
        
        String json = JSONUtil.toJson(schoolContentTreeMapList);
        List<WusanInside2SchoolContentTree> schoolContentTreeList = JSONUtil.parseToList(json, WusanInside2SchoolContentTree.class);
        System.out.println(JSONUtil.toJson(schoolContentTreeList));
    }
    
    /**
     * 8. 根据试题id获取试题信息
     */
    @Test
    public void schoolQueryContentListByQrcode() {
        WusanInside2SchoolListQuestionsRequest request = new WusanInside2SchoolListQuestionsRequest();
        request.setQuestionIds(Stream.of(
                "1922221997905371142",  // 普普通通填空题
                "1922221997909565448", // 选词填空 有小题
                "1922221998018617353",  // 七选五
                "1922221997968285711",  // 小题选择题
                "1922221997968285712",  // 1922221997968285711的小题 不是大题 检索不到 我们也不需要这样的题 正常
                "1750771842355257346",  // 杨老师给的综合题 全是填空
                "1704339480467046403",  // 杨老师给的综合题 第四小题是选择题
                "")
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList()));
        List<Map<String, Object>> schoolQuestionList = wusanInside2ApiTq.schoolListQuestions(request);
        System.out.println(JSONUtil.toJson(schoolQuestionList));
        
        List<String> dimKeySortList = Stream.of("edition", "level", "sysSubject", "grade", "year", "difficultyId", "questionTypeId", "knowledgePoints")
                .collect(Collectors.toList());
        Map<String, Integer> dimKey2Sort = new HashMap<>();
        for (int i = 0; i < dimKeySortList.size(); i++) {
            String dimKey = dimKeySortList.get(i);
            dimKey2Sort.put(dimKey, i);
        }
        
        for (Map<String, Object> question : schoolQuestionList) {
            System.out.println("========================== " + MapUtil.getTrim(question, "tqId"));
            System.out.println(question.get("tqId") + "\t" + question.get("tqNumber") + question.get("tqNumberSymbol"));
            List<Map<String, Object>> children = MapUtil.getCast(question, "children");
            for (Map<String, Object> subQuestion : children) {
                System.out.println("\t" + subQuestion.get("tqId") + "\t" + subQuestion.get("tqNumber") + subQuestion.get("tqNumberSymbol"));
            }
            List<Map<String, Object>> tagList = MapUtil.getCast(question, "tagList");
            tagList.sort(ComparatorEx
                    .<Map<String, Object>, Integer>ascNullLast(item -> dimKey2Sort.get(MapUtil.getTrimNullable(item, "tagDimKey")))
                    .thenAsc(item -> MapUtil.getTrim(item, "tqId"))
                    .thenAsc(item -> MapUtil.getTrim(item, "subTqId"))
            );
            for (Map<String, Object> tag : tagList) {
                System.out.println(tag.get("tagDimKey")
                    + "\t" + tag.get("tqId")
                    + "\t" + tag.get("subTqId")
                    + "\t" + tag.get("tagId")
                    + "\t" + tag.get("tagName")
                );
            }
        }
        
        List<WusanInside2Question> schoolContentTreeObjList = schoolQuestionList.stream()
                .map(item -> BeanUtil.mapToBean(item, WusanInside2Question.class,false, CopyOptions.create()))
                .collect(Collectors.toList());
        System.out.println(JSONUtil.toJson(schoolContentTreeObjList));
    }
    
    /**
     * 9. 根据试卷id获取试题
     */
    @Test
    public void schoolListQuestionsByPaperId() {
        WusanInside2SchoolListQuestionsByPaperIdRequest request = new WusanInside2SchoolListQuestionsByPaperIdRequest();
        request.setPaperId("1927206959264776193");
        WusanInside2SchoolListQuestionsByPaperIdResponse response = wusanInside2ApiTq.schoolListQuestionsByPaperId(request);
        System.out.println(JSONUtil.toJson(response));
    }
    
    /**
     * 10. 根据试题id获取试题信息
     */
    @Test
    public void schoolQueryTagMapping() {
        WusanInside2SchoolQueryTagMappingRequest request = new WusanInside2SchoolQueryTagMappingRequest();
        WusanInside2SchoolQueryTagMappingRequest parse = JSONUtil.parse(JSONUtil.toJson(request), WusanInside2SchoolQueryTagMappingRequest.class);
        List<Map<String, Object>> tagMappingList = wusanInside2ApiTq.schoolQueryTagMapping(request);
        List<Map<String, Object>> subList = ListUtil.sub(tagMappingList, 0, 10);
        System.out.println(JSONUtil.toJson(subList));
    }
    
    /***
     * 输出所有的题型
     */
    @Test
    public void getAllQuestionTypeTest() {
        WusanInside2SchoolSearchSectionSubjectRequest request = new WusanInside2SchoolSearchSectionSubjectRequest();
        List<WusanInside2TagInfo> stageList = wusanInside2ApiTq.schoolSearchSubject(request);
        Map<String, List<WusanInside2TagInfo>> stageSubject2questionTypeList = new HashMap<>();
        for (WusanInside2TagInfo stageTag : stageList) {
            String wusanStageId = stageTag.getId();
            String wusanStageName = stageTag.getName();
            List<WusanInside2TagInfo> subjectList = stageTag.getChildren();
            for (WusanInside2TagInfo subjectTag : subjectList) {
                String wusanSubjectId = subjectTag.getId();
                String wusanSubjectName = subjectTag.getName();
                String wusanCourseId = wusanStageId + "_" + wusanSubjectId;
                String wusanCourseName = wusanStageName + wusanSubjectName;
                WusanInside2SchoolQueryTagByLevelSubjectRequest questionTypeRequest = new WusanInside2SchoolQueryTagByLevelSubjectRequest();
                questionTypeRequest.setSectionId(wusanStageId);
                questionTypeRequest.setSubjectId(wusanSubjectId);
                questionTypeRequest.setTagDimKey("questionTypeId");
                List<WusanInside2TagInfo> questionTypeList = wusanInside2ApiTq.schoolQueryTagByLevelSubject(questionTypeRequest);
                for (WusanInside2TagInfo questionTypeTag : questionTypeList) {
                    String wusanQuestionTypeName = questionTypeTag.getName();
                    List<WusanInside2TagInfo> questionTypeChildren = questionTypeTag.getChildren();
                    if (CollectionUtils.isNotEmpty(questionTypeChildren)) {
                        throw new CommonException(ResponseStatusEnum.DATA_ERROR, "出现了子题型: " + wusanCourseName + " : " + wusanQuestionTypeName);
                    }
                }
                stageSubject2questionTypeList.put(wusanCourseId, questionTypeList);
            }
        }
        
        for (WusanInside2TagInfo stageTag : stageList) {
            String wusanStageId = stageTag.getId();
            String wusanStageName = stageTag.getName();
            List<WusanInside2TagInfo> subjectList = stageTag.getChildren();
            for (WusanInside2TagInfo subjectTag : subjectList) {
                String wusanSubjectId = subjectTag.getId();
                String wusanSubjectName = subjectTag.getName();
                String wusanCourseId = wusanStageId + "_" + wusanSubjectId;
                String wusanCourseName = wusanStageName + wusanSubjectName;
                List<WusanInside2TagInfo> questionTypeList = stageSubject2questionTypeList.get(wusanCourseId);
                for (WusanInside2TagInfo questionTypeTag : questionTypeList) {
                    String wusanQuestionTypeId = questionTypeTag.getId();
                    String wusanQuestionTypeName = questionTypeTag.getName();
                    System.out.println(wusanCourseId
                            + "\t" + wusanCourseName
                            + "\t" + wusanStageId
                            + "\t" + wusanStageName
                            + "\t" + wusanSubjectId
                            + "\t" + wusanSubjectName
                            + "\t" + wusanQuestionTypeId
                            + "\t" + wusanQuestionTypeName
                    );
                    
                }
            }
        }
        
    }
    
    @Test
    public void testAllCourseDifficulty() {
        WusanInside2SchoolSearchSectionSubjectRequest request = new WusanInside2SchoolSearchSectionSubjectRequest();
        List<WusanInside2TagInfo> stageList = wusanInside2ApiTq.schoolSearchSubject(request);
        
        List<Map<String, Object>> difficultyList = new ArrayList<>();
        for (WusanInside2TagInfo stageTag : stageList) {
            String wusanStageId = stageTag.getId();
            String wusanStageName = stageTag.getName();
            List<WusanInside2TagInfo> subjectList = stageTag.getChildren();
            for (WusanInside2TagInfo subjectTag : subjectList) {
                String wusanSubjectId = subjectTag.getId();
                String wusanSubjectName = subjectTag.getName();
                String wusanCourseId = wusanStageId + "_" + wusanSubjectId;
                String wusanCourseName = wusanStageName + wusanSubjectName;
                
                WusanInside2SchoolQueryTagByLevelSubjectRequest questionTypeRequest = new WusanInside2SchoolQueryTagByLevelSubjectRequest();
                questionTypeRequest.setSectionId(wusanStageId);
                questionTypeRequest.setSubjectId(wusanSubjectId);
                questionTypeRequest.setTagDimKey("difficultyId");
                List<WusanInside2TagInfo> courseDifficultyList = wusanInside2ApiTq.schoolQueryTagByLevelSubject(questionTypeRequest);
                for (WusanInside2TagInfo courseDifficulty : courseDifficultyList) {
                    String wusanDifficultyId = courseDifficulty.getId();
                    String wusanDifficultyName = courseDifficulty.getName();
                    System.out.println(wusanCourseId
                            + "\t" + wusanCourseName
                            + "\t" + wusanStageId
                            + "\t" + wusanStageName
                            + "\t" + wusanSubjectId
                            + "\t" + wusanSubjectName
                            + "\t" + wusanDifficultyId
                            + "\t" + wusanDifficultyName
                    );
                    difficultyList.add(new Document().append("difficultyId", wusanDifficultyId).append("difficultyName", wusanDifficultyName));
                }
            }
        }
        difficultyList = difficultyList.stream().distinct().collect(Collectors.toList());
        System.out.println(JSONUtil.toJson(difficultyList));
    }
    
    @Test
    public void testSaveWusanInside2PaperScore() {
        Map<String, String> qrcode2Qrcodes = new LinkedHashMap<>();
        qrcode2Qrcodes.put("W0-R011", "W0-R011");
        qrcode2Qrcodes.put("W0-R021", "W0-R021");
        qrcode2Qrcodes.put("W0-R031", "W0-R031");
        qrcode2Qrcodes.put("W0-R041", "W0-R041 W0-R042");
        qrcode2Qrcodes.put("W0-T011", "W0-T011");
        qrcode2Qrcodes.put("W0-T021", "W0-T021");
        qrcode2Qrcodes.put("W0-T032", "W0-T032");
        qrcode2Qrcodes.put("W0-T041", "W0-T041");
        qrcode2Qrcodes.put("W0-U011", "W0-U011 W0-U012");
        qrcode2Qrcodes.put("W0-U021", "W0-U021 W0-U022");
        qrcode2Qrcodes.put("W0-U031", "W0-U031 W0-U032");
        qrcode2Qrcodes.put("W0-U041", "W0-U041 W0-U042");
        qrcode2Qrcodes.put("W0-V011", "W0-V011");
        qrcode2Qrcodes.put("W0-V021", "W0-V021 W0-V022");
        qrcode2Qrcodes.put("W0-V031", "W0-V031 W0-V032");
        qrcode2Qrcodes.put("W0-V041", "W0-V041");
        qrcode2Qrcodes.put("W0-S011", "W0-S011");
        qrcode2Qrcodes.put("W0-S021", "W0-S021");
        qrcode2Qrcodes.put("W0-S031", "W0-S031");
        Map<String, List<Map<String, Object>>> qrcode2SchoolContentTreeMapList = new HashMap<>();
        for (String qrcode : qrcode2Qrcodes.keySet()) {
            WusanInside2SchoolQueryContentListByQrcodeRequest request = new WusanInside2SchoolQueryContentListByQrcodeRequest();
            request.setQrcodeName(qrcode);
            List<Map<String, Object>> schoolContentMapList = wusanInside2ApiTq.schoolQueryContentListByQrcode(request);
            List<Map<String, Object>> schoolContentTreeMapList = TreeUtil.list2Tree(schoolContentMapList, "contentId", "contentParentId", "children");
            qrcode2SchoolContentTreeMapList.put(qrcode, schoolContentTreeMapList);
        }
        for (Map.Entry<String, String> entry : qrcode2Qrcodes.entrySet()) {
            String qrcode = entry.getKey();
            String qrcodes = entry.getValue();
            List<Map<String, Object>> schoolContentTreeMapList = qrcode2SchoolContentTreeMapList.get(qrcode);
            List<Map<String, Object>> questionContentList = __getQuestionContentList(schoolContentTreeMapList);
            for (Map<String, Object> questionContent : questionContentList) {
                Map<String, Object> wusanQuestion = MapUtil.getCast(questionContent, "schoolTqInfoRsp");
                String questionTqId = MapUtil.getTrim(wusanQuestion, "tqId");
                __printScore(qrcodes, questionTqId, Stream.of(wusanQuestion).collect(Collectors.toList()));
            }
        }
    }
    
    private List<Map<String, Object>> __getQuestionContentList(List<Map<String, Object>> schoolContentTreeList) {
        List<Map<String, Object>> questionContentList = new ArrayList<>();
        __collectQuestionContentList(questionContentList, schoolContentTreeList);
        return questionContentList;
        
    }
    private void __collectQuestionContentList(List<Map<String, Object>> questionContentList, List<Map<String, Object>> schoolContentTreeList) {
        if (CollectionUtils.isEmpty(schoolContentTreeList)) {
            return;
        }
        for (Map<String, Object> schoolContentTree : schoolContentTreeList) {
            if (9 == MapUtil.getInt(schoolContentTree, "contentType", 0)) {
                questionContentList.add(schoolContentTree);
            } else {
                __collectQuestionContentList(questionContentList, MapUtil.getCast(schoolContentTree, "children"));
            }
        }
    }
    
    private void __printScore(String qrcodes, String questionTqId, List<Map<String, Object>> wusanQuestionList) {
        if (CollectionUtils.isEmpty(wusanQuestionList)) {
            return;
        }
        for (Map<String, Object> wusanQuestion : wusanQuestionList) {
            String questionSubTqId = MapUtil.getTrim(wusanQuestion, "tqId");
            String score = MapUtil.getTrim(wusanQuestion, "score", "");
            String questionSubTqIdDisplay = questionSubTqId;
            if (questionTqId.equals(questionSubTqId)) {
                questionSubTqIdDisplay = "-";
            }
            System.out.println(qrcodes + "\t" + questionTqId + "\t" + questionSubTqIdDisplay + "\t" + score);
            List<Map<String, Object>> wusanSubQuestionList = MapUtil.getCast(wusanQuestion, "children");
            __printScore(qrcodes, questionTqId, wusanSubQuestionList);
        }
    }
}
