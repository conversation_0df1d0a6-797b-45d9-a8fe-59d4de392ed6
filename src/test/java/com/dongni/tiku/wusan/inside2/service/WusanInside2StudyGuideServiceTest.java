package com.dongni.tiku.wusan.inside2.service;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2StudyGuidePaperInfo;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2TransferToDongniDTO;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2TransferToDongniErrorInfo;
import com.dongni.tiku.wusan.inside2.bean.tq.WusanInside2SchoolContentTree;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/05/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WusanInside2StudyGuideServiceTest {
    @Autowired
    private WusanInside2StudyGuideService wusanInside2StudyGuideService;
    
    @Test
    public void test() {
        WusanInside2StudyGuidePaperInfo studyGuidePaper = wusanInside2StudyGuideService.getStudyGuidePaper("W0-S011");
        List<WusanInside2SchoolContentTree> wusanPaperStructureList = studyGuidePaper.getWusanPaperStructureList();
        System.out.println(JSONUtil.toJson(wusanPaperStructureList));
        WusanInside2TransferToDongniDTO transferResult = studyGuidePaper.getTransferResult();
        List<Map<String, Object>> dongniQuestionList = transferResult.getDongniQuestionList();
        System.out.println(JSONUtil.toJson(dongniQuestionList));
        List<WusanInside2TransferToDongniErrorInfo> errorList = transferResult.getWusanInsideTransferToDongniErrorInfoList();
        System.out.println(JSONUtil.toJson(errorList));
    }
}
