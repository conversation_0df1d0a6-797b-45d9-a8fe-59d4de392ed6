package com.dongni.tiku.wusan.inside.utils;

import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2024/07/11
 */
public class WusanInsideAesUtilTest {
    
    @Test
    public void test() {
        // 解密演示路径的p参数到底是个啥
//        System.out.println(WusanInsideAesUtil.decrypt("vNCvwtsxeXRSpBFVyvMcob%2BQZA6kDdw8z2DurtoFW6w%3D"));
        // 是account啊
//        System.out.println(WusanInsideAesUtil.encrypt("***********"));
    }
    
}
