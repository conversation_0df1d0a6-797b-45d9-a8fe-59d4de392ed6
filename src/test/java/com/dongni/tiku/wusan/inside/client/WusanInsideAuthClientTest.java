package com.dongni.tiku.wusan.inside.client;


import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideAccountAdminInfoRequest;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideAccountAdminInfoResponse;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

/**
 *
 * <AUTHOR>
 * @date 2024/07/19
 */
public class WusanInsideAuthClientTest {
    
    @Test
    public void test() {
        
        WusanInsideAuthClient wusanInsideAuthClient = new WusanInsideAuthClient();
        wusanInsideAuthClient.setRestTemplate(new RestTemplate());
        
        WusanInsideAccountAdminInfoRequest wusanInsideAccountAdminInfoRequest = new WusanInsideAccountAdminInfoRequest();
        wusanInsideAccountAdminInfoRequest.setAccount("<EMAIL>");
        System.out.println(JSONUtil.toJson(wusanInsideAccountAdminInfoRequest));
        WusanInsideResponse<WusanInsideAccountAdminInfoResponse> result = wusanInsideAuthClient.post(
                "/v1/account/admin/info",
                wusanInsideAccountAdminInfoRequest,
                WusanInsideAccountAdminInfoResponse.class
        );
        System.out.println(JSONUtil.toJson(result));
        
        
    }
}
