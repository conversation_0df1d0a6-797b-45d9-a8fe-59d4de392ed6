package com.dongni.tiku.wusan.inside.serivce;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.wusan.inside.bean.WusanInsideResourcePaperInfo;
import com.dongni.tiku.wusan.inside.bean.WusanInsideResourceTqInfo;
import com.dongni.tiku.wusan.inside.client.WusanInsideResponse;
import com.dongni.tiku.wusan.inside.client.WusanInsideTqClient;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideResourcePaperInfoRequest;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideResourcePaperInfoResponse;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideResourceTqSearchRequest;
import com.dongni.tiku.wusan.inside.client.bean.WusanInsideResourceTqSearchResponse;
import com.dongni.tiku.wusan.inside.service.WusanInsideTqApi;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/07/19
 */
public class WusanInsideTqApiTest {
    
    private static final Logger log = LoggerFactory.getLogger(WusanInsideTqApiTest.class);
    
    private final WusanInsideTqApi wusanInsideTqApi;
    
    public WusanInsideTqApiTest() {
        RestTemplate restTemplate = new RestTemplate();
        WusanInsideTqClient wusanInsideTqClient = new WusanInsideTqClient();
        wusanInsideTqClient.setRestTemplate(restTemplate);
        wusanInsideTqApi = new WusanInsideTqApi();
        wusanInsideTqApi.setWusanInsideTqClient(wusanInsideTqClient);
    }
    
    @Test
    public void testPaper() {
        System.out.println();
        System.out.println(" ----------------------------------------- ");
        System.out.println();
        
        WusanInsideResourcePaperInfoRequest wusanInsideResourcePaperInfoRequest = new WusanInsideResourcePaperInfoRequest();
        wusanInsideResourcePaperInfoRequest.setPaperId("1811343135340539912");
        Map<String, Object> paperMap = wusanInsideTqApi.resourcePaperInfo(wusanInsideResourcePaperInfoRequest);
        WusanInsideResourcePaperInfo wusanPaper = BeanUtil.mapToBean(paperMap, WusanInsideResourcePaperInfoResponse.class, false, CopyOptions.create());
        System.out.println(JSON.toJson(wusanPaper));
        System.out.println(JSON.toJson(paperMap));
        
        System.out.println();
        System.out.println(" ----------------------------------------- ");
        System.out.println();
        
        WusanInsideResourceTqSearchRequest wusanInsideResourceTqSearchRequest = new WusanInsideResourceTqSearchRequest();
        wusanInsideResourceTqSearchRequest.setLevelCodeList(Stream.of(3).map(Object::toString).collect(Collectors.toList()));
        wusanInsideResourceTqSearchRequest.setSubjectCodeList(Stream.of(2).map(Object::toString).collect(Collectors.toList()));
        wusanInsideResourceTqSearchRequest.setBusinessTqType(Stream.of(1).map(Object::toString).collect(Collectors.toList()));
        WusanInsideResponse<List<Map<String, Object>>> qtResponse = wusanInsideTqApi.resourceTqSearch(wusanInsideResourceTqSearchRequest);
        List<Map<String, Object>> wusanQuestionListMap = qtResponse.getData();
        List<WusanInsideResourceTqInfo> wusanQuestionList = wusanQuestionListMap.stream()
                .map(item -> BeanUtil.mapToBean(item, WusanInsideResourceTqSearchResponse.class,false, CopyOptions.create()))
                .<WusanInsideResourceTqInfo>map(MapUtil::getCast)
                .collect(Collectors.toList());
        System.out.println(JSON.toJson(wusanQuestionList));
        System.out.println(JSON.toJson(wusanQuestionListMap));
        
    }
    
    @Test
    public void checkWusanPaper() {
        List<String> wusanPaperIdList = Stream.of(
                        "1811343135340539912",
                        "1818181249972211737",
                        "1818182099775950873",
                        "1818183219164385300",
                        "1818183778680344582",
                        "1818184413580529681",
                        "1818185022027239446",
                        null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        for (String wusanPaperId : wusanPaperIdList) {
            Document errorMsg = new Document();
            errorMsg.put("paperId", wusanPaperId);
            WusanInsideResourcePaperInfoRequest wusanInsideResourcePaperInfoRequest = new WusanInsideResourcePaperInfoRequest();
            wusanInsideResourcePaperInfoRequest.setPaperId(wusanPaperId);
            Map<String, Object> paperMap = wusanInsideTqApi.resourcePaperInfo(wusanInsideResourcePaperInfoRequest);
            WusanInsideResourcePaperInfo wusanPaper = BeanUtil.mapToBean(paperMap, WusanInsideResourcePaperInfoResponse.class, false, CopyOptions.create());
            for (WusanInsideResourceTqInfo tqInfo : wusanPaper.getTqInfoList()) {
                errorMsg.put("tqId", tqInfo.getTqId());
                Document errorMsgTq = new Document(errorMsg);
                String errMsg = "";
                if (CollectionUtils.isEmpty(tqInfo.getKnowledgeList())) {
                    errMsg += "知识点为空;";
                }
                if (tqInfo.getTqBusinessType() == null) {
                    errMsg += "题型为空;";
                }
                if (tqInfo.getFacility() == null) {
                    errMsg += "难度为空;";
                }
                if (StringUtils.isNotBlank(errMsg)) {
                    errorMsgTq.put("大题错误", errMsg);
                    log.error("{}", JSONUtil.toJson(errorMsgTq));
                    // log.warn("==== {}\t{}\t{}\t{}\t{}",
                    //         errorMsgTq.getOrDefault("paperId", ""),
                    //         errorMsgTq.getOrDefault("tqId", ""),
                    //         errorMsgTq.getOrDefault("大题错误", ""),
                    //         errorMsgTq.getOrDefault("subTqId", ""),
                    //         errorMsgTq.getOrDefault("小题错误", "")
                    // );
                }
                
                List<WusanInsideResourceTqInfo> subTqList = tqInfo.getChildren();
                if (CollectionUtils.isNotEmpty(subTqList)) {
                    for (WusanInsideResourceTqInfo subTqInfo : subTqList) {
                        Document errorMsgTqSub = new Document(errorMsgTq);
                        errorMsgTqSub.put("subTqId", subTqInfo.getTqId());
                        String subErrMsg = "";
                        if (CollectionUtils.isEmpty(subTqInfo.getKnowledgeList())) {
                            subErrMsg += "知识点为空;";
                        }
                        if (subTqInfo.getTqBusinessType() == null) {
                            subErrMsg += "题型为空;";
                        }
                        if (subTqInfo.getFacility() == null) {
                            subErrMsg += "难度为空;";
                        }
                        if (StringUtils.isNotBlank(subErrMsg)) {
                            errorMsgTqSub.put("小题错误", subErrMsg);
                            log.error("{}", JSONUtil.toJson(errorMsgTqSub));
                            // log.warn("==== {}\t{}\t{}\t{}\t{}",
                            //         errorMsgTqSub.getOrDefault("paperId", ""),
                            //         errorMsgTqSub.getOrDefault("tqId", ""),
                            //         errorMsgTqSub.getOrDefault("大题错误", ""),
                            //         errorMsgTqSub.getOrDefault("subTqId", ""),
                            //         errorMsgTqSub.getOrDefault("小题错误", "")
                            // );
                        }
                        List<WusanInsideResourceTqInfo> subSubTqList = subTqInfo.getChildren();
                        if (CollectionUtils.isNotEmpty(subSubTqList)) {
                            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "a");
                        }
                    }
                }
            }
        }
    }
}
