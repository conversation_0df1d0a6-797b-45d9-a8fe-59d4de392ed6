package com.dongni.tiku.studyguide.service;

import cn.hutool.core.io.FileUtil;
import com.dongni.basedata.studyguide.bean.dto.PublisherDTO;
import com.dongni.common.mongo.IManager;
import com.dongni.common.utils.StackTraceUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.bean.TikuMongodb;
import com.dongni.tiku.studyguide.bean.dto.StudyGuideChapterDTO;
import com.dongni.tiku.studyguide.bean.po.StudyGuideBookPO;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2StudyGuidePaperInfo;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2TransferToDongniDTO;
import com.dongni.tiku.wusan.inside2.bean.dto.WusanInside2TransferToDongniErrorInfo;
import com.dongni.tiku.wusan.inside2.bean.tq.WusanInside2SchoolContentTree;
import com.dongni.tiku.wusan.inside2.service.WusanInside2StudyGuideService;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.ReplaceOptions;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/05/22
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class StudyGuidePaperWusanInside2ServiceTest {
    
    @Autowired
    private StudyGuidePaperWusanInside2Service studyGuidePaperWusanInside2Service;
    @Autowired
    private WusanInside2StudyGuideService wusanInside2StudyGuideService;
    @Autowired
    private TikuMongodb tikuMongodb;
    
    @Test
    public void testSaveWusanInside2Paper() {
        WusanInside2StudyGuidePaperInfo studyGuidePaper = wusanInside2StudyGuideService.getStudyGuidePaper("W0-S011");
        List<WusanInside2SchoolContentTree> wusanPaperStructureList = studyGuidePaper.getWusanPaperStructureList();
        System.out.println(JSONUtil.toJson(wusanPaperStructureList));
        WusanInside2TransferToDongniDTO transferResult = studyGuidePaper.getTransferResult();
        List<Map<String, Object>> dongniQuestionList = transferResult.getDongniQuestionList();
        System.out.println(JSONUtil.toJson(dongniQuestionList));
        List<WusanInside2TransferToDongniErrorInfo> errorList = transferResult.getWusanInsideTransferToDongniErrorInfoList();
        System.out.println(JSONUtil.toJson(errorList));
        Map<Integer, Integer> questionType2UnitType = transferResult.getQuestionType2UnitType();
        
        if (CollectionUtils.size(errorList) > 0) {
            return;
        }
        
        PublisherDTO publisherDTO = new PublisherDTO();
        publisherDTO.setAreaId(10086L);
        
        StudyGuideBookPO bookPO = new StudyGuideBookPO();
        bookPO.setCourseId(0L);
        bookPO.setCourseName("手动测试");
        bookPO.setGradeType(10);
        bookPO.setStage(3);
        bookPO.setCreateDateTime(new Date());
        
        StudyGuideChapterDTO chapterDTO = new StudyGuideChapterDTO();
        chapterDTO.setPaperId(10086L);
        chapterDTO.setChapterName("转换测试");
        studyGuidePaperWusanInside2Service.saveWusanInside2Paper(
                publisherDTO, bookPO, chapterDTO,
                wusanPaperStructureList, dongniQuestionList, questionType2UnitType
        );
    }
    
    @Test
    public void testSaveWusanInside2PaperList() {
        // String datePure = "20250529";
        // List<String> qrcodesList = Stream.of(
        //                 "W0-R011",
        //                 "W0-R021",
        //                 "W0-R031",
        //                 "W0-R041,W0-R042",
        //                 "W0-S011",
        //                 "W0-S021",
        //                 "W0-S031",
        //                 "W0-T011",
        //                 "W0-T021",
        //                 "W0-T032",
        //                 "W0-T041",
        //                 "W0-U011,W0-U012",
        //                 "W0-U021,W0-U022",
        //                 "W0-U031,W0-U032",
        //                 "W0-U041,W0-U042",
        //                 "W0-V011",
        //                 "W0-V021,W0-V022",
        //                 "W0-V031,W0-V032",
        //                 "W0-V041",
        //         null
        //         )
        //         .filter(Objects::nonNull)
        //         .collect(Collectors.toList());
        
        String datePure = "20250604";
        List<String> qrcodesList = Stream.of(
                        "W2-H011",
                        "W2-J011",
                        "W2-M011",
                        null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        IManager testStudyGuideWusanInsideManager = new IManager(tikuMongodb, "testStudyGuideWusanInside" + datePure, "测试五三试卷转换") {
            @Override
            protected void indexInit() {
                initIndex("uk_qrcode", new Document("qrcode", 1.0), new IndexOptions().unique(true));
            }
        };
        
        
        for (String qrcodes : qrcodesList) {
            String[] split = qrcodes.split(",");
            String qrcode = split[0];
            System.out.println(" -------------------------------------------" + qrcodes);
            WusanInside2StudyGuidePaperInfo studyGuidePaper = wusanInside2StudyGuideService.getStudyGuidePaper(qrcode);
            List<Map<String, Object>> schoolContentTreeMapList = studyGuidePaper.getSchoolContentTreeMapList();
            System.out.println(JSONUtil.toJson(schoolContentTreeMapList));
            List<WusanInside2SchoolContentTree> wusanPaperStructureList = studyGuidePaper.getWusanPaperStructureList();
            System.out.println(JSONUtil.toJson(wusanPaperStructureList));
            WusanInside2TransferToDongniDTO transferResult = studyGuidePaper.getTransferResult();
            List<Map<String, Object>> dongniQuestionList = transferResult.getDongniQuestionList();
            Map<Integer, Integer> questionType2UnitType = transferResult.getQuestionType2UnitType();
            List<WusanInside2TransferToDongniErrorInfo> errorList = transferResult.getWusanInsideTransferToDongniErrorInfoList();
            Document studyGuidePaperDoc = null;
            String paperError = null;
            if (CollectionUtils.isEmpty(errorList)) {
                try {
                    PublisherDTO publisherDTO = new PublisherDTO();
                    publisherDTO.setAreaId(10086L);
                    StudyGuideBookPO bookPO = new StudyGuideBookPO();
                    bookPO.setCourseId(1L);
                    bookPO.setCourseName("手动测试假的课程");
                    bookPO.setGradeType(10);
                    bookPO.setStage(3);
                    bookPO.setCreateDateTime(new Date());
                    StudyGuideChapterDTO chapterDTO = new StudyGuideChapterDTO();
                    chapterDTO.setPaperId(10086L);
                    chapterDTO.setChapterName("转换测试");
                    studyGuidePaperDoc = studyGuidePaperWusanInside2Service.saveWusanInside2Paper(
                            publisherDTO, bookPO, chapterDTO,
                            wusanPaperStructureList, dongniQuestionList, questionType2UnitType
                    );
                    System.out.println(JSONUtil.toJson(studyGuidePaperDoc));
                } catch (Exception e) {
                    paperError = getStackTrace(e);
                    System.out.println(e.getMessage());
                }
                
            }
            Document testStudyGuideWusanInside = new Document();
            testStudyGuideWusanInside.put("qrcode", qrcode);
            testStudyGuideWusanInside.put("qrcodes", qrcodes);
            testStudyGuideWusanInside.put("mapTree", schoolContentTreeMapList);
            testStudyGuideWusanInside.put("beanTree", JSONUtil.parseToListMap(JSONUtil.toJson(wusanPaperStructureList)));
            testStudyGuideWusanInside.put("questionList", dongniQuestionList);
            testStudyGuideWusanInside.put("errorList", JSONUtil.parseToListMap(JSONUtil.toJson(errorList)));
            testStudyGuideWusanInside.put("paper", studyGuidePaperDoc);
            testStudyGuideWusanInside.put("paperError", paperError);
            
            Bson query = Filters.eq("qrcode", qrcode);
            
            testStudyGuideWusanInsideManager.replaceOne(query, testStudyGuideWusanInside, new ReplaceOptions().upsert(true));
        }
    }
    
    private String getStackTrace(Throwable throwable) {
        try {
            if (throwable == null) { return null; }
            return StackTraceUtil.printEnclosedStackTrace(throwable);
        } catch (Exception e) {
            return throwable.getMessage();
        }
    }
}
