package com.dongni.tiku.studyguide.service;

import com.dongni.tiku.studyguide.bean.param.StudyGuideBookQrcodeDownloadParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR>
 * @date 2025/04/10
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class StudyGuideBookServiceTest {
    
    @Autowired
    private StudyGuideBookService studyGuideBookService;
    
    @Test
    public void testDownloadBookQrcode() {
        StudyGuideBookQrcodeDownloadParam param = new StudyGuideBookQrcodeDownloadParam();
        param.setStudyGuidePublisherId(90024L);
        param.setStudyGuideBookId(60046L);
        String zipUrl = studyGuideBookService.downloadBookQrcode(param);
        System.out.println(zipUrl);
    }
}
