package com.dongni.tiku.third.jyeoo.service;

import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <br/>
 * @date 2020/07/07 <br/>
 *
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class JyeooUserServiceTest {
    
    @Autowired
    private JyeooUserService jyeooUserService;

    @Autowired
    private JyeooAreaUserService jyeooAreaUserService;
    
    @Test
    public void registerJyeooUsersForTask() {
        jyeooUserService.registerJyeooUsersForTask(MapUtil.of(
                "userId", 2,
                "userName", "菁优注册定时",
                "userType", 7
        ));
    }

    @Test
    public void registerJyeooAreaUsersForTask() {
        jyeooAreaUserService.registerJyeooAreaUsersForTask(MapUtil.of(
                "userId", 2,
                "userName", "菁优注册定时",
                "userType", 7
        ));
    }
}
