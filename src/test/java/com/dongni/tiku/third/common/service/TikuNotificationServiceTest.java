package com.dongni.tiku.third.common.service;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.third.common.utils.SignUtil;
import com.dongni.tiku.third.yiqi.service.YiqiNotificationService;
import com.pugwoo.wooutils.json.JSON;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2020/04/02 <br/>
 *
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class TikuNotificationServiceTest {
    
    private static String SIGN_KEY = "748868c7-84db-4143-b7cc-94d05c2c936e";
    
    @Autowired
    YiqiNotificationService yiqiNotificationService;
    
    @Test
    public void insert() {
        
        long timeStamp = System.currentTimeMillis();
        
        Map<String, Object> params = MapUtil.of(
                "yiqiPaperId", "5ea12b96c32ac3d3f553a6e2",
                "businessType", 1002,
                "status", 2,
                "timestamp", timeStamp,
                "rejectInfo", "题目：5ea12b95c32ac3d3f553a6e1<p>测试打回，收到打回后，请更新<img src=\\\"http://sentinel.oss-cn-beijing.aliyuncs.com/img-d4e2325800fe1968cdabe02b0247d4b7.png\\\" title=\\\"image.png\\\" _src=\\\"http://sentinel.oss-cn-beijing.aliyuncs.com/img-d4e2325800fe1968cdabe02b0247d4b7.png\\\" alt=\\\"image.png\\\"></p>"
        );
        params.put("sign", SignUtil.sign(SIGN_KEY, params));
    
        System.out.println(JSON.toJson(params));
        yiqiNotificationService.notification(params, true);
    }
    
    @Test
    public void sign() {
        
        long timeStamp = System.currentTimeMillis();
        
        Map<String, Object> params = MapUtil.of(
                "yiqiPaperId", "5ea12b96c32ac3d3f553a6e2",
                "businessType", 1002,
                "status", 2,
                "timestamp", timeStamp,
                "rejectInfo", "题目：5ea12b95c32ac3d3f553a6e1<p>测试打回，收到打回后，请更新<img src=\\\"http://sentinel.oss-cn-beijing.aliyuncs.com/img-d4e2325800fe1968cdabe02b0247d4b7.png\\\" title=\\\"image.png\\\" _src=\\\"http://sentinel.oss-cn-beijing.aliyuncs.com/img-d4e2325800fe1968cdabe02b0247d4b7.png\\\" alt=\\\"image.png\\\"></p>"
        );
        params.put("sign", SignUtil.sign(SIGN_KEY, params));
        
        System.out.println(JSON.toJson(params));
        //        yiqiNotificationService.notification(params, true);
    }
    
    public static void main(String[] args) {
        long timeStamp = System.currentTimeMillis();
        Map<String, Object> params = MapUtil.of(
                "yiqiPaperId", "devTest111",
                "businessType", 1001,
                "status", 3,
                "timestamp", timeStamp,
                "rejectInfo", ""
        );
        params.put("sign", SignUtil.sign(SIGN_KEY, params));
        System.out.println(JSON.toJson(params));
    }
}
