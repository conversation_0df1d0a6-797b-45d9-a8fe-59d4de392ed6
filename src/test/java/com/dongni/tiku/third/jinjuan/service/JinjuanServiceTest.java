package com.dongni.tiku.third.jinjuan.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description 学校金卷题库
 * @date 2022年03月29日
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class JinjuanServiceTest {

    @Autowired
    private JinJuanSchoolService jinJuanSchoolService;

    @Test
    public void closeJinjuanSchool() {
        jinJuanSchoolService.closeJinjuanSchool();
    }

}
