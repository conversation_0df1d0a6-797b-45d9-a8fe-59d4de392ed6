package com.dongni.tiku.manager.impl;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static com.mongodb.client.model.Filters.eq;

/**
 *
 * <AUTHOR>
 * @date 2024/06/17
 */
// @SpringBootTest
// @RunWith(SpringRunner.class)
public class AreaQuestionManagerTest {
    
    @Autowired
    private AreaQuestionManager areaQuestionManager;
    
    @Test
    public void test() {
        areaQuestionManager.getFirst(eq("_id", "5d5fb50a7f9e6e616d71956a"));
        areaQuestionManager.getFirst(eq("_id", "5d5fb5f27f9e6e616d7195c3"), new String[]{"_id"});
    }
}
