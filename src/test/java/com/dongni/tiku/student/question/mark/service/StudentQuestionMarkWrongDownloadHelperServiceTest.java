package com.dongni.tiku.student.question.mark.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.exam.wrong.serevice.WrongExamService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/6/4 周三 下午 03:21
 * @Version 1.0.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class StudentQuestionMarkWrongDownloadHelperServiceTest {
    @Autowired
    private WrongExamService wrongExamService;

    @Test
    public void test() {
        String paramStr = "{\n" +
                "  \"fileName\" : \"2025-06-04阿达瓦1数学错题下载\",\n" +
                "  \"outputType\" : 1,\n" +
                "  \"reviewType\" : [ 1, 2, 4 ],\n" +
                "  \"wrongDocumentId\" : 2121216,\n" +
                "  \"documentName\" : \"2025-06-04阿达瓦1数学错题下载.docx\",\n" +
                "  \"userName\" : \"阿达瓦1\",\n" +
                "  \"type\" : 1,\n" +
                "  \"downloadItemList\" : [ {\n" +
                "    \"belongType\" : 0,\n" +
                "    \"wrongItemId\" : 92085962,\n" +
                "    \"wrongItemQuestionFrom\" : 1\n" +
                "  }, {\n" +
                "    \"belongType\" : 4,\n" +
                "    \"studentQuestionMarkWrongItemId\" : 1,\n" +
                "    \"wrongItemQuestionFrom\" : 2\n" +
                "  } ],\n" +
                "  \"userId\" : 4618086,\n" +
                "  \"studentId\" : 3700931,\n" +
                "  \"currentTime\" : \"2025-06-04 15:18:40\",\n" +
                "  \"wrongItems\" : [ {\n" +
                "    \"belongType\" : 0,\n" +
                "    \"questionId\" : \"66ead588cb0f512dce16d9c7\",\n" +
                "    \"endDate\" : 1747756800000,\n" +
                "    \"examName\" : \"发布0521-1\",\n" +
                "    \"examType\" : 2,\n" +
                "    \"structureNumber\" : \"2\",\n" +
                "    \"wrongItemId\" : 92085962,\n" +
                "    \"questionNumber\" : 151,\n" +
                "    \"createDateTime\" : 1747819325000,\n" +
                "    \"studentId\" : 3700931,\n" +
                "    \"wrongItemQuestionFrom\" : 1,\n" +
                "    \"sourceType\" : 1,\n" +
                "    \"examId\" : 1077598111,\n" +
                "    \"courseId\" : 3,\n" +
                "    \"startDate\" : 1747756800000\n" +
                "  }, {\n" +
                "    \"studentId\" : 3700931,\n" +
                "    \"courseId\" : 3,\n" +
                "    \"sourceQuestionId\" : \"66ead588cb0f512dce16d9c7\",\n" +
                "    \"questionId\" : \"3533523957760000\",\n" +
                "    \"belongType\" : 4,\n" +
                "    \"studentQuestionMarkId\" : 12,\n" +
                "    \"studentQuestionMarkName\" : \"个册300014\",\n" +
                "    \"wrongItemQuestionFrom\" : 2,\n" +
                "    \"studentQuestionMarkWrongItemId\" : 1\n" +
                "  } ],\n" +
                "  \"userType\" : 5,\n" +
                "  \"status\" : 0\n" +
                "}";

        String wrongItems = "[ {\n" +
                "  \"belongType\" : 0,\n" +
                "  \"questionId\" : \"66ead588cb0f512dce16d9c7\",\n" +
                "  \"endDate\" : 1747756800000,\n" +
                "  \"examName\" : \"发布0521-1\",\n" +
                "  \"examType\" : 2,\n" +
                "  \"structureNumber\" : \"2\",\n" +
                "  \"wrongItemId\" : 92085962,\n" +
                "  \"questionNumber\" : 151,\n" +
                "  \"createDateTime\" : 1747819325000,\n" +
                "  \"studentId\" : 3700931,\n" +
                "  \"wrongItemQuestionFrom\" : 1,\n" +
                "  \"sourceType\" : 1,\n" +
                "  \"examId\" : 1077598111,\n" +
                "  \"courseId\" : 3,\n" +
                "  \"startDate\" : 1747756800000\n" +
                "}," +
                " {\n" +
                "  \"studentId\" : 3700931,\n" +
                "  \"courseId\" : 3,\n" +
                "  \"sourceQuestionId\" : \"66ead588cb0f512dce16d9c7\",\n" +
                "  \"questionId\" : \"3533523957760000\",\n" +
                "  \"belongType\" : 4,\n" +
                "  \"studentQuestionMarkId\" : 12,\n" +
                "  \"studentQuestionMarkName\" : \"个册300014\",\n" +
                "  \"wrongItemQuestionFrom\" : 2,\n" +
                "  \"studentQuestionMarkWrongItemId\" : 1\n" +
                "}, " +
                " {\n" +
                "  \"studentId\" : 3700931,\n" +
                "  \"courseId\" : 3,\n" +
                "  \"sourceQuestionId\" : \"66ead588cb0f512dce16d9c7\",\n" +
                "  \"questionId\" : \"65fd6508cb0f51513f83128b\",\n" +
                "  \"belongType\" : 0,\n" +
                "  \"studentQuestionMarkId\" : 12,\n" +
                "  \"studentQuestionMarkName\" : \"个册300014\",\n" +
                "  \"wrongItemQuestionFrom\" : 2,\n" +
                "  \"studentQuestionMarkWrongItemId\" : 2\n" +
                "}" +
                "]";

        DongniUserInfoContext userInfoContext = new DongniUserInfoContext(4618086L, "阿达瓦1", 4);
        DongniUserInfoContext.set(userInfoContext);
        wrongExamService.dealDocxFile(JSON.parseObject(paramStr, Map.class),
                JSON.parseObject(wrongItems, List.class));
    }
}
