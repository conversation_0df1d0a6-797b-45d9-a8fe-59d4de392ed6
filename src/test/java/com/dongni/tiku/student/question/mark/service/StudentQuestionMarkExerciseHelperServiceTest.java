package com.dongni.tiku.student.question.mark.service;

import com.alibaba.fastjson.JSON;
import com.dongni.commons.mvc.context.DongniUserInfoContext;
import com.dongni.exam.exercise.student.mq.StudentExercisePlanDownloadMessage;
import com.dongni.exam.exercise.student.service.StudentExercisePlanService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2025/5/30 周五 下午 04:19
 * @Version 1.0.0
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class StudentQuestionMarkExerciseHelperServiceTest {
    @Autowired
    private StudentExercisePlanService studentExercisePlanService;

    @Test
    public void exerciseQuestionDTO2StudentQuestionMarkInsertWrapDTOList() {
        String message = "{\n" +
                "  \"params\" : {\n" +
                "    \"client_check_time\" : \"1748593348659\",\n" +
                "    \"exerciseType\" : \"4\",\n" +
                "    \"wrongDocumentId\" : 2121214,\n" +
                "    \"documentName\" : \"05月30日数学考后巩固\",\n" +
                "    \"userName\" : \"阿达瓦1\",\n" +
                "    \"type\" : 2,\n" +
                "    \"userId\" : 4618086,\n" +
                "    \"exercisePlanId\" : 420096,\n" +
                "    \"exercisePlanStatus\" : 2,\n" +
                "    \"studentId\" : \"3700931\",\n" +
                "    \"currentTime\" : \"2025-05-30 16:30:42\",\n" +
                "    \"accountId\" : 2914,\n" +
                "    \"tokenKey\" : \"LOGIN:TOKEN:247aed96e2fb4ff4b8089f87597ac053___2\",\n" +
                "    \"clientType\" : \"2\",\n" +
                "    \"examId\" : \"**********\",\n" +
                "    \"exercisePlanName\" : \"05月19日数学考后巩固\",\n" +
                "    \"userType\" : 5,\n" +
                "    \"courseId\" : \"3\",\n" +
                "    \"paperId\" : ****************,\n" +
                "    \"status\" : 0\n" +
                "  },\n" +
                "  \"wrongDocumentId\" : 2121216\n" +
                "}";

        DongniUserInfoContext userInfoContext = new DongniUserInfoContext(4618086L, "阿达瓦1", 4);
        DongniUserInfoContext.set(userInfoContext);

        StudentExercisePlanDownloadMessage studentExercisePlanDownloadMessage = JSON.parseObject(message, StudentExercisePlanDownloadMessage.class);
        Map<String, Object> params = studentExercisePlanDownloadMessage.getParams();
        Long wrongDocumentId = studentExercisePlanDownloadMessage.getWrongDocumentId();

        studentExercisePlanService.dealDocxFile(params, wrongDocumentId);
    }
}