package com.dongni.tiku.similar.v3;

import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.similar.StudentLevel;
import com.dongni.tiku.similar.enumeration.Difficulty;
import org.junit.Test;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * 2022/09/22
 */
public class Similar3UtilTest {
    
    @Test
    public void testGetDifficulty2SimilarNum() {
    
//        Collection<ScoreRateLevel> config = Stream.of(
//                new ScoreRateLevel("A", "(0.8,    ]", 0.0, "UP"),
//                new ScoreRateLevel("B", "(0.6, 0.8]", 0.2, "UP"),
//                new ScoreRateLevel("C", "(0.4, 0.6]", 0.4, "DOWN"),
//                new ScoreRateLevel("D", "(0.2, 0.4]", 0.6, "DOWN"),
//                new ScoreRateLevel("E", "(   , 0.2]", 0.8, "DOWN")
//        ).collect(Collectors.toList());
    
        Question3 source = null;
        // new Question3("123")
        //         .setScoringRate(0.66)
        //         .setKnowledgeIdSet(Stream.of("101", "102", "103").collect(Collectors.toSet()))
        //         ;
        Map<String, Integer> questionId2similarNum = MapUtil.of(
                "123", 100
        );
        Map<Difficulty, Map<String, Double>> difficulty2KnowledgeId2GraspRate = MapUtil.of(
                Difficulty.EASY, MapUtil.of("101", 0.91, "102", 0.99, "103", 0.92),
                Difficulty.MIDDLE, MapUtil.of("101", 0.01, "102", 0.01),
                Difficulty.DIFFICULT, MapUtil.of("101", 0.1)
        );
        Map<Difficulty, Double> knowledgeGraspRateTarget = MapUtil.of(
                Difficulty.EASY, 0.90,
                Difficulty.MIDDLE, 0.70,
                Difficulty.DIFFICULT, 0.40
        );
        
        double studentScoreRate = 0.1;
        StudentLevel studentLevel = new StudentLevel("A", 0.0, "UP");
        
        System.out.println(Similar3Util.getDifficulty2SimilarNum(
                source,
                questionId2similarNum.get(source.getQuestionId()),
                difficulty2KnowledgeId2GraspRate,
                knowledgeGraspRateTarget,
                studentScoreRate,
                studentLevel
        ));
    }
    
}
