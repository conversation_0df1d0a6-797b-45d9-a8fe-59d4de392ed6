package com.dongni.tiku.similar.v3;

import cn.hutool.core.collection.CollUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.similar.KnowledgeGraspRate;
import com.dongni.tiku.similar.Range;
import com.dongni.tiku.similar.StudentLevel;
import com.dongni.tiku.similar.enumeration.Difficulty;
import com.dongni.tiku.wrong.book.utils.WrongBookUtil;
import org.apache.commons.collections4.SetUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2022/10/12
 */
public class Similar3Test {
    
    @Test
    public void test() {
        试题充足推题数量测试();
    }
    
    private void 试题充足推题数量测试() {
        boolean debug = true;
        
        // ---------------------------------------------------------- 原题信息
        Question3 sourceQuestion201 = Question3.ofSource("201", Difficulty.DIFFICULT, 0.30, BigDecimal.valueOf(5.0), 0.38, 0.41, 0.20, BigDecimal.valueOf(4.0), Stream.of("k001", "k002").collect(Collectors.toSet()));
        Question3 sourceQuestion202 = Question3.ofSource("202", Difficulty.DIFFICULT, 0.30, BigDecimal.valueOf(5.0), 0.38, 0.20, 0.40, BigDecimal.valueOf(4.5), Stream.of("k001", "k002").collect(Collectors.toSet()));
        List<Question3> sourceList = Stream.of(
                Question3.ofSource("001", Difficulty.EASY, 0.82, BigDecimal.valueOf(5.0), 0.93, 0.73, 0.68,BigDecimal.valueOf(4.0), Stream.of("k001", "k002").collect(Collectors.toSet())),
                Question3.ofSource("002", Difficulty.EASY, 0.82, BigDecimal.valueOf(5.0), 0.93, 0.83, 0.70,BigDecimal.valueOf(4.5), Stream.of("k001", "k002").collect(Collectors.toSet())),
                Question3.ofSource("101", Difficulty.MIDDLE, 0.62, BigDecimal.valueOf(5.0), 0.80, 0.66,0.83, BigDecimal.valueOf(4.0), Stream.of("k001", "k002").collect(Collectors.toSet())),
                Question3.ofSource("102", Difficulty.MIDDLE, 0.62, BigDecimal.valueOf(5.0), 0.80, 0.68, 0.99,BigDecimal.valueOf(5.0), Stream.of("k001", "k002").collect(Collectors.toSet())),
                sourceQuestion201,
                sourceQuestion202,
                Question3.ofSource("301", Difficulty.EASY, 0.89, BigDecimal.valueOf(5.0), 0.88, 0.87, 0.53,BigDecimal.valueOf(5.0), Stream.of("k001", "k002").collect(Collectors.toSet())),
                Question3.ofSource("302", Difficulty.EASY, 0.89, BigDecimal.valueOf(5.0), 0.88, 0.87, 0.59,BigDecimal.valueOf(5.0), Stream.of("k001", "k002").collect(Collectors.toSet()))
        ).collect(Collectors.toList());
        // 所有的原题 因为原题可能没有类题关系 而它又是错的
        Set<Question3> sourceQuestionSet = new HashSet<>(sourceList);
        
        // ---------------------------------------------------------- 老师审题  剔除备选题 自己增加备选题 设置此题不练
        // 此题不练
        Set<String> excludeSourceQuestionIdSet = Stream.of(
                        "001",
                        "302",
                        (String) null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());;
        // 原题类体关系
        List<SourceCandidate3> sourceCandidate3List = new ArrayList<>();
        for (Question3 source : sourceList) {
            for (Difficulty difficulty : Difficulty.values()) {
                for (int i = 0; i < 6; i++) {
                    String questionId = source.getQuestionId() + "_" + (difficulty.getValue() * 100 + i);
                    Question3 candidate = Question3.ofCandidate(questionId, difficulty, null);
                    sourceCandidate3List.add(new SourceCandidate3(source, candidate));
                }
            }
        }
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion202,
                Question3.ofCandidate("201_301", Difficulty.DIFFICULT, CollUtil.newHashSet("A", "B"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion202,
                Question3.ofCandidate("201_302", Difficulty.DIFFICULT, CollUtil.newHashSet("A"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion202,
                Question3.ofCandidate("201_303", Difficulty.DIFFICULT, CollUtil.newHashSet("C"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion202,
                Question3.ofCandidate("201_304", Difficulty.DIFFICULT, CollUtil.newHashSet("D"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion201,
                Question3.ofCandidate("202_301", Difficulty.DIFFICULT, CollUtil.newHashSet("A"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion201,
                Question3.ofCandidate("202_302", Difficulty.DIFFICULT, CollUtil.newHashSet("A", "B", "C", "D"))));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion201,
                Question3.ofCandidate("202_303", Difficulty.DIFFICULT, CollUtil.newHashSet())));
        sourceCandidate3List.add(new SourceCandidate3(sourceQuestion201,
                Question3.ofCandidate("202_304", Difficulty.DIFFICULT, null)));
//        sourceCandidate3List.forEach(System.out::println);
//        System.out.println();
        
        // ---------------------------------------------------------- 学生分层
        StudentLevel studentLevel = new StudentLevel("A", 0.0, "UP");
//        StudentLevel studentLevel = new StudentLevel("B", 0.2, "UP");
//        StudentLevel studentLevel = new StudentLevel("C", 0.4, "DOWN");
//        StudentLevel studentLevel = new StudentLevel("D", 0.6, "DOWN");
//        StudentLevel studentLevel = new StudentLevel("E", 0.8, "DOWN");
        
        // ---------------------------------------------------------- 错题本题量数量
        // 错题本最小题量 控制个性化成长手册的推题数量 学霸除了错题还有按照班级共性错题由难到易进行推题加强练
        int wrongBankQuestionMin = 10;
        // 错题本最大题量 控制个性化成长手册的推题数量 学渣会根据班级共性错题由易到难进行推题
        int wrongBankQuestionMax = 15;
        // 开启按试题得分率区间筛选原错题后，若某题的个人/班级/年级得分率不在定义范围内，此原题及其类题不展示 获取试题的得分率 可以为null
//        Function<Question3, Double> sourceQuestionRateFunction = null;
        Function<Question3, Double> sourceQuestionRateFunction = Question3::getGradeScoringRate;
        // 开启按试题得分率区间筛选原错题后，若某题的个人/班级/年级得分率不在定义范围内，此原题及其类题不展示 得分率区间      可以为null
        Range<Double> sourceQuestionRateRange = Range.parse("[0, 0.80]", MapUtil::getDoubleNullable);
        
        // ---------------------------------------------------------- 推题数设置
        // 推题题目id -> 推题数量
        Map<String, Integer> questionId2SimilarNum = MapUtil.of(
               "001", 4,
               "002", 4,
               "101", 4,
               "102", 0,
               "201", 4,
               "202", 4
        );
        // 推题为0时原题也不展示
        boolean excludeSourceQuestionWhenSimilarNumIsZero = true;
        
        // ---------------------------------------------------------- 其他非页面配置值
        // 增强练习的推题数量
        int consolidateSimilarNum = 2;
        // 知识点达标值  简单->0.90
        Map<Difficulty, Double> knowledgeGraspRateTarget = WrongBookUtil.buildKnowledgeGraspRateTarget();
        // 不能选为推题的题目idSet 比如说最近两次考试的试题不能再被选为推题等情况
        Set<String> excludeCandidateQuestionIdSet = Stream.of(
                        "123456789",
                        (String) null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 知识点掌握程度
        List<KnowledgeGraspRate> knowledgeGraspRateList = new ArrayList<>();
        
        // ---------------------------------------------------------- 必练题
        List<EssentialQuestion3> essentialQuestion3List = Stream.of(
                        null,
                        EssentialQuestion3.of(Question3.ofEssential("必练1"), "102"),
                        EssentialQuestion3.of(Question3.ofEssential("拔高1"), "拔高1"),
                        EssentialQuestion3.of(Question3.ofEssential("必练3"), "201"),
                        EssentialQuestion3.of(Question3.ofEssential("必练4"), "201"),
                        null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        // ---------------------------------------------------------- 拔高题
        
        List<Question3> advancedQuestion3List = Stream.of(
                        null,
                        Question3.ofAdvanced("拔高1"),
                        Question3.ofAdvanced("拔高2"),
                        Question3.ofAdvanced("拔高3"),
                        null
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        
        Similar3Builder similar3Builder = new Similar3Builder();
        Similar3 similar3 = similar3Builder
                .setDebug(debug)
                .setSourceQuestionSet(sourceQuestionSet)
                .setExcludeSourceQuestionIdSet(excludeSourceQuestionIdSet)
                .setSourceCandidate3List(sourceCandidate3List)
                .setStudentLevel(studentLevel)
                .setWrongBankQuestionMin(wrongBankQuestionMin)
                .setWrongBankQuestionMax(wrongBankQuestionMax)
                .setSourceQuestionRateFunction(sourceQuestionRateFunction)
                .setSourceQuestionRateRange(sourceQuestionRateRange)
                .setQuestionId2SimilarNum(questionId2SimilarNum)
                .setExcludeSourceQuestionWhenSimilarNumIsZero(excludeSourceQuestionWhenSimilarNumIsZero)
                .setConsolidateSimilarNum(consolidateSimilarNum)
                .setKnowledgeGraspRateTarget(knowledgeGraspRateTarget)
                .setExcludeCandidateQuestionIdSet(excludeCandidateQuestionIdSet)
                .setKnowledgeGraspRateList(knowledgeGraspRateList)
                .setEssentialQuestion3List(essentialQuestion3List)
                .setAdvancedQuestion3List(advancedQuestion3List)
                
//                .setComputeEssentialQuestion(false)
//                .setComputeAdvancedQuestion(false)
//                .setComputeConsolidateQuestion(false)
                
                .match();
        
        // part 1 必练题
        Set<Question3> essentialResult = similar3.getEssentialResult();
        System.out.println("---------------- part 1 必练题");
        for (Question3 question3 : essentialResult) {
            System.out.println(question3);
        }
        System.out.println();
        
        // part 2 推荐框架返回的类题列表
        Map<Question3, List<Question3>> similarResult = similar3.getSimilarResult();
        System.out.println("---------------- part 2 推题");
        for (Map.Entry<Question3, List<Question3>> entry : similarResult.entrySet()) {
            System.out.println("------ 原题 " + entry.getKey());
            for (Question3 similar : entry.getValue()) {
                System.out.println(similar);
            }
        }
        System.out.println();
        
        // part 3 拔高练习
        Set<Question3> advancedResult = similar3.getAdvancedResult();
        System.out.println("---------------- part 3 拔高练习");
        for (Question3 question3 : advancedResult) {
            System.out.println(question3);
        }
        
        // part 4 加强练习
        Map<Question3, List<Question3>> consolidateResult = similar3.getConsolidateResult();
        System.out.println("---------------- part 4 加强练习");
        for (Map.Entry<Question3, List<Question3>> entry : consolidateResult.entrySet()) {
            System.out.println("------ 原题 " + entry.getKey());
            for (Question3 similar : entry.getValue()) {
                System.out.println(similar);
            }
        }
    }
}
