package com.dongni.tiku.similar.v4;

import cn.hutool.core.collection.CollUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.dongni.tiku.similar.EssentialConfig;
import com.dongni.tiku.similar.KnowledgeGraspRate;
import com.dongni.tiku.similar.Range;
import com.dongni.tiku.similar.StudentLevel;
import com.dongni.tiku.similar.enumeration.Difficulty;
import com.dongni.tiku.similar.enumeration.EssentialQuestionFromType;
import com.dongni.tiku.similar.enumeration.SimilarQuestionDifficultyType;
import com.dongni.tiku.similar.enumeration.SourceQuestionDisplayType;
import com.dongni.tiku.wrong.book.enumerate.WrongQuestionLevelEnum;
import com.dongni.tiku.wrong.book.enumerate.WrongQuestionTypeEnum;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Stream;

import static com.dongni.tiku.similar.enumeration.EssentialQuestionFromType.CLASS_COMMON_WRONG_QUESTION;
import static com.dongni.tiku.similar.enumeration.EssentialQuestionFromType.SELECTED_QUESTION;
import static com.dongni.tiku.similar.enumeration.SimilarQuestionDifficultyType.*;
import static com.dongni.tiku.wrong.book.enumerate.WrongQuestionLevelEnum.*;
import static com.dongni.tiku.wrong.book.enumerate.WrongQuestionTypeEnum.*;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toSet;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/9/13 周五 下午 05:05
 * @Version 1.0.0
 */
public class Similar4Test {

    @Test
    public void test() {
        boolean debug = true;

        // ---------------------------------------------------------- 原题信息
        Question4 sourceQuestion201 = Question4.ofSource("201", Difficulty.DIFFICULT, 0.30, BigDecimal.valueOf(5.0), 0.38, 0.41, 0.20, BigDecimal.valueOf(4.0), 1);
        Question4 sourceQuestion202 = Question4.ofSource("202", Difficulty.DIFFICULT, 0.30, BigDecimal.valueOf(5.0), 0.38, 0.20, 0.40, BigDecimal.valueOf(4.5), 1);
        List<Question4> sourceList = Stream.of(
                Question4.ofSource("001", Difficulty.EASY, 0.82, BigDecimal.valueOf(5.0), 0.93, 0.73, 0.68, BigDecimal.valueOf(4.0), 2),
                Question4.ofSource("002", Difficulty.EASY, 0.82, BigDecimal.valueOf(5.0), 0.93, 0.83, 0.70, BigDecimal.valueOf(4.5), 2),
                Question4.ofSource("101", Difficulty.MIDDLE, 0.62, BigDecimal.valueOf(5.0), 0.80, 0.66, 0.83, BigDecimal.valueOf(4.0), 3),
                Question4.ofSource("102", Difficulty.MIDDLE, 0.62, BigDecimal.valueOf(5.0), 0.80, 0.68, 0.76, BigDecimal.valueOf(5.0), 3),
                sourceQuestion201,
                sourceQuestion202,
                Question4.ofSource("301", Difficulty.EASY, 0.89, BigDecimal.valueOf(5.0), 0.88, 0.87, 0.53, BigDecimal.valueOf(5.0), 1),
                Question4.ofSource("302", Difficulty.EASY, 0.89, BigDecimal.valueOf(5.0), 0.88, 0.87, 0.59, BigDecimal.valueOf(5.0), 1)
        ).collect(toList());
        // 所有的原题 因为原题可能没有类题关系 而它又是错的
        Set<Question4> sourceQuestionSet = new HashSet<>(sourceList);

        // ---------------------------------------------------------- 老师审题  剔除备选题 自己增加备选题 设置此题不练
        // 此题不练
        Set<String> excludeSourceQuestionIdSet = Stream.of(
                        "001",
                        "302",
                        (String) null
                )
                .filter(Objects::nonNull)
                .collect(toSet());
        ;
        // 原题类体关系
        List<SourceCandidate4> SourceCandidate4List = new ArrayList<>();
        for (Question4 source : sourceList) {
            for (Difficulty difficulty : Difficulty.values()) {
                for (int i = 0; i < 1; i++) {
                    String questionId = source.getQuestionId() + "_" + (difficulty.getValue() * 100 + i);
                    int random = ThreadLocalRandom.current().nextInt();
                    boolean singular = random % 2 == 1;
                    Question4 candidate = Question4.ofCandidate(questionId, difficulty, null, singular ? CONSOLIDATE : EXPAND);
                    SourceCandidate4List.add(new SourceCandidate4(source, candidate));
                }
            }
        }
//        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion202,
//                Question4.ofCandidate("201_301", Difficulty.DIFFICULT, CollUtil.newHashSet("A", "B"), CONSOLIDATE)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion202,
                Question4.ofCandidate("201_302", Difficulty.DIFFICULT, CollUtil.newHashSet("A"), CONSOLIDATE)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion202,
                Question4.ofCandidate("201_303", Difficulty.DIFFICULT, CollUtil.newHashSet("C"), CONSOLIDATE)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion202,
                Question4.ofCandidate("201_304", Difficulty.DIFFICULT, CollUtil.newHashSet("D"), EXPAND)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion201,
                Question4.ofCandidate("202_301", Difficulty.DIFFICULT, CollUtil.newHashSet("A"), EXPAND)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion201,
                Question4.ofCandidate("202_302", Difficulty.DIFFICULT, CollUtil.newHashSet("A", "B", "C", "D"), EXPAND)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion201,
                Question4.ofCandidate("202_303", Difficulty.DIFFICULT, CollUtil.newHashSet(), EXPAND)));
        SourceCandidate4List.add(new SourceCandidate4(sourceQuestion201,
                Question4.ofCandidate("202_304", Difficulty.DIFFICULT, null, EXPAND)));
//        SourceCandidate4List.forEach(System.out::println);
//        System.out.println();

        // ---------------------------------------------------------- 学生分层
//        StudentLevel studentLevel = new StudentLevel("A", 0.0, "UP");
//        StudentLevel studentLevel = new StudentLevel("B", 0.2, "UP");
        StudentLevel studentLevel = new StudentLevel("C", 0.4, "DOWN");
//        StudentLevel studentLevel = new StudentLevel("D", 0.6, "DOWN");
//        StudentLevel studentLevel = new StudentLevel("E", 0.8, "DOWN");

        // ---------------------------------------------------------- 错题本题量数量
        // 错题本最小题量 控制个性化成长手册的推题数量 学霸除了错题还有按照班级共性错题由难到易进行推题加强练
        int wrongBankQuestionMin = 20;
        // 错题本最大题量 控制个性化成长手册的推题数量 学渣会根据班级共性错题由易到难进行推题
        int wrongBankQuestionMax = 25;
        // 开启按试题得分率区间筛选原错题后，若某题的个人/班级/年级得分率不在定义范围内，此原题及其类题不展示 获取试题的得分率 可以为null
//        Function<Question4, Double> sourceQuestionRateFunction = null;
        Function<Question4, Double> sourceQuestionRateFunction = Question4::getGradeScoringRate;
        // 开启按试题得分率区间筛选原错题后，若某题的个人/班级/年级得分率不在定义范围内，此原题及其类题不展示 得分率区间      可以为null
        Range<Double> sourceQuestionRateRange = Range.parse("[0, 0.80]", MapUtil::getDoubleNullable);

        // 学生错题得分率定义区间
        Range<Double> wrongScoreRateRange = Range.parse("[0, 0.6]", MapUtil::getDoubleNullable);

        // ---------------------------------------------------------- 推题数设置
        // 可推巩固/拓展
        Map<WrongQuestionLevelEnum, List<SimilarQuestionDifficultyType>> objectQuestion = MapUtil.of(
                SIMPLE_QUESTION, Stream.of(CONSOLIDATE).collect(toList()),
                INTERMEDIATE_QUESTION, Stream.of(CONSOLIDATE, EXPAND).collect(toList()),
                DIFFICULT_QUESTION, Stream.of(EXPAND, CONSOLIDATE).collect(toList()));
        Map<WrongQuestionLevelEnum, List<SimilarQuestionDifficultyType>> blankQuestion = MapUtil.of(
                SIMPLE_QUESTION, Collections.emptyList(),
                INTERMEDIATE_QUESTION, Stream.of(CONSOLIDATE, EXPAND).collect(toList()),
                DIFFICULT_QUESTION, Stream.of(EXPAND).collect(toList()));
        Map<WrongQuestionLevelEnum, List<SimilarQuestionDifficultyType>> solutionQuestion = MapUtil.of(
                SIMPLE_QUESTION, null,
                INTERMEDIATE_QUESTION, Stream.of(CONSOLIDATE).collect(toList()),
                DIFFICULT_QUESTION, Stream.of(EXPAND).collect(toList()));
        Map<WrongQuestionTypeEnum, Map<WrongQuestionLevelEnum, List<SimilarQuestionDifficultyType>>> sourceAllowDifficultyType = MapUtil.of(
            OBJECTIVE_QUESTION, objectQuestion,
            BLANK_QUESTION, blankQuestion,
            SOLUTION_QUESTION, solutionQuestion
        );

        // 数量设置
        Map<String, Integer> questionId2SimilarNum = MapUtil.of(
                "201", 2,
                "202", 3,
                "001", 3,
                "002", 4,
                "101", 2,
                "102", 0,
                "301", 1,
                "302", 3
        );

        // ---------------------------------------------------------- 其他非页面配置值
        // 不能选为推题的题目idSet 比如说最近两次考试的试题不能再被选为推题等情况
        Set<String> excludeCandidateQuestionIdSet = Stream.of(
                        "123456789",
                        (String) null
                )
                .filter(Objects::nonNull)
                .collect(toSet());
        // 知识点掌握程度
        List<KnowledgeGraspRate> knowledgeGraspRateList = new ArrayList<>();

        // ---------------------------------------------------------- 必练题
        List<EssentialQuestion4> essentialQuestion4List = Stream.of(
                        null,
                        EssentialQuestion4.of(Question4.ofEssential("必练1"), "102"),
                        EssentialQuestion4.of(Question4.ofEssential("拔高1"), "拔高1"),
                        EssentialQuestion4.of(Question4.ofEssential("必练3"), "201"),
                        EssentialQuestion4.of(Question4.ofEssential("必练4"), "201"),
                        null
                )
                .filter(Objects::nonNull)
                .collect(toList());

        // 必练典例配置
        EssentialConfig essentialConfig = new EssentialConfig(true, 8,
                Arrays.asList(
//                        SELECTED_QUESTION,
                        CLASS_COMMON_WRONG_QUESTION
                ),
                Range.between(0.2d, true, 0.9, true));

        // ---------------------------------------------------------- 拔高题

        List<Question4> advancedQuestion4List = Stream.of(
                        null,
                        Question4.ofAdvanced("拔高1", 0.3d),
                        Question4.ofAdvanced("拔高2", 0.2d),
                        Question4.ofAdvanced("拔高3", 1d),
                        null
                )
                .filter(Objects::nonNull)
                .collect(toList());

        Similar4Builder similar4Builder = new Similar4Builder();
        Similar4 similar4 = similar4Builder
                .setDebug(debug)
                .setSourceQuestionSet(sourceQuestionSet)
                .setSourceCandidate4List(SourceCandidate4List)
                .setExcludeSourceQuestionIdSet(excludeSourceQuestionIdSet)
                .setEssentialQuestion4List(essentialQuestion4List)
                .setAdvancedQuestion4List(advancedQuestion4List)
                .setStudentLevel(studentLevel)
                .setWrongBankQuestionMin(wrongBankQuestionMin)
                .setWrongBankQuestionMax(wrongBankQuestionMax)
                .setSourceQuestionRateFunction(sourceQuestionRateFunction)
                .setSourceQuestionRateRange(sourceQuestionRateRange)
                .setSourceAllowDifficultyType(sourceAllowDifficultyType)
                .setWrongScoreRateRange(wrongScoreRateRange)
                .setQuestionId2SimilarNum(questionId2SimilarNum)
                .setEssentialConfig(essentialConfig)
                .setComputeAdvancedQuestion(true)
                .setComputeConsolidateQuestion(true)
                .setExcludeSourceQuestionIdSet(excludeSourceQuestionIdSet)
                .setSourceQuestionDisplayType(SourceQuestionDisplayType.DISPLAY_BY_ALL)
                .build();
        similar4.match();

        // part 1.1 必练典例 - 老师挑选的必练题
        Set<Question4> essentialResult = similar4.getEssentialResult();
        System.out.println("---------------- part 1.1 必练典例 - 老师挑选的必练题");
        for (Question4 Question4 : essentialResult) {
            System.out.println(Question4.getQuestionId());
        }
        System.out.println();

        // part 1.2 必练典例 - 班级共性错题的巩固类题
        Map<Question4, Question4> consolidateEssentialResult = similar4.getConsolidateEssentialResult();
        System.out.println("---------------- part 1.2 必练典例 - 班级共性错题的巩固类题");
        for (Map.Entry<Question4, Question4> entry : consolidateEssentialResult.entrySet()) {
            Question4 essential = entry.getKey();
            Question4 from = entry.getValue();
            System.out.println(essential + " ==来源于==> " + from);
        }
        System.out.println();

        // part 2 推荐框架返回的类题列表
        Map<Question4, List<Question4>> similarResult = similar4.getSimilarResult();
        System.out.println("---------------- part 2 推题");
        for (Map.Entry<Question4, List<Question4>> entry : similarResult.entrySet()) {
            System.out.println("------ 原题 " + entry.getKey());
            for (Question4 similar : entry.getValue()) {
                System.out.println(similar);
            }
        }
        System.out.println();

        // part 3 拔高练习
        Set<Question4> advancedResult = similar4.getAdvancedResult();
        System.out.println("---------------- part 3 拔高练习");
        for (Question4 Question4 : advancedResult) {
            System.out.println(Question4);
        }

        // part 4 加强练习
        Map<Question4, List<Question4>> consolidateResult = similar4.getConsolidateResult();
        System.out.println("---------------- part 4 加强练习");
        for (Map.Entry<Question4, List<Question4>> entry : consolidateResult.entrySet()) {
            System.out.println("------ 原题 " + entry.getKey());
            for (Question4 similar : entry.getValue()) {
                System.out.println(similar);
            }
        }
    }
}
