package com.dongni.common.pdf;

import com.dongni.analysis.view.monitor.service.ExamStudentStatService;
import com.dongni.common.report.pdf.service.SubjectWordService;
import com.dongni.common.utils.DictUtil;
import com.dongni.commons.utils.DateUtil;
import com.dongni.exam.bean.ExamRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020/04/17 13:18
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SubjectWordServiceTest {

    @Autowired
    SubjectWordService subjectWordService;

    @Autowired
    ExamStudentStatService examStudentStatService;

    @Autowired
    ExamRepository examRepository;

    @Test
    public void toSingleWord() {

        HashMap<String, Object> hashMap = new HashMap<>();

        hashMap.put("userId", "5661");  //5661
        hashMap.put("userType", "3");  // 3
        hashMap.put("userName", "老师1");
        hashMap.put("clientType", "1");
        hashMap.put("search", "");
        hashMap.put("pageNo", "");
        hashMap.put("sortKey", "totalScore");
        hashMap.put("sortType", "desc");
        hashMap.put("pageSize", "");
        hashMap.put("examId", "1569923");
        hashMap.put("schoolId", "794");
        hashMap.put("statId", "0");
        hashMap.put("paperId", "847232784");
        hashMap.put("classId", "12261");
        hashMap.put("courseId", "2"); //
        hashMap.put("teacherId", "19263");
        hashMap.put("compareExamId", "1569755");
        hashMap.put("compareStatId", "0");
        hashMap.put("sheet", "1,2,3");
        hashMap.put("examName", "多科目");
//        System.out.println(subjectWordService.toWordAll(hashMap));
        long l = System.currentTimeMillis();
        System.out.println(subjectWordService.wordAndExcelToZip(hashMap));
        System.out.println(System.currentTimeMillis() - l);

    }


    @Test
    public void toTotalWord() {
        HashMap<String, Object> hashMap = new HashMap<>();


        hashMap.put("userId", "820");
        hashMap.put("userType", "3");
        hashMap.put("userName", "靓仔22");
        hashMap.put("clientType", "1");
        hashMap.put("examId", "1566017");
        hashMap.put("schoolId", "516");
        hashMap.put("sortKey", "totalScore");
        hashMap.put("sortType", "desc");
        hashMap.put("classId", "");
        hashMap.put("statId", "0");
        hashMap.put("search", "");
//        hashMap.put("pageNo", "1");
//        hashMap.put("pageSize", "50");
        hashMap.put("isGradeReverse", false);
        hashMap.put("compareExamId", "");
        hashMap.put("compareStatId", "0");
        hashMap.put("teacherId", "15380");
        hashMap.put("studentId", "373859");
        hashMap.put("currentIndex", "0");
//        hashMap.put("courseId", "");


        hashMap.put("examName", "广东又开始高考");
        subjectWordService.toWordAll(hashMap);

    }

    @Test
    public void test() {
        int column = 14;
        // 默认一行
        int row = 1;
        int keySize = 4 + 21;
        int mo = (keySize - 4) / (column - 4);
        int yu = (keySize - 4) % (column - 4);
        if (mo > 0 && yu > 0) {
            row = mo + 1;
        } else if (yu == 0 && mo > 0) {
            row = mo;
        }

        System.out.println("row:" + row);
    }


    @Test
    public void testWordMapperInsert() {
        //  : 入库 t_word_download_id
        Map<String, Object> wordParams = new HashMap<>();
        // 是否需要转换PDF
        wordParams.put("id", null);
        wordParams.put("wordReportStatus", 0);
        wordParams.put("areaId", 1);
        wordParams.put("examId", 1);
        wordParams.put("wordReportName", "成绩单");
        wordParams.put("wordReportJson", "成绩单");
        wordParams.put("userId", 1);
        wordParams.put("userName", "test");
        wordParams.put("currentTime", DateUtil.getCurrentDateTime());
        wordParams.put("address", "test");
        int insert = examRepository.insert("WordReportExportMapper.insertReturnId", wordParams);
        System.out.println(wordParams.get("id"));
    }


}
