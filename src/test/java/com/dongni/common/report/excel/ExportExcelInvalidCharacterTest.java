package com.dongni.common.report.excel;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/4/12 周五 下午 04:48
 * @Version 1.0.0
 */
public class ExportExcelInvalidCharacterTest {
    public static void main(String[] args) {
        TestExportExcel testExportExcel1 = new TestExportExcel("'/我有<*特殊字符'");
        TestExportExcel testExportExcel2 = new TestExportExcel("'/DJ");
        TestExportExcel testExportExcel3 = new TestExportExcel("History");
        testExportExcel1.addSheet(testExportExcel2);
        testExportExcel1.addSheet(testExportExcel3);
        testExportExcel1.exportToLocalPath("/home/<USER>/test/");
    }
}
