package com.dongni.common.utils;

import org.junit.Assert;
import org.junit.Test;

/**
 *
 * <AUTHOR>
 * @date 2023/08/14
 */
public class DisplayUtilTest {
    
    @Test
    public void testStorageSize() {
        Assert.assertEquals("   0.000  B", DisplayUtil.storageSize(0));
        Assert.assertEquals("   1.000  B", DisplayUtil.storageSize(1));
        Assert.assertEquals("  10.000  B", DisplayUtil.storageSize(10));
        Assert.assertEquals(" 100.000  B", DisplayUtil.storageSize(100));
        Assert.assertEquals("1023.000  B", DisplayUtil.storageSize(1024L - 1));
        Assert.assertEquals("   1.000 KB", DisplayUtil.storageSize(1024L));
        Assert.assertEquals("   1.001 KB", DisplayUtil.storageSize(1024L + 1));
        Assert.assertEquals("1023.999 KB", DisplayUtil.storageSize(1024L * 1024 - 1));
        Assert.assertEquals("   1.000 MB", DisplayUtil.storageSize(1024L * 1024));
        Assert.assertEquals("   1.000 MB", DisplayUtil.storageSize(1024L * 1024 + 1));
        Assert.assertEquals("   1.001 MB", DisplayUtil.storageSize(1024L * 1024 + 1024));
        Assert.assertEquals("1023.999 MB", DisplayUtil.storageSize(1024L * 1024 * 1024 - 1024));
        Assert.assertEquals("1024.000 MB", DisplayUtil.storageSize(1024L * 1024 * 1024 - 1));
        Assert.assertEquals("   1.000 GB", DisplayUtil.storageSize(1024L * 1024 * 1024));
        Assert.assertEquals("   1.000 GB", DisplayUtil.storageSize(1024L * 1024 * 1024 + 1));
        Assert.assertEquals("   1.001 GB", DisplayUtil.storageSize(1024L * 1024 * 1024 + 1024 * 1024));
        Assert.assertEquals("1024.000 GB", DisplayUtil.storageSize(1024L * 1024 * 1024 * 1024 - 1));
        Assert.assertEquals("   1.000 TB", DisplayUtil.storageSize(1024L * 1024 * 1024 * 1024));
        Assert.assertEquals("   1.000 TB", DisplayUtil.storageSize(1024L * 1024 * 1024 * 1024 + 1));
    }
    
    @Test
    public void testPercent() {
        Assert.assertEquals("  0.00 %", DisplayUtil.percent(0.000, 100.00));
        Assert.assertEquals("  0.00 %", DisplayUtil.percent(0.001, 100.00));
        Assert.assertEquals("  0.01 %", DisplayUtil.percent(0.01, 100.00));
        Assert.assertEquals("  0.10 %", DisplayUtil.percent(0.1, 100.00));
        Assert.assertEquals("  1.00 %", DisplayUtil.percent(1.0, 100.00));
        Assert.assertEquals(" 10.00 %", DisplayUtil.percent(10.0, 100.00));
        Assert.assertEquals("100.00 %", DisplayUtil.percent(100.0, 100.00));
        Assert.assertEquals("  4.40 %", DisplayUtil.percent(4.4, 100.00));
        Assert.assertEquals("  4.44 %", DisplayUtil.percent(4.44, 100.00));
        Assert.assertEquals("  4.44 %", DisplayUtil.percent(4.444, 100.00));
        Assert.assertEquals("  4.44 %", DisplayUtil.percent(4.4444, 100.00));
        Assert.assertEquals("  5.50 %", DisplayUtil.percent(5.5, 100.00));
        Assert.assertEquals("  5.55 %", DisplayUtil.percent(5.55, 100.00));
        Assert.assertEquals("  5.56 %", DisplayUtil.percent(5.555, 100.00));
        Assert.assertEquals("  5.56 %", DisplayUtil.percent(5.5555, 100.00));
        Assert.assertEquals("  -    %", DisplayUtil.percent(100, 0));
        Assert.assertEquals("  -    %", DisplayUtil.percent(0, 0));
        Assert.assertEquals("10000.00 %", DisplayUtil.percent(10000, 100));
    }
    
    @Test
    public void testPaddingSpace() {
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 1));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 2));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 3));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 4));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 5));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 6));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 7));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 8));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 9));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceLeftAlign("1234567890", 10));
        Assert.assertEquals("1234567890 ",      DisplayUtil.paddingSpaceLeftAlign("1234567890", 11));
        Assert.assertEquals("1234567890  ",     DisplayUtil.paddingSpaceLeftAlign("1234567890", 12));
        Assert.assertEquals("1234567890   ",    DisplayUtil.paddingSpaceLeftAlign("1234567890", 13));
        Assert.assertEquals("1234567890    ",   DisplayUtil.paddingSpaceLeftAlign("1234567890", 14));
        Assert.assertEquals("1234567890     ",  DisplayUtil.paddingSpaceLeftAlign("1234567890", 15));
        Assert.assertEquals("1234567890      ", DisplayUtil.paddingSpaceLeftAlign("1234567890", 16));
        Assert.assertEquals("                ", DisplayUtil.paddingSpaceLeftAlign("",           16));
        Assert.assertEquals("null            ", DisplayUtil.paddingSpaceLeftAlign(null,         16));
        
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 1));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 2));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 3));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 4));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 5));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 6));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 7));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 8));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 9));
        Assert.assertEquals("1234567890",       DisplayUtil.paddingSpaceRightAlign("1234567890", 10));
        Assert.assertEquals(" 1234567890",      DisplayUtil.paddingSpaceRightAlign("1234567890", 11));
        Assert.assertEquals("  1234567890",     DisplayUtil.paddingSpaceRightAlign("1234567890", 12));
        Assert.assertEquals("   1234567890",    DisplayUtil.paddingSpaceRightAlign("1234567890", 13));
        Assert.assertEquals("    1234567890",   DisplayUtil.paddingSpaceRightAlign("1234567890", 14));
        Assert.assertEquals("     1234567890",  DisplayUtil.paddingSpaceRightAlign("1234567890", 15));
        Assert.assertEquals("      1234567890", DisplayUtil.paddingSpaceRightAlign("1234567890", 16));
        Assert.assertEquals("                ", DisplayUtil.paddingSpaceRightAlign("",           16));
        Assert.assertEquals("            null", DisplayUtil.paddingSpaceRightAlign(null,         16));
    }
}
