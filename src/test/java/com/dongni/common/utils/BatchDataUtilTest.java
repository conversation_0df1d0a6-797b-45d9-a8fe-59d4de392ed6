package com.dongni.common.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.Assert;
import org.junit.Test;

public class BatchDataUtilTest {

	@Test
	public void execute() {
		for (Integer number : new int[]{0, 10, 12, 100, 15, 325, 524}) {
			List<Integer> expected = new ArrayList<>();
			for (int i = 0; i < number; i++) {
				expected.add(i);
			}
			List<Integer> result = new ArrayList<>();
			BatchDataUtil.execute(expected, (x) -> result.addAll(x), 15);
			Assert.assertEquals(expected, result);
		}
	}

	@Test
	public void submit() {
		for (Integer number : new int[]{0, 10, 12, 100, 15, 325, 524}) {
			List<Integer> objects = new ArrayList<>();
			for (int i = 0; i < number; i++) {
				objects.add(i);
			}
			List<Integer> expected = objects.stream().map(x -> x * 2).collect(Collectors.toList());
			List<Integer> result = BatchDataUtil.submit(objects, (x) -> x.stream().map(n->n*2).collect(Collectors.toList()), 15);
			Assert.assertEquals(expected,result);
		}

	}
}