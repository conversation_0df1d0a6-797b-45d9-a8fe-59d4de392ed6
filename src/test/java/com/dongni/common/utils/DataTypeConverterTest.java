package com.dongni.common.utils;

import com.dongni.commons.utils.MD5Util;
import org.junit.Assert;
import org.junit.Test;

import static com.dongni.common.utils.DataTypeConverter.*;

public class DataTypeConverterTest {

	@Test
	public void test2Long() {
		String appid = "xmcds40a6c04b3019925593716e115c848fd7";
		String timestamp = String.valueOf(System.currentTimeMillis());
		String appkey = "133bd0303e3a0f2a297b3751d10c058c";

		System.out.println(timestamp);
		System.out.println(MD5Util.md5(appid + timestamp + appkey));
	}

	@Test
	public void test2Integer() {
		Assert.assertEquals(new Integer(2345),  toInt(2345L));
		Assert.assertEquals(new Integer(2345),  toInt(2345));
		Assert.assertEquals(new Integer(2345),  toInt("2345"));
		Assert.assertNull(toInt(null));
		Assert.assertNull(toInt(""));

	}

	@Test
	public void test2Double() {
		Assert.assertEquals(new Double(2345.123),  toDouble(2345.123F));
		Assert.assertEquals(new Double(2345.123),  toDouble(2345.123D));
		Assert.assertEquals(new Double(2345.123),  toDouble("2345.123"));
		Assert.assertNull(toDouble(null));
		Assert.assertNull(toDouble(""));
	}

}