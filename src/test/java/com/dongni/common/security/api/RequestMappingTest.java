package com.dongni.common.security.api;

import com.dongni.basedata.bean.BaseDataMongodb;
import com.dongni.basedata.enumeration.UserCategory;
import com.dongni.basedata.enumeration.UserTypeEnum;
import com.dongni.common.mongo.IManager;
import com.dongni.common.utils.ComparatorEx;
import com.dongni.common.utils.MongoUtil;
import com.dongni.commons.annotation.AuthAnnotation;
import com.dongni.commons.annotation.DataScope;
import com.dongni.commons.annotation.DataScopeBy;
import com.dongni.commons.annotation.DongniRequest;
import com.dongni.commons.annotation.LoginAnnotation;
import com.dongni.commons.annotation.NotInterceptorAnnotation;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Updates.combine;
import static com.mongodb.client.model.Updates.set;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toSet;

/**
 *
 * <AUTHOR>
 * @date 2024/11/06
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RequestMappingTest {
    
    @Autowired()
    private RequestMappingHandlerMapping requestMappingHandlerMapping;
    
    @Autowired
    private BaseDataMongodb baseDataMongodb;
    
    @Test
    public void test() {
        String collectionName = "developRequestMapping";
        IManager requestMappingManager = new IManager(baseDataMongodb, collectionName, "收集接口信息") {
        };
        
        
        Map<UserCategory, List<UserTypeEnum>> userCategory2UserTypeEnum = Arrays.stream(UserTypeEnum.values())
                .collect(groupingBy(UserTypeEnum::getUserCategory));
        
        int requestMappingCount = 0;
        int dongniRequestCount = 0;
        String currentDateTime = DateUtil.getCurrentDateTime();
        List<Document> newRequestMappingList = new ArrayList<>();
        Map<RequestMappingInfo, HandlerMethod> handlerMethods = requestMappingHandlerMapping.getHandlerMethods();
        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : handlerMethods.entrySet()) {
            requestMappingCount++;
            RequestMappingInfo requestMappingInfo = entry.getKey();
            HandlerMethod handlerMethod = entry.getValue();
            Class<?> beanType = handlerMethod.getBeanType();
            String className = beanType.getName();
            String classSimpleName = beanType.getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            
            boolean responseBody = handlerMethod.getMethodAnnotation(ResponseBody.class) != null
                    || beanType.getAnnotation(RestController.class) != null
                    || beanType.getAnnotation(ResponseBody.class) != null;
            boolean requireLogin = LoginAnnotation.isRequireLogin(handlerMethod, true);
            boolean interceptor = NotInterceptorAnnotation.isHandlerInterceptor(handlerMethod, true);
            boolean dongniAuth = AuthAnnotation.isDongniAuth(handlerMethod, false);
            boolean deprecated = handlerMethod.getMethodAnnotation(Deprecated.class) != null;
            
            Document dataScopeByDoc = null;
            DataScopeBy dataScopeBy = handlerMethod.getMethodAnnotation(DataScopeBy.class);
            List<String> dataScoreClassSimpleNameList = new ArrayList<>();
            if (dataScopeBy != null) {
                dataScopeByDoc = new Document();
                Class<? extends DataScope>[] dataScopes = dataScopeBy.value();
                List<Class<? extends DataScope>> dataScopeList = Arrays.asList(dataScopes);
                List<String> dataScoreClassNameList = dataScopeList.stream()
                        .map(item -> Optional.ofNullable(item).map(Class::getName).orElse(null))
                        .collect(Collectors.toList());
                dataScopeByDoc.put("value", dataScoreClassNameList);
                dataScoreClassSimpleNameList = dataScopeList.stream()
                        .map(item -> Optional.ofNullable(item).map(Class::getSimpleName).orElse(""))
                        .sorted(Comparator.naturalOrder())
                        .collect(Collectors.toList());
            }
            
            DongniRequest dongniRequest = handlerMethod.getMethodAnnotation(DongniRequest.class);
            String operationName = "";
            List<String> remarkList = new ArrayList<>();
            List<UserTypeEnum> userTypeList = new ArrayList<>();
            Document dongniRequestDoc = null;
            if (dongniRequest != null) {
                dongniRequestCount++;
                
                dongniRequestDoc = new Document();
                operationName = dongniRequest.operationName();
                remarkList = Arrays.asList(dongniRequest.remark());
                List<UserCategory> userCategoryList = Arrays.asList(dongniRequest.userCategory());
                List<UserTypeEnum> UserTypeEnumList = Arrays.asList(dongniRequest.userType());
                List<String> privilegeIdList = Arrays.asList(dongniRequest.privilegeId());
                
                dongniRequestDoc.put("operationName", operationName);
                dongniRequestDoc.put("remark", remarkList);
                dongniRequestDoc.put("privilegeIds", privilegeIdList);
                dongniRequestDoc.put("userCategories", toDocumentUserCategory(userCategoryList));
                dongniRequestDoc.put("userTypeEnums", toDocumentUserTypeEnum(UserTypeEnumList));
                
                Set<UserTypeEnum> userTypeSet = new HashSet<>(UserTypeEnumList);
                for (UserCategory userCategory : userCategoryList) {
                    List<UserTypeEnum> userCategoryUserTypeEnums = userCategory2UserTypeEnum.get(userCategory);
                    if (CollectionUtils.isNotEmpty(userCategoryUserTypeEnums)) {
                        userTypeSet.addAll(userCategoryUserTypeEnums);
                    }
                }
                userTypeList = userTypeSet.stream()
                        .sorted(ComparatorEx.asc(UserTypeEnum::getValue))
                        .collect(Collectors.toList());
                dongniRequestDoc.put("__userTypeEnums", toDocumentUserTypeEnum(userTypeList));
            }
            
            Document requestMappingDoc = new Document();
            requestMappingDoc.append("requestMapping", requestMappingInfo.toString());
            requestMappingDoc.append("handlerMethod", handlerMethod.toString());
            requestMappingDoc.append("className", className);
            requestMappingDoc.append("classSimpleName", classSimpleName);
            requestMappingDoc.append("method", methodName);
            requestMappingDoc.append("deprecated", deprecated);
            requestMappingDoc.append("responseBody", responseBody);
            requestMappingDoc.append("requireLogin", requireLogin);
            requestMappingDoc.append("interceptor", interceptor);
            requestMappingDoc.append("dongniAuth", dongniAuth);
            requestMappingDoc.append("operationName", operationName);
            requestMappingDoc.append("remarkStr", String.join(";", remarkList));
            requestMappingDoc.append("userTypeList", toDocumentUserTypeEnum(userTypeList));
            requestMappingDoc.append("userTypeStr", userTypeList.stream()
                    .map(item -> item.getValue() + item.getLabel())
                    .collect(Collectors.joining(";"))
            );
            requestMappingDoc.append("dataScopeStr", String.join(";", dataScoreClassSimpleNameList));
            requestMappingDoc.append("dongniRequest", dongniRequestDoc);
            requestMappingDoc.append("dataScopeBy", dataScopeByDoc);
            requestMappingDoc.append("deleted", false);
            requestMappingDoc.append("currentDateTime", currentDateTime);
            newRequestMappingList.add(requestMappingDoc);
        }
        
        List<Document> oldRequestMappingList = requestMappingManager.getList();
        Map<String, List<Document>> requestMapping2oldList = oldRequestMappingList.stream()
                .collect(groupingBy(item -> MapUtil.getString(item, "requestMapping")));
        requestMapping2oldList.forEach((requestMapping, oldList) -> {
            if (oldList.size() > 1) {
                oldList.forEach(item -> System.out.println(JSONUtil.toJson(item)));
                throw new CommonException(ResponseStatusEnum.FAILURE, "旧的api冲突");
            }
        });
        
        Set<ObjectId> oldRequestMappingIdSet = oldRequestMappingList.stream()
                .map(MongoUtil::getMongoId)
                .collect(toSet());
        
        for (Document newRequestMapping : newRequestMappingList) {
            String requestMapping = MapUtil.getString(newRequestMapping, "requestMapping");
            Document oldRequestMapping = Optional.of(requestMapping2oldList)
                    .map(item -> item.get(requestMapping))
                    .map(item -> item.get(0))
                    .orElse(null);
            if (MapUtils.isNotEmpty(oldRequestMapping)) {
                ObjectId mongoId = MongoUtil.getMongoId(oldRequestMapping);
                oldRequestMapping.forEach((key, value) -> {
                    if ("_id".equals(key)) {
                        return;
                    }
                    newRequestMapping.putIfAbsent(key, value);
                });
                newRequestMapping.putIfAbsent("mongoRemark", "");
                requestMappingManager.replace(mongoId, newRequestMapping);
                oldRequestMappingIdSet.remove(mongoId);
            } else {
                newRequestMapping.putIfAbsent("mongoRemark", "");
                requestMappingManager.insertOne(newRequestMapping);
            }
        }
        if (CollectionUtils.isNotEmpty(oldRequestMappingIdSet)) {
            requestMappingManager.updateMany(in("_id", oldRequestMappingIdSet), combine(
                    set("deleted", true),
                    set("currentDateTime", currentDateTime)
                    
            ));
        }
    }
    
    private List<Document> toDocumentUserCategory(List<UserCategory> userCategoryList) {
        return userCategoryList.stream()
                .map(item -> new Document()
                        .append("code", item.getCode())
                        .append("name", item.getName())
                )
                .collect(Collectors.toList());
    }
    
    private List<Document> toDocumentUserTypeEnum(List<UserTypeEnum> userTypeEnumList) {
        return userTypeEnumList.stream()
                .map(item -> new Document()
                        .append("value", item.getValue())
                        .append("key", item.getKey())
                        .append("label", item.getLabel())
                        .append("userCategory", item.getUserCategory().getCode() + item.getUserCategory().getName())
                )
                .collect(Collectors.toList());
    }
}
