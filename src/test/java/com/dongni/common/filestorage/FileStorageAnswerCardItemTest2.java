package com.dongni.common.filestorage;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.common.utils.FileStorageCommonUtil;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.constant.FileStorageConstants;
import com.dongni.commons.utils.jdk.java.lang.LongUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 切割块图片:
 * 11. upload/answerCard/yyyy/m/d/examId/examUploaderId/randomValue_fileName/xxxx.png
 * 12. upload/answerCard/yyyy/m/d/examId/examUploaderId/renew/recognitionId/randomValue_fileName/xxxx.png
 * 13. upload/answerCard/yyyy/m/d/examId/examUploaderId/readAppend_randomValue_randomValue_fileName/xxxx.png
 * 21. upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/randomValue_fileName/xxxx.png
 * 22. upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/renew/recognitionId/randomValue_fileName/xxxx.png
 * 23. upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/readAppend_randomValue_randomValue_fileName/xxxx.png
 *
 * 11. upload/answerCard/2021/2/9/144726/127136/723035572_DN0209000001/3001v0v0.png
 * 12. upload/answerCard/2021/2/20/144962/127251/81850828_DN0220000001/renew/5136/2-1_7_split-0.png
 * 13. upload/answerCard/2018/12/24/41340/27789/readAppend_1545641666806_-1469674045_DN1224000001/11.png
 * 21. upload/answerCard/2018/12/24/41236/27738/6610,6611,6607,6608,6609,6612/261660695_DN1224000001/1.png
 * 22. upload/answerCard/2022/6/18/1370515/1396337/60510,..,60520/renew/1148096/1723812398_DN0618000062/studentNumber.png
 * 23. upload/answerCard/2018/12/24/41422/27853/1871/readAppend_1545614958142_265586569_DN1224000001/31.png
 *
 * dongni-product
 * 2022-12-13
 * 文件存储总量: 58.2 TB
 * t_exam_item切割块图片数量: 6.5亿     (654796503)
 * t_exam_item切割块图片占用: 24.638 TB (27089712043897)
 * examUploaderId下文件占用: 52.274 TB
 *
 * 切割块占存储总量: 42.333%
 * 切割块占examUploaderId: 47.132%
 *
 *
 * 未处理统计的:
 * upload/answerCard/2017/6/5/5389/1496735740092_120026.png                                                                             应该是 yyyy/m/d/examId/classIds
 * upload/answerCard/2017/6/5/5389/311,312,313,314,315,316/1496739828479_1 (1)/11.png                                                   应该是 yyyy/m/d/examId/classIds
 * upload/answerCard/2021/2/9/144726/127136/723035572_DN0209000001.TIF                                                                  答题卡大图
 * upload/answerCard/2018/4/11/17241/14042/636,637...652,653/867624668_2017.4.11高三语文0074.png                                        答题卡大图
 * upload/answerCard/2021/2/9/144838/2030214800/-1293641686_612.png                                                                     答题卡大图
 * upload/answerCard/2019/6/5/53648/41341/correct/-928557476_DN0605000001.png                                                           矫正的
 * upload/answerCard/2019/6/5/53643/41365/8015,8016/correct/443902844_DN0605000003.png                                                  矫正的
 * upload/answerCard/2018/12/24/41320/27776/score/-185181184_第五次理综测试0003_1545749704781.png                                       分数合成图片
 * upload/answerCard/2018/4/11/17241/14042/636,637...652,653/score/431066595_2018.4.11高三语文0001/431066595_2018.4.11高三语文0001.png  分数合成图片
 * upload/answerCard/2022/10/15/1576985/1601279/1995824666_DN1015000034/replace/621.png                                                 异常卷处理替换的图片
 * upload/answerCard/2010/9/7/1201143/1196345/template/1283789398006.png                                                                模板的?
 * upload/answerCard/2022/8/9/1485711/1509212//-582500806_DN0809000001/studentNumber.png                                                奇怪路径
 *
 *
 *
 * <AUTHOR>
 * 2022/12/07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageAnswerCardItemTest2 {
    
    private final static Logger log = LoggerFactory.getLogger(FileStorageAnswerCardItemTest2.class);
    
    private final LongAdder allExamUploaderIdFileSize = new LongAdder();
    private final LongAdder allItemFileSize = new LongAdder();
    private final LongAdder allItemCount = new LongAdder();
    private final AtomicBoolean finished = new AtomicBoolean();
    
    
    private final Map<String, String> type2Path = new ConcurrentHashMap<>();
    private final Map<String, String> type2InvalidPath = new ConcurrentHashMap<>();
    private final Map<String, Long> yyyyMMddDirPath2FileSize = new ConcurrentHashMap<>();
    private final Map<String, Long> yyyyMMddDirPath2FileCount = new ConcurrentHashMap<>();
    
    private final LongAdder yyyyMMddDirPathDone = new LongAdder();
    private long yyyyMMddDirPathCount = 0;
    
    // static {
    //     int availableProcessors = Runtime.getRuntime().availableProcessors();
    //     int forkJoinPoolParallelism = availableProcessors * 3;
    //     String forkJoinPoolParallelismString = String.valueOf(forkJoinPoolParallelism);
    //     System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", forkJoinPoolParallelismString);
    // }
    
    @Test
    public void test() {
        finished.set(false);
        allExamUploaderIdFileSize.reset();
        allItemFileSize.reset();
        allItemCount.reset();
        type2Path.clear();
        type2InvalidPath.clear();
        yyyyMMddDirPath2FileSize.clear();
        yyyyMMddDirPath2FileCount.clear();
        yyyyMMddDirPathDone.reset();
        
        String uploadAnswerCardDirPath = "upload" + FileStorageConstants.PATH_SEPARATOR
                                         + "answerCard" + FileStorageConstants.PATH_SEPARATOR;
    
        Map<String, Integer> dirList2yyyyMMdd = FileStorageCommonUtil.getDirList2yyyyMMdd(uploadAnswerCardDirPath);
        yyyyMMddDirPathCount = dirList2yyyyMMdd.size();
        
        new Thread(() -> {
            while (true) {
                printResult();
                if (finished.get()) {
                    break;
                }
                try {
                    Thread.sleep(1_000);
                } catch (InterruptedException ignore) {
                }
            }
            printResult();
        }).start();
    
        upload_answerCard_yyyy_m_d(dirList2yyyyMMdd.keySet());
    
        log.info("-----------------------");
    
        List<String> dirPathListSorted = dirList2yyyyMMdd.keySet().stream()
                .sorted(Comparator.comparing(dirList2yyyyMMdd::get))
                .collect(Collectors.toList());
        for (String dirPath : dirPathListSorted) {
            Long fileSize = yyyyMMddDirPath2FileSize.get(dirPath);
            Long fileCount = yyyyMMddDirPath2FileCount.get(dirPath);
            log.info("path: {} ; count: {}; size: {} - {}",
                    DisplayUtil.paddingSpaceLeftAlign(dirPath, 60),
                    fileCount,
                    fileSize,
                    DisplayUtil.storageSize(fileSize)
            );
        }
    
        log.info("-----------------------");
        for (Map.Entry<String, String> entry : type2Path.entrySet()) {
            log.info("path: {} ; type: {}", entry.getValue(), entry.getKey());
        }
        log.info("-----------------------");
        for (Map.Entry<String, String> entry : type2InvalidPath.entrySet()) {
            log.info("path: {} ; type: {}", entry.getValue(), entry.getKey());
        }
        finished.set(true);
    }
    
    private void printResult() {
        long yyyyMMddDirPathDoneLong = yyyyMMddDirPathDone.sum();
        long examUploaderIdFileSize = allExamUploaderIdFileSize.sum();
        long fileCount = allItemCount.sum();
        long fileSize = allItemFileSize.sum();
        
        log.info("{}/{}: {}; itemCount: {}; itemFileSize: {} - {}; examUploaderFileSize: {} - {}, item/examUploader: {}",
                yyyyMMddDirPathDoneLong, yyyyMMddDirPathCount, DisplayUtil.percent(yyyyMMddDirPathDoneLong, yyyyMMddDirPathCount),
                fileCount, fileSize, DisplayUtil.storageSize(fileSize),
                examUploaderIdFileSize, DisplayUtil.storageSize(examUploaderIdFileSize),
                DisplayUtil.percent(fileSize, examUploaderIdFileSize)
        );
    }
    
    private void print(String comment, String path, long size) {
        log.info("comment: {} : {}: {}", comment, DisplayUtil.paddingSpaceLeftAlign(path, 60), DisplayUtil.storageSize(size));
    }
    
    private long upload_answerCard_yyyy_m_d(Collection<String> dirList_yyyy_m_d) {
        return dirList_yyyy_m_d.stream()
                .map(this::upload_answerCard_yyyy_m_d)
                .reduce(0L, Long::sum);
    }
    
    private long upload_answerCard_yyyy_m_d(String dir_yyyy_m_d) {
        LongAdder fileSizeCount = new LongAdder();
        LongAdder fileCount = new LongAdder();
        FileStorageTemplate.listFileRecursive(dir_yyyy_m_d, itemFile -> {
            String itemFilePath = itemFile.getFilePath();
            allExamUploaderIdFileSize.add(itemFile.getFileSize());
            
            // item 都是 png
            if (!itemFilePath.endsWith(".png")) {
                type2InvalidPath.putIfAbsent("not png", itemFilePath);
                return;
            }
            // replace 不能删
            if (itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "replace" + FileStorageConstants.PATH_SEPARATOR)) {
                type2InvalidPath.putIfAbsent("replace", itemFilePath);
                return;
            }
            
            String subItemFilePath = itemFilePath.substring(dir_yyyy_m_d.length());
            String[] splits = subItemFilePath.split(FileStorageConstants.PATH_SEPARATOR);
            
            if (splits.length < 3) {
                type2InvalidPath.putIfAbsent("/ .size limit error", itemFilePath);
                return;
            }
            
            // yyyy/m/d/examId
            String examIdString = splits[0];
            Long examId = LongUtil.tryParse(examIdString);
            if (examId == null || examId < 1) {
                type2InvalidPath.putIfAbsent("not examId", itemFilePath);
                return;
            }
    
            // yyyy/m/d/examId/examUploaderId
            String examUploaderIdString = splits[1];
            Long examUploaderId = LongUtil.tryParse(examUploaderIdString);
            if (examUploaderId == null || examUploaderId < 1) {
                type2InvalidPath.putIfAbsent("not examUploaderId", itemFilePath);
                // log.error("{}", examUploaderIdDir);
                return;
            }
            
            if (splits.length == 3) {
                // log.warn("examId.examUploaderId.answerCard: {}", itemFile);
                type2InvalidPath.putIfAbsent("examId.examUploaderId.answerCard", itemFilePath);
                return;
            }
            boolean classIds = false;
            String itemDirName = splits[2];
            if (isClassIds(itemDirName)) {
                if (splits.length == 4) {
                    type2InvalidPath.putIfAbsent("examId.examUploaderId.classIds.answerCard", itemFilePath);
                    // log.warn("examId.examUploaderId.classIds.answerCard: {}", itemFile);
                    return;
                }
                classIds = true;
                itemDirName = splits[3];
            }
        
            if ("correct".equals(itemDirName) || itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "correct" + FileStorageConstants.PATH_SEPARATOR)) {
                // log.warn("correct: {}", itemFile);
                if (classIds) {
                    type2InvalidPath.putIfAbsent("correct", itemFilePath);
                } else {
                    type2InvalidPath.putIfAbsent("correct classIds", itemFilePath);
                }
                return;
            }
            if ("score".equals(itemDirName) || itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "score" + FileStorageConstants.PATH_SEPARATOR)) {
                // log.warn("score: {}", itemFile);
                if (classIds) {
                    type2InvalidPath.putIfAbsent("score", itemFilePath);
                } else {
                    type2InvalidPath.putIfAbsent("score classIds", itemFilePath);
                }
                return;
            }
            if ("template".equals(itemDirName) || itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "template" + FileStorageConstants.PATH_SEPARATOR)) {
                // log.warn("template: {}", itemFile);
                if (classIds) {
                    type2InvalidPath.putIfAbsent("template classIds", itemFilePath);
                } else {
                    type2InvalidPath.putIfAbsent("template", itemFilePath);
                }
                return;
            }
        
            if ("renew".equals(itemDirName) || itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "renew" + FileStorageConstants.PATH_SEPARATOR)) {
                // log.info("renew item: {}", itemFile);
                if (classIds) {
                    type2Path.putIfAbsent("renew classIds", itemFilePath);
                } else {
                    type2Path.putIfAbsent("renew", itemFilePath);
                }
            } else {
                String[] itemDirNameSplits = itemDirName.split("_");
                if (itemDirNameSplits.length >= 2) {
                    String itemDirNameSplit0 = itemDirNameSplits[0];
                    if (itemDirNameSplit0.startsWith("readAppend")) {
                        // log.info("target readAppend: {}", itemFile);
                        if (classIds) {
                            type2Path.putIfAbsent("readAppend classIds", itemFilePath);
                        } else {
                            type2Path.putIfAbsent("readAppend", itemFilePath);
                        }
                    } else if (LongUtil.tryParse(itemDirNameSplit0) == null) {
                        // log.warn("not target1: {}", itemFile);
                        if (classIds) {
                            type2InvalidPath.putIfAbsent("not readAppend or randomValue classIds", itemFilePath);
                        } else {
                            type2InvalidPath.putIfAbsent("not readAppend or randomValue", itemFilePath);
                        }
                        return;
                    } else {
                        // log.info("target: {}", itemFile);
                        if (classIds) {
                            type2Path.putIfAbsent("normal classIds", itemFilePath);
                        } else {
                            type2Path.putIfAbsent("normal", itemFilePath);
                        }
                    }
                } else {
                    // log.warn("not target2: {}", itemFile);
                    if (classIds) {
                        type2InvalidPath.putIfAbsent("not contains _ classIds", itemFilePath);
                    } else {
                        type2InvalidPath.putIfAbsent("not contains _", itemFilePath);
                    }
                    return;
                }
            }
            fileSizeCount.add(itemFile.getFileSize());
            fileCount.increment();
            allItemFileSize.add(itemFile.getFileSize());
            allItemCount.increment();
        });
        long fileSizeCountLong = fileSizeCount.sum();
        print("d", dir_yyyy_m_d, fileSizeCountLong);
        yyyyMMddDirPathDone.increment();
        yyyyMMddDirPath2FileSize.put(dir_yyyy_m_d, fileSizeCountLong);
        yyyyMMddDirPath2FileCount.put(dir_yyyy_m_d, fileCount.sum());
        return fileSizeCountLong;
    }
    
    private boolean isClassIds(String classIdsDirName) {
        String[] classIdsString = classIdsDirName.split(",");
        for (String classIdString : classIdsString) {
            Long classId = LongUtil.tryParse(classIdString);
            if (classId == null) {
                return false;
            }
        }
        return true;
    }
}
