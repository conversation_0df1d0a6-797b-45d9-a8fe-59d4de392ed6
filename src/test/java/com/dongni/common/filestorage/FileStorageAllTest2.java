package com.dongni.common.filestorage;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageInfo;
import com.dongni.commons.utils.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * product-aliyun
 *   执行时间大概三个小时
 *   执行时间2023/02/02 阿里云数据为56.46TB (文件数880,877,876)
 *                                    : fileCount: 825634958  ; fileSize: 43963956208576   - 39.985 TB
 *   abc/                             : fileCount: 3          ; fileSize: 1337745382       - 1.246 GB
 *   backup/                          : fileCount: 1          ; fileSize: 78               - 78.000 B
 *   document/                        : fileCount: 1          ; fileSize: 206887           - 202.038 KB
 *   expire/                          : fileCount: 4568       ; fileSize: 7433452126       - 6.923 GB
 *   export/                          : fileCount: 15132      ; fileSize: 2682503634       - 2.498 GB
 *   http:/                           : fileCount: 44         ; fileSize: 12690489         - 12.103 MB
 *   null2019/                        : fileCount: 392        ; fileSize: 87145856         - 83.109 MB
 *   replace/                         : fileCount: 11         ; fileSize: 2292936          - 2.187 MB
 *   resource/                        : fileCount: 37         ; fileSize: 4083865          - 3.895 MB
 *   roll/                            : fileCount: 376        ; fileSize: 88088122         - 84.007 MB
 *   static/                          : fileCount: 235181     ; fileSize: 6955306692       - 6.478 GB
 *   template/                        : fileCount: 28         ; fileSize: 800588           - 781.824 KB
 *   temporary-file/                  : fileCount: 45         ; fileSize: 1208378410       - 1.125 GB
 *   test/                            : fileCount: 2          ; fileSize: 396406           - 387.115 KB
 *   tidb/                            : fileCount: 0          ; fileSize: 0                - 0.000 B
 *   upload/                          : fileCount: 825378645  ; fileSize: **************   - 39.967 TB
 *   upload/HGscan/                   : fileCount: 9          ; fileSize: 436105373        - 415.902 MB
 *   upload/answerCard/               : fileCount: 787245088  ; fileSize: 38109345760640   - 34.660 TB
 *   upload/answerCard/2010/          : fileCount: 4          ; fileSize: 2331118          - 2.223 MB
 *   upload/answerCard/2016/          : fileCount: 1888101    ; fileSize: 34126982481      - 31.783 GB
 *   upload/answerCard/2017/          : fileCount: 14277064   ; fileSize: 462899755060     - 431.109 GB
 *   upload/answerCard/2018/          : fileCount: 40617487   ; fileSize: 830026956383     - 773.023 GB
 *   upload/answerCard/2019/          : fileCount: 78818909   ; fileSize: 1217498800373    - 1.107 TB
 *   upload/answerCard/2020/          : fileCount: 139302913  ; fileSize: *************    - 4.332 TB
 *   upload/answerCard/2021/          : fileCount: 233505829  ; fileSize: 13725927336520   - 12.484 TB
 *   upload/answerCard/2022/          : fileCount: 272142233  ; fileSize: 16067466334331   - 14.613 TB
 *   upload/answerCard/2023/          : fileCount: 6283660    ; fileSize: 556586076265     - 518.361 GB
 *   upload/answerCard/2028/          : fileCount: 2          ; fileSize: 504903           - 493.069 KB
 *   upload/answerCard/correct/       : fileCount: 82544      ; fileSize: 74477178594      - 69.362 GB
 *   upload/answerCard/pdf/           : fileCount: 117852     ; fileSize: 27165501494      - 25.300 GB
 *   upload/answerCard/template/      : fileCount: 199979     ; fileSize: 38037764461      - 35.425 GB
 *   upload/answerCard/zip/           : fileCount: 8511       ; fileSize: 311638125090     - 290.236 GB
 *   upload/answerCardTemplate/       : fileCount: 114594     ; fileSize: 86804561064      - 80.843 GB
 *   upload/answerPath/               : fileCount: 133289     ; fileSize: 111544732672     - 103.884 GB
 *   upload/ckeditor/                 : fileCount: 4334       ; fileSize: 154391886        - 147.240 MB
 *   upload/declaration/              : fileCount: 6          ; fileSize: 1621569          - 1.546 MB
 *   upload/entrust/                  : fileCount: 1069250    ; fileSize: 26972116597      - 25.120 GB
 *   upload/entrustPath/              : fileCount: 87376      ; fileSize: 172877151235     - 161.004 GB
 *   upload/exam/                     : fileCount: 183118     ; fileSize: 61766715459      - 57.525 GB
 *   upload/exercise/                 : fileCount: 1825       ; fileSize: 280630376        - 267.630 MB
 *   upload/export/                   : fileCount: 35013      ; fileSize: 9030770048       - 8.411 GB
 *   upload/feedback/                 : fileCount: 325        ; fileSize: 292968303        - 279.396 MB
 *   upload/homework/                 : fileCount: 36199271   ; fileSize: 5351936802951    - 4.868 TB
 *   upload/material/                 : fileCount: 599        ; fileSize: 108479502        - 103.454 MB
 *   upload/personal-info/            : fileCount: 425        ; fileSize: 133683363        - 127.490 MB
 *   upload/roll/                     : fileCount: 81879      ; fileSize: 4351597852       - 4.053 GB
 *   upload/system/                   : fileCount: 5          ; fileSize: 3092811          - 2.950 MB
 *   upload/tool/                     : fileCount: 10322      ; fileSize: 1354767157       - 1.262 GB
 *   upload/ueditor/                  : fileCount: 211869     ; fileSize: 4806052697       - 4.476 GB
 *   upload/video/                    : fileCount: 48         ; fileSize: 1822303738       - 1.697 GB
 *   wechat/                          : fileCount: 489        ; fileSize: 118379465        - 112.895 MB
 *
 *   执行时间大概四个小时 占用大量内存
 *   执行时间2023/08/09 阿里云数据为 57.24TB (文件数1,059,974,674)
 *                                            : fileCount: 1059901135 ; fileSize: 62929591604784 - 57.234 TB   ; 2016-05-31 11:41:26 ~ 2023-08-09 14:38:50 ; min: upload/answerCard/2016/5/93/94/image0837.png; max: upload/answerCard/2023/8/9/2409663/2622478/2639949_DN0809000239/2401v0v0.png
 *   abc/                                     : fileCount: 3          ; fileSize: 1337745382     - 1.246 GB    ; 2020-12-07 22:39:59 ~ 2020-12-22 10:20:59 ; min: abc/def.png; max: abc/泸西一中343班错题本 (7).zip
 *   backup/                                  : fileCount: 1          ; fileSize: 78             - 78.000 B    ; 2021-04-22 11:49:03 ~ 2021-04-22 11:49:03 ; min: backup/tidb/backup.lock; max: backup/tidb/backup.lock
 *   document/                                : fileCount: 1          ; fileSize: 206887         - 202.038 KB  ; 2020-04-08 16:53:44 ~ 2020-04-08 16:53:44 ; min: document/notification/tiku/yiqi/5656fa894a6d4f699bc94ee1a32ccf5e/notification_tiku_yiqi.pdf; max: document/notification/tiku/yiqi/5656fa894a6d4f699bc94ee1a32ccf5e/notification_tiku_yiqi.pdf
 *   expire/                                  : fileCount: 4456       ; fileSize: 20922751592    - 19.486 GB   ; 2023-08-06 08:59:08 ~ 2023-08-09 12:19:31 ; min: expire/60/974907792_1262799887/1691283548257.docx; max: expire/1/1638766627_2072617650/1691554771421.xls
 *   export/                                  : fileCount: 15132      ; fileSize: 2682503634     - 2.498 GB    ; 2016-09-03 17:46:37 ~ 2020-04-14 11:39:30 ; min: export/examReport/xls/258/学生成绩.xls; max: export/template/introduction.pdf
 *   http:/                                   : fileCount: 44         ; fileSize: 12690489       - 12.103 MB   ; 2022-01-05 23:45:05 ~ 2022-03-24 11:26:30 ; min: http://cdntest.dongni100.com/upload/answerCardTemplate/2022/1/5/1190302/1341863071237898/1185635/57373_1.png; max: http://cdntest.dongni100.com/upload/answerCardTemplate/2022/3/24/1199426/1341863071350746/1194836/63786_1.png
 *   null2019/                                : fileCount: 392        ; fileSize: 87145856       - 83.109 MB   ; 2019-05-21 10:54:19 ~ 2019-10-15 11:06:49 ; min: null2019/5/52965/GZ8fGtLriDIoFu2V3lZjT1vbLuvoa7QLZa73akZtuA6pc0KWvJjP0JjgUkTjZm-Q.jpg; max: null2019/10/58552/wrfIgc5hn2t6XG0A3xIVGMiSTmx_WjD5ICsBRS6HVJnCxxEwdKn7dxgs-jGX4LMo.jpg
 *   qa_a4a1fca9_4b6e_4453_a8c3_47eb74a0e7d0/ : fileCount: 35         ; fileSize: 3355839854     - 3.125 GB    ; 2023-08-02 16:14:49 ~ 2023-08-05 16:24:25 ; min: qa_a4a1fca9_4b6e_4453_a8c3_47eb74a0e7d0/automated/school_info/362.xls; max: qa_a4a1fca9_4b6e_4453_a8c3_47eb74a0e7d0/automated/answer_card_zip/362_2.zip
 *   replace/                                 : fileCount: 11         ; fileSize: 2292936        - 2.187 MB    ; 2022-05-13 21:41:28 ~ 2023-01-08 13:24:05 ; min: replace/3.png; max: replace/34.png
 *   resource/                                : fileCount: 37         ; fileSize: 4083865        - 3.895 MB    ; 2019-09-09 16:18:13 ~ 2020-05-23 19:03:43 ; min: resource/image/dn_logo_export.png; max: resource/image/audit-result.png
 *   roll/                                    : fileCount: 376        ; fileSize: 88088122       - 84.007 MB   ; 2019-01-21 21:59:29 ~ 2019-01-21 22:08:13 ; min: roll/stat/1548079166652/; max: roll/stat/326/1548079688209石承鑫.png/
 *   static/                                  : fileCount: 265121     ; fileSize: **********     - 6.921 GB    ; 2019-09-10 21:42:33 ~ 2023-08-09 10:56:42 ; min: static/css/app.763cfc77.css; max: static/fat-web/static/js/dll/prod/fabric.dll.js.LICENSE.txt
 *   template/                                : fileCount: 28         ; fileSize: 799564         - 780.824 KB  ; 2017-01-19 21:46:43 ~ 2023-03-27 11:49:16 ; min: template/school/班级导入模版.xls; max: template/school/教师名单模板.xls
 *   temporary-file/                          : fileCount: 52         ; fileSize: **********     - 1.632 GB    ; 2020-11-15 22:04:42 ~ 2023-07-26 11:23:37 ; min: temporary-file/export/senior/zip/2020/11/15/68123/深圳市龙华高级中学2020-2021学年第一学期期中考试2020届(组合排名).zip; max: temporary-file/export/senior/zip/2023/7/26/2481005/2022-2023下厦门市高一期末质检(组合排名).zip
 *   test/                                    : fileCount: 1          ; fileSize: 381254         - 372.318 KB  ; 2022-12-13 20:44:24 ~ 2022-12-13 20:44:24 ; min: test/110657655708_1669989772421.jpg/110657655708_1669989772421.jpeg; max: test/110657655708_1669989772421.jpg/110657655708_1669989772421.jpeg
 *   tidb/                                    : fileCount: 0          ; fileSize: 0              - 0.000 B     ; null                ~ null                ; min: null; max: null
 *   upload/                                  : fileCount: 1059614954 ; fileSize: 62891795223800 - 57.200 TB   ; 2016-05-31 11:41:26 ~ 2023-08-09 14:38:50 ; min: upload/answerCard/2016/5/93/94/image0837.png; max: upload/answerCard/2023/8/9/2409663/2622478/2639949_DN0809000239/2401v0v0.png
 *   upload/HGscan/                           : fileCount: 11         ; fileSize: 525734661      - 501.380 MB  ; 2022-09-25 17:45:05 ~ 2023-03-30 13:55:20 ; min: upload/HGscan/HQScanner_v1.0.9/HQScanner_v1.0.9.apk; max: upload/HGscan/HQScanner_v1.2.1/HQScanner_v1.2.1.apk
 *   upload/TestData/                         : fileCount: 1          ; fileSize: 686450         - 670.361 KB  ; 2023-02-13 18:09:55 ~ 2023-02-13 18:09:55 ; min: upload/TestData/答题卡压缩包/A3-2.zip; max: upload/TestData/答题卡压缩包/A3-2.zip
 *   upload/answerCard/                       : fileCount: 1020943290 ; fileSize: 56929445806427 - 51.777 TB   ; 2016-05-31 11:41:26 ~ 2023-08-09 14:38:50 ; min: upload/answerCard/2016/5/93/94/image0837.png; max: upload/answerCard/2023/8/9/2409663/2622478/2639949_DN0809000239/2401v0v0.png
 *   upload/answerCard/2010/                  : fileCount: 4          ; fileSize: 2331118        - 2.223 MB    ; 2022-03-31 12:53:49 ~ 2022-03-31 12:53:50 ; min: upload/answerCard/2010/9/7/1201143/1196345/template/1283789398006.png; max: upload/answerCard/2010/9/7/1201143/1196345/template/1283789404912.png
 *   upload/answerCard/2011/                  : fileCount: 1          ; fileSize: 241168         - 235.516 KB  ; 2023-06-05 16:48:14 ~ 2023-06-05 16:48:14 ; min: upload/answerCard/2011/1/7/2403866/2590211/GIN4EIF8_1_DN0107000001.png; max: upload/answerCard/2011/1/7/2403866/2590211/GIN4EIF8_1_DN0107000001.png
 *   upload/answerCard/2016/                  : fileCount: 1888101    ; fileSize: 34124721129    - 31.781 GB   ; 2016-05-31 11:41:26 ~ 2023-03-22 04:24:31 ; min: upload/answerCard/2016/5/93/94/image0837.png; max: upload/answerCard/2016/12/1006/1114/421,422,423,424/040081/31.png
 *   upload/answerCard/2017/                  : fileCount: 14277064   ; fileSize: 462699900719   - 430.923 GB  ; 2017-01-03 09:22:33 ~ 2023-06-23 17:12:28 ; min: upload/answerCard/2017/1/1035/1143/高三化学A0006.tif; max: upload/answerCard/2017/10/25/4202/7656/1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479/322972137_10周1415.tif
 *   upload/answerCard/2018/                  : fileCount: 40617487   ; fileSize: 824888451365   - 768.237 GB  ; 2018-01-01 20:10:43 ~ 2023-06-23 02:14:28 ; min: upload/answerCard/2018/1/1/10439/10115/-2045625840_12.30yw10990144.tif; max: upload/answerCard/2018/3/27/16103/13410/915333030_10336.tif
 *   upload/answerCard/2019/                  : fileCount: 78818909   ; fileSize: 1215992835034  - 1.106 TB    ; 2019-01-01 20:06:02 ~ 2023-07-08 18:31:25 ; min: upload/answerCard/2019/1/1/42765/28373/475085841_DN0101000291.TIF; max: upload/answerCard/2019/6/17/54510/42008/4184,4185/11763483_DN0617000033.TIF
 *   upload/answerCard/2020/                  : fileCount: 139302913  ; fileSize: 3090388943790  - 2.811 TB    ; 2020-01-01 08:35:51 ~ 2023-05-22 07:00:31 ; min: upload/answerCard/2020/1/1/62362/57631/-1394864092_101117575_Scanc/2-1_5.png; max: upload/answerCard/2020/11/15/133693/107176/1287568712_DN1112000199/2-1-3.png
 *   upload/answerCard/2021/                  : fileCount: 233516911  ; fileSize: 12961104784865 - 11.788 TB   ; 2021-01-01 07:40:21 ~ 2023-03-19 16:54:59 ; min: upload/answerCard/2021/1/1/140321/1912368607/1240843398_148.png; max: upload/answerCard/2021/5/27/157700/148767/2491229_DN0319000903/2-1_13.png
 *   upload/answerCard/2022/                  : fileCount: 272162389  ; fileSize: 15380493258384 - 13.988 TB   ; 2022-01-01 00:20:17 ~ 2023-08-01 14:50:43 ; min: upload/answerCard/2022/1/1/1189908/1185107/-726931706_HHEDU20211231000041.jpg; max: upload/answerCard/2022/9/30/1489704/1513686/112736/1837704603_DN0422000020.TIF
 *   upload/answerCard/2023/                  : fileCount: 239733183  ; fileSize: 22093901919342 - 20.094 TB   ; 2023-01-01 00:00:49 ~ 2023-08-09 14:38:50 ; min: upload/answerCard/2023/1/1/1589951/3909980600/1783707990_621595658.png; max: upload/answerCard/2023/8/9/2409663/2622478/2639949_DN0809000239/2401v0v0.png
 *   upload/answerCard/2028/                  : fileCount: 2          ; fileSize: 504903         - 493.069 KB  ; 2021-11-24 11:10:45 ~ 2021-11-24 11:37:15 ; min: upload/answerCard/2028/1554543538088.png; max: upload/answerCard/2028/3-color.png
 *   upload/answerCard/correct/               : fileCount: 95872      ; fileSize: 84371620395    - 78.577 GB   ; 2023-07-29 00:00:43 ~ 2023-08-09 12:21:41 ; min: upload/answerCard/correct/2023/7/29/2408667/2620875/2638302_0171.png; max: upload/answerCard/correct/2023/8/9/2409676/2600235/2668729_DN0809000328.png
 *   upload/answerCard/pdf/                   : fileCount: 143656     ; fileSize: 32104537290    - 29.900 GB   ; 2020-03-13 00:28:59 ~ 2023-08-09 11:36:18 ; min: upload/answerCard/pdf/1336600885723168/1336610891235360.pdf; max: upload/answerCard/pdf/1341863071952435/1341863071952441.pdf
 *   upload/answerCard/template/              : fileCount: 371376     ; fileSize: 109039874766   - 101.551 GB  ; 2019-05-15 07:21:33 ~ 2023-08-09 12:25:09 ; min: upload/answerCard/template/2019/5/7875395853/1557876113979_071.jpg; max: upload/answerCard/template/2023/8/9/2409663/2600220/template/1691555110205.png
 *   upload/answerCard/zip/                   : fileCount: 15422      ; fileSize: 640331882159   - 596.356 GB  ; 2023-06-05 01:13:16 ~ 2023-08-09 12:10:26 ; min: upload/answerCard/zip/2023/6/5/2403409/2590055/1685898794059_008_DN0603000356.zip; max: upload/answerCard/zip/2023/8/9/2409621/2622473/1691554201949_015_426.ZIP
 *   upload/answerCardTemplate/               : fileCount: 114594     ; fileSize: 86804561064    - 80.843 GB   ; 2016-06-08 18:46:25 ~ 2022-04-11 11:31:35 ; min: upload/answerCardTemplate/54/1.png; max: upload/answerCardTemplate/2022/4/11/1203065/1341863071382147/1198050/65315_1.png
 *   upload/answerPath/                       : fileCount: 154984     ; fileSize: 131244035890   - 122.231 GB  ; 2019-04-05 10:51:08 ~ 2023-08-09 11:26:29 ; min: upload/answerPath/2019/4/25302/U10IWBAAMJ8TKJL.png; max: upload/answerPath/2023/8/23405268/精品解析：2023年高考广东卷物理真题（解析版）.docx
 *   upload/ckeditor/                         : fileCount: 4334       ; fileSize: 154391886      - 147.240 MB  ; 2019-04-05 11:01:51 ~ 2019-05-15 00:27:54 ; min: upload/ckeditor/answerCard/2019/4/4433141239/1554433308800_064.png; max: upload/ckeditor/answerCard/2019/5/7823261968/1557851271088_076.png
 *   upload/declaration/                      : fileCount: 15         ; fileSize: 5251896        - 5.009 MB    ; 2021-06-08 22:22:41 ~ 2023-02-28 14:02:33 ; min: upload/declaration/absent/1/491737/1623162161261_024.png; max: upload/declaration/absent/90007/110657518984/1677564155169_085.pdf
 *   upload/entrust/                          : fileCount: 1208519    ; fileSize: 31661588548    - 29.487 GB   ; 2019-06-12 15:38:30 ~ 2023-08-09 11:50:31 ; min: upload/entrust/2019/6/12/22590/1560325110238_078.png; max: upload/entrust/238867/1691553031176_041.png
 *   upload/entrustPath/                      : fileCount: 89726      ; fileSize: 177700041525   - 165.496 GB  ; 2016-06-08 19:00:21 ~ 2023-08-09 07:59:36 ; min: upload/entrustPath/2016/6/3082/1465383563490_数学.zip; max: upload/entrustPath/2023/8/23686072/238856/_1691539174046/晚测3教师版.docx
 *   upload/exam/                             : fileCount: 183118     ; fileSize: 61766715459    - 57.525 GB   ; 2017-05-28 02:31:27 ~ 2019-03-30 22:34:19 ; min: upload/exam/2017/5/2128/Y9DhMoFIqO27Knw4Zh3OFxlsf4zfiikZDG0fXrh7p1pN1SzJUlBrW1jUZLypII3O.jpg; max: upload/exam/2019/3/49000/-o_e44pX4THn6_vIeqkq9Cxc1u8vZP1Azlp-Ofgmr5KNsG8zomAJt2JoH-N59lLz.jpg
 *   upload/exercise/                         : fileCount: 1825       ; fileSize: 280630376      - 267.630 MB  ; 2017-03-06 02:49:30 ~ 2019-04-26 23:37:52 ; min: upload/exercise/2017/3/4/1488739665000_郭丞文.png; max: upload/exercise/2019/4/31700/oNA5SFUH6gtLKJk8qh1aa9IQfCIuIwlR9FOeqxYqMDFe4OCofD7S5tF141MTdS2t.jpg
 *   upload/export/                           : fileCount: 35013      ; fileSize: 9030770048     - 8.411 GB    ; 2019-04-05 08:55:11 ~ 2020-12-21 18:58:04 ; min: upload/export/2019/4/5/1554425711641_2019-04-05.docx; max: upload/export/泸西一中343班错题本-部分.zip
 *   upload/feedback/                         : fileCount: 325        ; fileSize: 292968303      - 279.396 MB  ; 2019-04-08 11:26:43 ~ 2019-10-15 21:27:24 ; min: upload/feedback/2019/4/9687/8HK20EOX7Z0LQZXC.png; max: upload/feedback/2019/10/15/20191015-2849/1571146040666_006.png
 *   upload/homework/                         : fileCount: 36571141   ; fileSize: 5449837011631  - 4.957 TB    ; 2018-05-12 08:30:21 ~ 2023-08-09 13:52:19 ; min: upload/homework/2018/5/21676/13865/21676/1526085021815.png; max: upload/homework/2537075/110657116613/2023/8/9/crop/110657116613_1691560339644.jpeg
 *   upload/material/                         : fileCount: 599        ; fileSize: 108479502      - 103.454 MB  ; 2018-10-13 09:51:46 ~ 2022-06-10 10:18:23 ; min: upload/material/2018/9/13/15393955038781; max: upload/material/2022/6/10/16548275061712
 *   upload/personal-info/                    : fileCount: 480        ; fileSize: 153081348      - 145.990 MB  ; 2022-01-27 15:12:41 ~ 2023-05-18 14:02:17 ; min: upload/personal-info/identify-card/2364653/1643267562284-0.****************.jpg; max: upload/personal-info/identify-card/23339609/1684389736534-0.****************.jpg
 *   upload/roll/                             : fileCount: 81879      ; fileSize: 4351597852     - 4.053 GB    ; 2018-10-29 09:34:27 ~ 2023-01-14 19:52:18 ; min: upload/roll/common/school/468/1540776867038.jpg; max: upload/roll/photo/2023/1/14/11/2/2/-1041382935_377051379.jpeg
 *   upload/system/                           : fileCount: 5          ; fileSize: 3092811        - 2.950 MB    ; 2016-09-06 14:24:20 ~ 2016-09-06 14:56:48 ; min: upload/system/news/2016/8/14731430574030815_640.webp.jpg; max: upload/system/news/2016/8/147314500569804_产品介绍.jpg
 *   upload/tool/                             : fileCount: 13173      ; fileSize: 1800279853     - 1.677 GB    ; 2019-10-16 08:42:33 ~ 2023-08-09 11:08:41 ; min: upload/tool/feedback/2019/10/16/2983/1597264310_493.png; max: upload/tool/feedback/2023/8/9/202389-5950/217441849_2100478580.jpg
 *   upload/ueditor/                          : fileCount: 211869     ; fileSize: 4806052697     - 4.476 GB    ; 2016-06-08 19:03:23 ~ 2019-10-15 08:57:58 ; min: upload/ueditor/image/2016/6/4b5addd0-fa00-4b72-ace9-9a8dccc290e4.jpg; max: upload/ueditor/image/2019/10/15/1571101078678_8b1da78076274f5e86a5de5217ed187b.png
 *   upload/video/                            : fileCount: 53         ; fileSize: 1822445573     - 1.697 GB    ; 2019-03-19 16:49:56 ~ 2023-03-01 14:54:27 ; min: upload/video/2019/03/19/46842/2/1548028/; max: upload/video/2023/03/01/2236180/451/1677653666794/20220919-102101.mp4
 *   wechat/                                  : fileCount: 489        ; fileSize: 118379465      - 112.895 MB  ; 2019-04-06 17:38:59 ~ 2019-09-24 11:41:35 ; min: wechat/test/1554543538088.png; max: wechat/homework/crop/1569296495142.png
 *
 * <AUTHOR>
 * 2023/02/02
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageAllTest2 {
    
    private final static Logger log = LoggerFactory.getLogger(FileStorageAllTest2.class);
    
    private final LongAdder allFileCount = new LongAdder();
    private final LongAdder allFileSize = new LongAdder();
    private final Map<String, StatisticInfo> path2Info = new ConcurrentHashMap<>();
    private final AtomicBoolean finished = new AtomicBoolean();
    
    /** 文件递归时最小的层次 FileStorageTemplate.listFileRecursive要求的最小层级 */
    private final int levelToListFileRecursiveLimit = 2;
    
    /**
     * 固定会有 ""
     * 对于specialDirPathSet的每一个，会进行路径处理
     *    如 upload/answerCard/2023/02/, 会在该变量中追加
     *       upload/
     *       upload/answerCard/
     *       upload/answerCard/2023/
     *       upload/answerCard/2023/02/
     */
    private final Set<String> allSpecialDirPathSet = new HashSet<>();
    
    // -------------------------------------------------------------------------
    
    /** 特殊处理的路径的文件递归层次 这个越大越快但是内存占用巨大*/
    private final int levelToListFileRecursiveSpecial = 4;
    
    /** 特殊处理的目录 对于特殊的目录，会使用levelToListFileRecursiveSpecial */
    private final Set<String> specialDirPathSet = Stream.of(
                    "upload/answerCard/",
                    null
            )
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
    
    @Test
    public void test() throws InterruptedException {
        // 统计的目录
        String statisticDirPath = "";
        
        init();
        printResultThread();
        statistic(statisticDirPath);
        finished.set(true);
        Thread.sleep(2000);
    }
    
    private void init() {
        allFileCount.reset();
        allFileSize.reset();
        path2Info.clear();
        finished.set(false);
        allSpecialDirPathSet.add("");
        for (String specialDirPath : specialDirPathSet) {
            String path = "";
            for (String s : specialDirPath.split("/")) {
                path = path + s + "/";
                allSpecialDirPathSet.add(path);
            }
        }
        log.warn("specialDirPathSet: {}", specialDirPathSet);
        log.warn("allSpecialDirPathSet: {}", allSpecialDirPathSet);
    }
    
    private void statistic(String dirPath) {
        statistic(dirPath, getLevel(dirPath), levelToListFileRecursiveLimit, new ArrayList<>());
    }
    
    private void statistic(String dirPath, int level, int levelToListFileRecursive, List<StatisticInfo> statisticInfoList) {
        List<StatisticInfo> currentStatisticInfoList = new ArrayList<>(statisticInfoList);
        StatisticInfo statisticInfo = new StatisticInfo();
        currentStatisticInfoList.add(statisticInfo);
        path2Info.put(dirPath, statisticInfo);
        
        if (allSpecialDirPathSet.contains(dirPath)) {
            // 目录下的文件
            FileStorageTemplate.listFile(dirPath, file -> {
                add(file, currentStatisticInfoList);
            });
            
            // 目录下的文件夹
            List<FileStorageInfo> childrenDirList = FileStorageTemplate.listDir(dirPath);
            int nextLevel = level + 1;
            int nextLevelToListFileRecursive = specialDirPathSet.contains(dirPath)
                    ? Math.max(levelToListFileRecursiveLimit, levelToListFileRecursiveSpecial)
                    : levelToListFileRecursiveLimit;
            childrenDirList.stream().forEach(childDir -> {
                String childDirPath = childDir.getFilePath();
                statistic(childDirPath, nextLevel, nextLevelToListFileRecursive, currentStatisticInfoList);
            });
        }
        
        else {
            List<String> childrenDirPathList = new CopyOnWriteArrayList<>();
            statisticLevelTopFileAndDirList(childrenDirPathList, dirPath, levelToListFileRecursive, level, currentStatisticInfoList);
            childrenDirPathList.stream().forEach(childrenDirPath -> {
                FileStorageTemplate.listFileRecursive(childrenDirPath, file -> {
                    add(file, currentStatisticInfoList);
                });
            });
        }
        
        statisticInfo.done.set(true);
    }
    
    private void statisticLevelTopFileAndDirList(List<String> dirPathList,
                                                 String dirPath,
                                                 int levelToListFileRecursive,
                                                 int level,
                                                 List<StatisticInfo> statisticInfoList) {
        if (level >= levelToListFileRecursive) {
            dirPathList.add(dirPath);
            return;
        }
        
        FileStorageTemplate.listFile(dirPath, file -> {
            add(file, statisticInfoList);
        });
        
        List<FileStorageInfo> levelDirList = FileStorageTemplate.listDir(dirPath);
        levelDirList.stream().forEach(levelDir -> {
            statisticLevelTopFileAndDirList(dirPathList, levelDir.getFilePath(), levelToListFileRecursive, level + 1, statisticInfoList);
        });
    }
    
    private void add(FileStorageInfo fileStorageInfo, List<StatisticInfo> statisticInfoList) {
        Long fileSize = fileStorageInfo.getFileSize();
        String filePath = fileStorageInfo.getFilePath();
        boolean hasSize = fileSize != null && fileSize > 0;
        if (hasSize) {
            Date lastModifyDate = fileStorageInfo.getLastModifyDate();
            for (StatisticInfo statistic : statisticInfoList) {
                statistic.fileCount.increment();
                statistic.fileSize.add(fileSize);
                statistic.setMinLastModifyDateAndFilePath(lastModifyDate, filePath);
                statistic.setMaxLastModifyDateAndFilePath(lastModifyDate, filePath);
            }
        }
    }
    
    private void printResultThread() {
        new Thread(() -> {
            while (true) {
                printResult();
                if (finished.get()) {
                    break;
                }
                
                try {
                    long now = System.currentTimeMillis();
                    long tmp = now % 1000;
                    Thread.sleep(1000 - tmp);
                } catch (InterruptedException ignore) {
                }
            }
            printResult();
        }).start();
    }
    
    private void printResult() {
        log.info(" =========================== ");
        List<String> pathSortList = path2Info.keySet().stream().sorted().collect(Collectors.toList());
        for (String path : pathSortList) {
            StatisticInfo statisticInfo = path2Info.get(path);
            long fileCount = statisticInfo.fileCount.sum();
            long fileSize = statisticInfo.fileSize.sum();
            Date minLastModifyDate = statisticInfo.minLastModifyDate;
            Date maxLastModifyDate = statisticInfo.maxLastModifyDate;
            String minLastModifyPath = statisticInfo.minLastModifyPath;
            String maxLastModifyPath = statisticInfo.maxLastModifyPath;
            boolean done = statisticInfo.done.get();
            String logMsg = "{} : fileCount: {}; fileSize: {} - {}; {} ~ {}; done: {}; min: {}; max: {}";
            Object[] logArgs = {
                    DisplayUtil.paddingSpaceLeftAlign(path, 40),
                    DisplayUtil.paddingSpaceRightAlign(fileCount, 11),
                    DisplayUtil.paddingSpaceRightAlign(fileSize, 16),
                    DisplayUtil.storageSize(fileSize),
                    DisplayUtil.paddingSpaceRightAlign(Optional.ofNullable(minLastModifyDate).map(DateUtil::formatDateTime).orElse(null), 20),
                    DisplayUtil.paddingSpaceRightAlign(Optional.ofNullable(maxLastModifyDate).map(DateUtil::formatDateTime).orElse(null), 20),
                    done,
                    minLastModifyPath,
                    maxLastModifyPath
            };
            if (done) {
                log.info(logMsg, logArgs);
            } else {
                log.warn(logMsg, logArgs);
            }
        }
    }
    
    private int getLevel(String dirPath) {
        int level = 0;
        for (int i = 0; i < dirPath.length(); i++) {
            if (dirPath.charAt(i) == '/') {
                level++;
            }
        }
        return level;
    }
    
    private static class StatisticInfo {
        private final LongAdder fileCount = new LongAdder();
        private final LongAdder fileSize = new LongAdder();
        private final AtomicBoolean done = new AtomicBoolean(false);
        
        private final Lock minLastModifyDateLock = new ReentrantLock();
        private Date minLastModifyDate = null;
        
        private String minLastModifyPath = null;
        
        private final Lock maxLastModifyDateLock = new ReentrantLock();
        private Date maxLastModifyDate = null;
        
        private String maxLastModifyPath = null;
        
        public void setMinLastModifyDateAndFilePath(Date lastModifyDate, String filePath) {
            if (lastModifyDate == null) { return; }
            if (this.minLastModifyDate == null || lastModifyDate.compareTo(minLastModifyDate) < 0) {
                minLastModifyDateLock.lock();
                try {
                    if (this.minLastModifyDate == null || lastModifyDate.compareTo(minLastModifyDate) < 0) {
                        this.minLastModifyDate = lastModifyDate;
                        this.minLastModifyPath = filePath;
                    }
                } finally {
                    minLastModifyDateLock.unlock();
                }
            }
        }
        
        public void setMaxLastModifyDateAndFilePath(Date lastModifyDate, String filePath) {
            if (lastModifyDate == null) { return; }
            if (this.maxLastModifyDate == null || lastModifyDate.compareTo(maxLastModifyDate) > 0) {
                maxLastModifyDateLock.lock();
                try {
                    if (this.maxLastModifyDate == null || lastModifyDate.compareTo(maxLastModifyDate) > 0) {
                        this.maxLastModifyDate = lastModifyDate;
                        this.maxLastModifyPath = filePath;
                    }
                } finally {
                    maxLastModifyDateLock.unlock();
                }
            }
        }
    }
}
