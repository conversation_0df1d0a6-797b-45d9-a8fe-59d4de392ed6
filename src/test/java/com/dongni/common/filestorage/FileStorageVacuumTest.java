package com.dongni.common.filestorage;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.commons.filestorage.entity.FileStorageInfo;
import com.dongni.commons.filestorage.vacuum.BaseFileStorageVacuumService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.LongAdder;

/**
 *
 * <AUTHOR>
 * 2022/11/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageVacuumTest {
    
    private final static Logger log = LoggerFactory.getLogger(FileStorageVacuumTest.class);
    
    @Autowired(required = false)
    private List<BaseFileStorageVacuumService> fileStorageVacuumServiceList;
    
    @Test
    public void testFileStorageVacuumServiceList() {
        if (CollectionUtils.isEmpty(fileStorageVacuumServiceList)) {
            log.info("fileStorageVacuumServiceList == null");
            return;
        }
        
        List<FileStorageInfo> vacuumList = new ArrayList<>();
        for (BaseFileStorageVacuumService fileStorageVacuumService : fileStorageVacuumServiceList) {
            log.info("");
            log.info("------- service: " + fileStorageVacuumService.getClass());
            
            String vacuumServiceName = fileStorageVacuumService.name();
            LongAdder currentFileCount = new LongAdder();
            LongAdder currentFileSizeCount = new LongAdder();
            fileStorageVacuumService.lockVacuum(false, fileStorageInfo -> {
                Long fileSize = fileStorageInfo.getFileSize();
                currentFileCount.increment();
                if (fileSize != null) {
                    currentFileSizeCount.add(fileSize);
                }
//                log.info("{}", fileStorageInfo);
            });
            
            
            long fileCount = currentFileCount.sum();
            long fileSize = currentFileSizeCount.sum();
            log.info("name: {}; fileCount:{}, fileSize: {} - {}", vacuumServiceName, fileCount, fileSize, DisplayUtil.storageSize(fileSize));
            
            FileStorageInfo vacuumInfo = new FileStorageInfo()
                    .setFilePath(vacuumServiceName)
                    .setFileSize(fileSize);
            vacuumList.add(vacuumInfo);
        }
        long allFileSizeSum = vacuumList.stream().mapToLong(FileStorageInfo::getFileSize).sum();
        
        log.info("");
        log.info("");
        log.info("------ vacuum: {} - {}", allFileSizeSum, DisplayUtil.storageSize(allFileSizeSum));
        for (FileStorageInfo vacuumInfo : vacuumList) {
            Long fileSize = vacuumInfo.getFileSize();
            log.info("vacuumFilePath: {}; fileSize:{} - {}",
                    vacuumInfo.getFilePath(),
                    fileSize,
                    DisplayUtil.storageSize(fileSize)
            );
        }
    }
    
}
