package com.dongni.common.filestorage;

import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageCopy;
import com.dongni.commons.filestorage.entity.FileStorageGet;
import com.dongni.commons.filestorage.entity.FileStorageInfo;
import com.dongni.commons.filestorage.entity.FileStoragePut;
import com.dongni.commons.filestorage.entity.FileStoragePutResult;
import com.dongni.commons.filestorage.enumeration.FileStoragePathEnum;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageTemplateTest {
    
    private final static Logger log = LoggerFactory.getLogger(FileStorageTemplateTest.class);
    
    @Test
    public void getToken() {
        System.out.println(FileStorageTemplate.getToken("214213"));
    }

    @Test
    public void put() {
        // 上传一个指定目录的永久文件
        String path1 = FileStorageTemplate.put(fileStorage -> {
            File file1 = new File("D:/maven.cer");
            File file2 = new File(fileStorage.getRootPath() + "test.xlsx");
            try {
                FileUtils.copyFile(file1, file2);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            fileStorage.setLocalFile(file2);
            fileStorage.setResourceKey(FileStoragePathEnum.ANSWER_CARD);
            fileStorage.setUniqueKey("41232/24123");
            fileStorage.setAutoExpire(false);
        });
        System.out.println(path1);
    }

    @Test
    public void putForAutoExpire() {
        // 上传一个自动过期文件
        String path1 = FileStorageTemplate.put(fileStorage -> {
            File file1 = new File("D:/maven.cer");
            File file2 = new File(fileStorage.getRootPath() + "maven.cer");
            try {
                FileUtils.copyFile(file1, file2);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
            fileStorage.setLocalFile(file2);
            fileStorage.setAutoExpire(true);
        });
        System.out.println(path1);
    }

    @Test
    public void putForInputStream() {
        // 上传一个自动过期的文件，使用流的方式
        String path1 = FileStorageTemplate.put(fileStorage -> {
            File file1 = new File("D:/test.xlsx");
            File file2 = new File(fileStorage.getRootPath() + "test.xlsx");
            try {
                FileUtils.copyFile(file1, file2);
                fileStorage.setInputStream(new FileInputStream(file2));
                fileStorage.setFileName("test.xlsx");
                fileStorage.setAutoExpire(true);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
        });
        System.out.println(path1);
    }

    @Test
    public void get() {
        // 下载单个文件
        Map<Integer, File> map = new HashMap<>();
        FileStorageTemplate.get("expire/3/478354784_913/1566382803947", file -> {
            map.put(1, file);
            System.out.println(file.exists());
            System.out.println(file.getName());
            System.out.println(file.canRead());
        });

        File file = map.get(1);
        System.out.println(file.exists());
        System.out.println(file.getName());
        System.out.println(file.canRead());
    }

    @Test
    public void batchGet() {
        // 批量下载
        List<FileStorageGet> fileStorageGets = new ArrayList<>();
        FileStorageGet fileStorageGet1 = new FileStorageGet();
        fileStorageGet1.setFilePath("expire/3/-731369648_54/1566525801558");
        fileStorageGets.add(fileStorageGet1);

        FileStorageGet fileStorageGet2 = new FileStorageGet();
        fileStorageGet2.setFilePath("expire/3/-731369648_54/1566525801558");
        fileStorageGets.add(fileStorageGet2);

        FileStorageTemplate.batchGet(fileStorageGets, fileStorageBatchGet -> {
            fileStorageBatchGet.getFileStorageGets().forEach(fileStorageGet -> {
                System.out.println(fileStorageGet.getFile().getAbsolutePath());
            });
        });
    }

    @Test
    public void batchPut() {
        // 批量上传，用传文件的形式
        FileStorageTemplate.batchPut(fileStorageBatch -> {
            File file = new File("D:/test");

            File target = new File(fileStorageBatch.getRootPath() + "test");
            try {
                FileUtils.copyDirectoryToDirectory(file, target);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            String[] fileExtensions = {"png"};
            fileStorageBatch.setFiles((List<File>)FileUtils.listFiles(target, fileExtensions, true));
            fileStorageBatch.setResourceKey(FileStoragePathEnum.ROLL_PHOTO);
            fileStorageBatch.setUniqueKey("123123");
            fileStorageBatch.setAutoExpire(false);
        });
    }

    @Test
    public void batchPutByDir() {
        // 批量上传，用传目录的形式
        List<FileStoragePutResult> fileStoragePutResults = FileStorageTemplate.batchPut(fileStorageBatch -> {
            File file = new File("D:/ziyuan");

            File target = new File(fileStorageBatch.getRootPath() + "ziyuan");
            try {
                FileUtils.copyDirectoryToDirectory(file, target);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            fileStorageBatch.setDir(target);
            fileStorageBatch.setResourceKey(FileStoragePathEnum.ANSWER_CARD);
            fileStorageBatch.setUniqueKey("41232/24123");
            fileStorageBatch.setAutoExpire(false);
            fileStorageBatch.setRandomFileName(false);
            fileStorageBatch.setOverrideFile(false);
        });
        System.out.println(fileStoragePutResults.size());
    }

    @Test
    public void local() {
        FileStorageTemplate.local(fileStorage -> {
            System.out.println(fileStorage.getRootPath());

            File file = new File("D:/manualTest");

            File target = new File(fileStorage.getRootPath() + "manualTest");
            try {
                FileUtils.copyDirectoryToDirectory(file, target);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
        });
    }

    @Test
    public void delete() {
        // 单个对象删除
        FileStorageTemplate.delete("expire/3/839353575_127/1567782824267.xlsx");
    }

    @Test
    public void batchDelete() {
        List<String> objectNames = new ArrayList<>();
        objectNames.add("expire/3/926298868_553/1567783169281.xlsx");
        objectNames.add("expire/3/482500446_837/1567783170117.xlsx");
        // 批量删除
        FileStorageTemplate.batchDelete(objectNames);
    }

    @Test
    public void deleteByDirPath() {
        FileStorageTemplate.deleteByDirPath("expire/3");
    }
    
    @Test
    public void listFile() {
        List<FileStorageInfo> fileStorageInfos = FileStorageTemplate.listFile("");
        fileStorageInfos.forEach(fileStorageInfo -> {
            log.info(fileStorageInfo.getFilePath());
        });
        log.info(fileStorageInfos.size() + "");
    }
    
    @Test
    public void listFileRecursive() {
        List<FileStorageInfo> fileStorageInfos = FileStorageTemplate.listFileRecursive("upload/answerCard/zip");
        fileStorageInfos.forEach(fileStorageInfo -> {
            log.info(fileStorageInfo.toString());
        });
        log.info(fileStorageInfos.size() + "");
    }
    
    @Test
    public void listDir() {
        List<FileStorageInfo> fileStorageInfos = FileStorageTemplate.listDir("upload/answerCard/");
        fileStorageInfos.forEach(fileStorageInfo -> {
            log.info(fileStorageInfo.getFilePath());
        });
        log.info(fileStorageInfos.size() + "");
    }
    
    @Test
    public void copy() {
        FileStorageTemplate.copy(fileStorageCopy -> {
            fileStorageCopy.setSourcePath("D:/school模板 - 副本.xls");
            fileStorageCopy.setDestinationPath("D:/fh/school模板 - 副本.xls");
        });
    }

    @Test
    public void batchCopy() {
        FileStorageTemplate.batchCopy(fileStorageCopyList -> {
            fileStorageCopyList.add(new FileStorageCopy("expire/3/test.png", "expire/3/copy/test.png"));
            fileStorageCopyList.add(new FileStorageCopy("expire/3/featureOutputImage.png", "expire/3/copy/featureOutputImage.png"));
        });
    }

    @Test
    public void replace() {
        // 替换
        FileStorageTemplate.replace(fileStorageReplace -> {
            File file1 = new File("D:/page3/test.txt");
            File file2 = new File(fileStorageReplace.getRootPath() + "test.txt");
            try {
                FileUtils.copyFile(file1, file2);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            fileStorageReplace.setFilePath("upload/answerCard/2019/9/22/12412/41231/correct/test.txt");
            fileStorageReplace.setLocalFile(file2);
        });
    }

    @Test
    public void generatePutDir() {
        FileStoragePut fileStoragePut = new FileStoragePut(FileStoragePathEnum.ANSWER_CARD, "12412/41231");
        System.out.println(FileStorageTemplate.generatePutDir(fileStoragePut));
    }

    @Test
    public void putAppend() {
        String path = FileStorageTemplate.putAppend(fileStoragePutAppend -> {
            File file1 = new File("D:/page3/test.txt");
            File file2 = new File(fileStoragePutAppend.getRootPath() + "test.txt");
            try {
                FileUtils.copyFile(file1, file2);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            try {
                fileStoragePutAppend.setInputStream(new FileInputStream(file2));
            } catch (FileNotFoundException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }
            fileStoragePutAppend.setExistFilePath("upload/answerCard/2019/9/22/12412/41231/");
            fileStoragePutAppend.setAppendDir("aaa/");
            fileStoragePutAppend.setFileName("bbbb.txt");
            fileStoragePutAppend.setRandomFileName(false);
        });

        System.out.println(path);
    }

    @Test
    public void batchPutAppend() {
        FileStorageTemplate.batchPutAppend(fileStoragePutAppend -> {
            File file = new File("D:/manualTest");

            File target = new File(fileStoragePutAppend.getRootPath() + "manualTest");
            try {
                FileUtils.copyDirectoryToDirectory(file, target);
            } catch (IOException e) {
                LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
            }

            String[] fileExtensions = {"png"};
            fileStoragePutAppend.setFileList((List<File>)FileUtils.listFiles(target, fileExtensions, true));
            fileStoragePutAppend.setExistFilePath("upload/answerCard/2019/9/22/12412/41231/");
        });
    }
}
