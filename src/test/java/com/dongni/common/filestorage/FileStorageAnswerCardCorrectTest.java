package com.dongni.common.filestorage;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.common.utils.FileStorageCommonUtil;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.constant.FileStorageConstants;
import com.dongni.commons.filestorage.enumeration.FileStoragePathEnum;
import com.dongni.commons.utils.JSONUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 已知的矫正图路径
 *
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/correct/fileName.png                   upload/answerCard/2019/11/16/60684/53340/correct/378989920_DN1116000934.png
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/correct/fileName/fileName.png          upload/answerCard/2018/5/28/23375/17290/correct/-58558107_政治周六0360/-58558107_政治周六0360.png
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/correct/correct/fileName.png           upload/answerCard/2022/5/7/1305692/1391290/correct/correct/1733833825_DN0507000007.png
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/correct/fileName.png          upload/answerCard/2018/5/28/23362/17271/1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867/correct/-2102968138_wuli1097.png
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/correct/fileName/fileName.png upload/answerCard/2018/5/28/23389/17307/656,657,659,660,661,662,663,664,665,666,667,668/correct/-592502517_10791/-592502517_10791.png
 * upload/answerCard/yyyy/m/d/examId/examUploaderId/classIds/correct/correct/fileName.png  upload/answerCard/2020/5/18/116196/67693/5490,5491,5492,5493,5494,5495,5496,5497,5498,5499,5500,5501,5502,5503,5504,5505,5506,5507,5508,5509/correct/correct/-723365124_DN0518000015.png
 *
 * 统计路径包含 /correct/ 则视为矫正图片
 * dongni-product
 *   2023-01-28
 *   文件存储总量: 约 60 TB
 *   t_answer_card 矫正图片数量:  39886788
 *   t_answer_card 矫正图片占用:  18.487 TB (20326129362294)
 *   examUploaderId下文件占用:    52.569 TB (57799737855182)
 *
 *   矫正图片占存储总量:     约 30.81 %
 *   矫正图片占examUploaderId: 35.17 %
 *
 * <AUTHOR>
 * 2022/11/28
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageAnswerCardCorrectTest {
    
    private final static Logger log = LoggerFactory.getLogger(FileStorageVacuumTest.class);
    
    private final LongAdder allExamUploaderFileSize = new LongAdder();
    private final LongAdder allExamUploaderFileCount = new LongAdder();
    private final LongAdder allCorrectFileSize = new LongAdder();
    private final LongAdder allCorrectFileCount = new LongAdder();
    
    private final AtomicBoolean finished = new AtomicBoolean(false);
    
    private final Map<String, Long> yyyyMMddExamUploaderDirPath2FileSize = new ConcurrentHashMap<>();
    private final Map<String, Long> yyyyMMddExamUploaderDirPath2FileCount = new ConcurrentHashMap<>();
    
    private final Map<String, Long> yyyyMMddCorrectDirPath2FileSize = new ConcurrentHashMap<>();
    private final Map<String, Long> yyyyMMddCorrectDirPath2FileCount = new ConcurrentHashMap<>();
    
    private final Map<String, Date> correctFile2LastModifyDate = new ConcurrentHashMap<>();
    
    private final LongAdder yyyyMMddDirPathDone = new LongAdder();
    private long yyyyMMddDirPathCount = 0;
    
    @Test
    public void test() throws InterruptedException {
        String uploadAnswerCardDirPath = FileStoragePathEnum.ANSWER_CARD.getPath();
        Map<String, Integer> dirList2yyyyMMdd = FileStorageCommonUtil.getDirList2yyyyMMdd(uploadAnswerCardDirPath);
        yyyyMMddDirPathCount = dirList2yyyyMMdd.size();
        
        printResultThread();
        upload_answerCard_yyyy_m_d(dirList2yyyyMMdd.keySet());
        finished.set(true);
        Thread.sleep(2000);
        
        List<String> dirPathListSorted = dirList2yyyyMMdd.keySet().stream()
                .sorted(Comparator.comparing(dirList2yyyyMMdd::get))
                .collect(Collectors.toList());
        for (String dirPath : dirPathListSorted) {
            Long examUploaderFileSize = yyyyMMddExamUploaderDirPath2FileSize.get(dirPath);
            Long examUploaderFileCount = yyyyMMddExamUploaderDirPath2FileCount.get(dirPath);
            Long correctFileSize = yyyyMMddCorrectDirPath2FileSize.get(dirPath);
            Long correctFileCount = yyyyMMddCorrectDirPath2FileCount.get(dirPath);
            log.info("path: {} ; correctCount: {}; correctSize: {} - {}; examUploaderCount: {}; examUploaderSize: {} - {}",
                    DisplayUtil.paddingSpaceLeftAlign(dirPath, 30),
                    correctFileCount, correctFileSize, DisplayUtil.storageSize(correctFileSize),
                    examUploaderFileCount, examUploaderFileSize, DisplayUtil.storageSize(examUploaderFileSize)
            );
        }
        
        Map<String, Date> sortCorrectFile2LastModifyDate = new LinkedHashMap<>();
        List<String> correctFilePathList = correctFile2LastModifyDate.keySet().stream().sorted().collect(Collectors.toList());
        for (String correctFilePath : correctFilePathList) {
            sortCorrectFile2LastModifyDate.put(correctFilePath, correctFile2LastModifyDate.get(correctFilePath));
        }
        System.out.println(JSONUtil.toJson(sortCorrectFile2LastModifyDate));
    }
    
    private void printResultThread() {
        new Thread(() -> {
            while (true) {
                printResult();
                if (finished.get()) {
                    break;
                }
                
                try {
                    long now = System.currentTimeMillis();
                    long tmp = now % 1000;
                    Thread.sleep(1000 - tmp);
                } catch (InterruptedException ignore) {
                }
            }
            printResult();
        }).start();
    }
    
    private void printResult() {
        long yyyyMMddDirPathDoneLong = yyyyMMddDirPathDone.sum();
        long allCorrectFileCountLong = allCorrectFileCount.sum();
        long allCorrectFileSizeLong = allCorrectFileSize.sum();
        long allExamUploaderFileCountLong = allExamUploaderFileCount.sum();
        long allExamUploaderFileSizeLong = allExamUploaderFileSize.sum();
        log.info("{}/{}: {}; correctCount: {}; correctSize: {} - {}; examUploaderCount: {}, examUploaderSize: {} - {}, correct/examUploader: {};",
                yyyyMMddDirPathDoneLong, yyyyMMddDirPathCount, DisplayUtil.percent(yyyyMMddDirPathDoneLong, yyyyMMddDirPathCount),
                allCorrectFileCountLong, allCorrectFileSizeLong, DisplayUtil.storageSize(allCorrectFileSizeLong),
                allExamUploaderFileCountLong, allExamUploaderFileSizeLong, DisplayUtil.storageSize(allExamUploaderFileSizeLong),
                DisplayUtil.percent(allCorrectFileSizeLong, allExamUploaderFileSizeLong)
        );
    }
    
    private void upload_answerCard_yyyy_m_d(Collection<String> dirList_yyyy_m_d) {
        dirList_yyyy_m_d.stream().forEach(this::upload_answerCard_yyyy_m_d);
    }
    
    private void upload_answerCard_yyyy_m_d(String dir_yyyy_m_d) {
        LongAdder currentExamUploaderFileSize = new LongAdder();
        LongAdder currentExamUploaderFileCount = new LongAdder();
        LongAdder currentCorrectFileSize = new LongAdder();
        LongAdder currentCorrectFileCount = new LongAdder();
        FileStorageTemplate.listFileRecursive(dir_yyyy_m_d, itemFile -> {
            String itemFilePath = itemFile.getFilePath();
            // 海草有些文件拿不到文件大小
            Long fileSize = itemFile.getFileSize();
            if (fileSize != null) {
                currentExamUploaderFileSize.add(fileSize);
                allExamUploaderFileSize.add(fileSize);
            }
            currentExamUploaderFileCount.increment();
            allExamUploaderFileCount.increment();
            
            if (itemFilePath.contains(FileStorageConstants.PATH_SEPARATOR + "correct" + FileStorageConstants.PATH_SEPARATOR)) {
                if (fileSize != null) {
                    currentCorrectFileSize.add(fileSize);
                    allCorrectFileSize.add(fileSize);
                }
                currentCorrectFileCount.increment();
                allCorrectFileCount.increment();
                correctFile2LastModifyDate.put(itemFilePath, itemFile.getLastModifyDate());
            }
        });
        yyyyMMddExamUploaderDirPath2FileSize.put(dir_yyyy_m_d, currentExamUploaderFileSize.sum());
        yyyyMMddExamUploaderDirPath2FileCount.put(dir_yyyy_m_d, currentExamUploaderFileCount.sum());
        yyyyMMddCorrectDirPath2FileSize.put(dir_yyyy_m_d, currentCorrectFileSize.sum());
        yyyyMMddCorrectDirPath2FileCount.put(dir_yyyy_m_d, currentCorrectFileCount.sum());
        yyyyMMddDirPathDone.increment();
    }
}
