package com.dongni.common.docx.aspose;


import com.dongni.common.utils.EscapeUtils;
import com.dongni.common.utils.ResourceUtils;
import com.dongni.common.utils.VMTool;
import com.dongni.exam.homework.bean.vo.QuestionTypeAIVO;
import com.dongni.exam.mark.bigmodel.bean.LLMChatResult;
import com.dongni.exam.mark.bigmodel.helper.LLMChatHelper;
import com.dongni.tiku.common.util.MapUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.FileWriter;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.dongni.common.docx.aspose.TimeToCsvBasic.writeToCsv;


@RunWith(SpringRunner.class)  // JUnit 4 的 Spring 支持
@SpringBootTest
public class AutoHomeWordAIByTextService {
    @Autowired
    LLMChatHelper llmChatHelper;
    @Autowired
    private WordToHtmlServiceTest wordToHtmlServiceTest;


    private final Logger log = LoggerFactory.getLogger(getClass());

    @Test
    public void test() throws Exception {
        //21.6
        removeWaterMark();
        // 输入和输出目录
        String inputDir = "word7";    // Word文档所在目录
        String outputDir = "html_16_end_17";   // HTML输出目录
        String copyOutputDir = "copyhtml";   // HTML输出目录
        TimeToCsvBasic.CSV_PATH ="16_end_17.csv";
        // 确保输出目录存在
        Files.createDirectories(Paths.get(outputDir));
        Files.createDirectories(Paths.get(copyOutputDir));

        File[] wordFiles = new File(inputDir).listFiles((dir, name) ->
                name.toLowerCase().endsWith(".docx") || name.toLowerCase().endsWith(".doc"));

        if (wordFiles == null || wordFiles.length == 0) {
            System.out.println("在目录 " + inputDir + " 中未找到Word文档");
            return;
        }
        // 1. 准备要写入的数据（示例：序号、记录时间、操作描述）
        List<List<Object>> dataList = new ArrayList<>();
        for (File wordFile : wordFiles) {
            long startTime1 = System.currentTimeMillis();
            String html = wordToHtmlServiceTest.wordToHtml(wordFile);
            long endTime1 = System.currentTimeMillis();
            endTime1 = endTime1 - startTime1;


            //String htmlOutput = questionType(html);


            long startTime2 = System.currentTimeMillis();
            Document parsedDoc = Jsoup.parse(html);
            Elements paragraphs = parsedDoc.select("h1, h2, h3, h4, h5, h6, p:not(td p), table");
            List<Element> data = new ArrayList<>();
            StringBuilder cueWord = new StringBuilder();
            for (Element paragraph : paragraphs) {
                data.add(paragraph);
                String text = paragraph.text();
//                if(text.length() > 50){
//                    text = text.substring(0, 50);
//                }
                cueWord.append(String.format("[[%d]]%s\n", data.size(), text));
            }
            long endTime2 = System.currentTimeMillis();
            endTime2 = endTime2 - startTime2;


            long startTime3 = System.currentTimeMillis();
            String htmlOutput = questionType(html,data,cueWord.toString(),parsedDoc);
            long endTime3 = System.currentTimeMillis();


            endTime3 = endTime3 - startTime3;
            long endTime = System.currentTimeMillis();
            long duration1 = endTime - startTime1;
            // 3. 生成目标HTML文件名（基于原Word文件名，替换后缀为.html）
            String fileName = wordFile.getName();
            String htmlFileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".html";
            String outputHtmlFileName = fileName.substring(0, fileName.lastIndexOf(".")) + "Output_" + "_html" + endTime1/1000
                    + "_jsoup" + endTime2/1000 + "_ai" + endTime3/1000
                    + "_all" +  duration1/1000 + ".html";

            // 2. 动态添加行数据（无需提前知道行数和列数）
            List<Object> row1 = new ArrayList<>();
            row1.add(fileName);
            row1.add(endTime1/1000);
            row1.add(endTime3/1000);
            dataList.add(row1); // 添加行

            File targetFile = new File(outputDir, htmlFileName);
            File outputTargetFile = new File(outputDir, outputHtmlFileName);
            // 4. 写入HTML内容到本地文件
            try (FileWriter writer = new FileWriter(targetFile)) {
                writer.write(html);
                System.out.println("HTML文件已生成：" + targetFile.getAbsolutePath());
            }catch (Exception e){
                e.printStackTrace();
            }

            // 4. 写入HTML内容到本地文件
            try (FileWriter writer = new FileWriter(outputTargetFile)) {
                writer.write(htmlOutput.toString());
                System.out.println("HTML文件已生成：" + outputTargetFile.getAbsolutePath());
            }catch (Exception e){
                e.printStackTrace();
            }

        }
// 3. 将 List 转换为 String[][] 二维数组（处理类型转换）
        String[][] dataArray = new String[dataList.size()][];
        for (int i = 0; i < dataList.size(); i++) {
            List<Object> row = dataList.get(i);
            String[] stringRow = new String[row.size()];

            // 逐个元素转换为 String（避免类型不匹配）
            for (int j = 0; j < row.size(); j++) {
                Object element = row.get(j);
                // 处理 null 值，避免 NPE
                stringRow[j] = (element != null) ? element.toString() : "";
            }

            dataArray[i] = stringRow;
        }

// 2. 写入 CSV（追加模式）
        writeToCsv(dataArray, true);
        System.out.println(123);
    }

    public String questionType(String html,List<Element> data,String cueWord,Document document){
        String questionPromptTpl = getReadFileToString(getDirPath() + "prompt_bot_question.vm");
        Map<String, Object> descMap = new HashMap<>();
        descMap.put("desc",cueWord);
        // 生成请求体并校验JSON合法性
        String prompt = VMTool.parse(questionPromptTpl, descMap);
        String requestDataTpl = getReadFileToString(getDirPath() + "request_bot_native.vm");
        Map<String, Object> requestDataMap = new HashMap<>();
        requestDataMap.put("prompt", EscapeUtils.escape(prompt));
        String requestData = VMTool.parse(requestDataTpl, requestDataMap);

//        if (!JSONUtil.isValid(requestData)) {
//            System.err.println("无效的requestData：" + requestData);
//            return;
//        }
        String outPutHtml = "";
        // 调用方法并获取结果（添加超时）
        Mono<LLMChatResult> monoResult = llmChatHelper.chatResponseMemo(requestData);
        String content = "";
        QuestionTypeAIVO questionTypeAIVO = new QuestionTypeAIVO();
        try {
            long startTime = System.currentTimeMillis();

            // 阻塞获取结果，超时120秒（覆盖请求超时+重试）
            LLMChatResult result = monoResult.block(Duration.ofSeconds(300));
            if (result != null) {
                content = result.getContent();

                long endTime = System.currentTimeMillis();

                long time = endTime - startTime;

                System.out.println("转换ai性能" + time / 1000);
                // 使用正则匹配提取标签内容
                questionTypeAIVO = new QuestionTypeAIVO();

                List<QuestionTypeAIVO> questionTypeAIVOS = parseToQuestionList(content);
                // if (sameQuestionType.contains("否")) {
                //  questionTypeAIVO.setMessage("检测到可能不是同一个题型");
                //throw new CommonException(ResponseStatusEnum.DATA_ERROR, "检测到不是同一个题型");
                //  }

//                List<QuestionTypeAIVO> paragraphs = new ArrayList<>();
//                String regex = "<(p|table)[^>]*>(.*?)</\\1>";
//
//                Pattern pattern = Pattern.compile(regex, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
//                Matcher matcher = pattern.matcher(html);
//
//                while (matcher.find()) {
//                    QuestionTypeAIVO questionSeparate = new QuestionTypeAIVO();
//                    questionSeparate.setContent(matcher.group(0));
//                    //System.out.println("分割内容: " + questionSeparate.content);
//                    paragraphs.add(questionSeparate);
//                }

//                // 使用Stream流找出最大值
//                Optional<Integer> max = questionTypeAIVOS.stream()
//                        .map(QuestionTypeAIVO::getEndLineNumber)
//                        .max(Integer::compare);
//                Integer maxLineNumber = max.orElse(1);
//
//                Integer[] array= new Integer[maxLineNumber];
//                for (QuestionTypeAIVO typeAIVO : questionTypeAIVOS) {
//                    Integer startLineNumber = typeAIVO.getStartLineNumber();
//                    Integer endLineNumber = typeAIVO.getEndLineNumber();
//                   // t[startLineNumber] = typeAIVO.getQuestionNumberInt();
//                    // 填充值
//                    for (int i = startLineNumber; i <= endLineNumber; i++) {
//                        array[i] =  typeAIVO.getQuestionNumberInt();
//                    }
//                }


                for (int i = 0; i < questionTypeAIVOS.size(); i++) {
                    QuestionTypeAIVO typeAIVO = questionTypeAIVOS.get(i);
                    QuestionTypeAIVO typeAIVONext = null;
                    if(i < questionTypeAIVOS.size() - 2){
                        typeAIVONext = questionTypeAIVOS.get(i + 1);
                    }
                    if (typeAIVO.getStartLineNumber() != null) {
                        data.get(typeAIVO.getStartLineNumber() - 1).before("<p>" +
                                ",检测题号为:" + typeAIVO.getQuestionNumberInt() +
                                ",检测类型为:" + typeAIVO.getQuestionTypeStr() +
                                ",检测行数为:" + typeAIVO.getStartLineNumber() +
                                ",检测选项个数为:" + typeAIVO.getOptionCount()+
                                ",检测分数为:" + typeAIVO.getScore() +
                                " </p>");

                       // Integer tem;
                        if (typeAIVONext != null && typeAIVO.getEndLineNumber() > typeAIVONext.getStartLineNumber()) {
                            //如果题目的结束行号比下一题的开始行号还大则结束行号等于下一题的开始行号-1
                            //  tem = typeAIVO.getEndLineNumber();
                            typeAIVO.setEndLineNumber(typeAIVONext.getStartLineNumber() - 1);
                            // typeAIVONext.setEndLineNumber(tem);
                        }

                        data.get(typeAIVO.getEndLineNumber() - 1).after("<p> " +
                                "题号结束" + typeAIVO.getQuestionNumberInt() +
                                "</p>");
                        System.out.println(data.get(typeAIVO.getEndLineNumber() - 1).outerHtml());
                    }
                }
//                StringBuilder stringBuilder = new StringBuilder();
//                for (Element datum : data) {
//                    if(datum.previousElementSibling() != null) {
//                        String html1 = datum.previousElementSibling().outerHtml();
////                        if(html1.contains("检测题号为") || html1.contains("题号结束")){
//                            //stringBuilder.append(datum.outerHtml());
////                        }
//                    }
//                    String html1 = datum.outerHtml();
//                    stringBuilder.append(datum.outerHtml());
//                }
                outPutHtml = document.outerHtml();
            }
        } catch (Exception e){
            log.error("获取结果失败：" + e.getMessage());
            e.printStackTrace();
        }
        System.out.println(outPutHtml);
        return outPutHtml;
    }

    private static String extractTag(String content, String tagName) {
        Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">");
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(1).trim() : "";
    }

    private String getReadFileToString(String resourcePath) {
        return ResourceUtils.readResourceFile2String(resourcePath);
    }


    String getDirPath() {
        return "/llm/word/blankfill/";
    }


    public static List<QuestionTypeAIVO> parseToQuestionList(String input) {

        List<QuestionTypeAIVO> questionList = new ArrayList<>();

        // 改进主匹配正则，确保正确分割题目块
        Pattern lineNumberPattern = Pattern.compile("<开始行号>(\\d+)</开始行号>.*?(?=<开始行号>|$)", Pattern.DOTALL);
        Matcher lineNumerbMatcher = lineNumberPattern.matcher(input);

        while (lineNumerbMatcher.find()) {
            String questionBlock = lineNumerbMatcher.group(); // 单个题的完整文本块
            QuestionTypeAIVO question = new QuestionTypeAIVO();

            int lineNumber = Integer.parseInt(lineNumerbMatcher.group(1));
            question.setStartLineNumber(lineNumber);

            Pattern endLineNumberPattern = Pattern.compile("<结束行号>(\\d+).*?</结束行号>");
            Matcher endLineNumberMatcher = endLineNumberPattern.matcher(questionBlock);
            if (endLineNumberMatcher.find()) {
                question.setEndLineNumber(Integer.parseInt(endLineNumberMatcher.group(1)));
            }


            // 2. 提取题号
            Pattern questionNumberPattern = Pattern.compile("<题号>(\\d+).*?</题号>");
            Matcher questionNumberMatcher = questionNumberPattern.matcher(questionBlock);
            if (questionNumberMatcher.find()) {
                question.setQuestionNumberInt(Integer.parseInt(questionNumberMatcher.group(1)));
            }

            // 3. 提取题型
            Pattern typePattern = Pattern.compile("<题型>(.*?)</题型>");
            Matcher typeMatcher = typePattern.matcher(questionBlock);
            if (typeMatcher.find()) {
                question.setQuestionTypeStr(typeMatcher.group(1));
            }

            // 4. 提取分值
            Pattern scorePattern = Pattern.compile("<分值>(\\d+).*?</分值>");
            Matcher scoreMatcher = scorePattern.matcher(questionBlock);
            if (scoreMatcher.find()) {
                question.setScore(MapUtil.getDouble(scoreMatcher.group(1)));
            }


            // 5. 提取选项个数
            Pattern optionCountPattern = Pattern.compile("<选项个数>(\\d+)</选项个数>");
            Matcher optionCountMatcher = optionCountPattern.matcher(questionBlock);
            if (optionCountMatcher.find()) {
                question.setOptionCount(Integer.parseInt(optionCountMatcher.group(1)));
            }
            questionList.add(question);
        }
        return questionList;
    }


    @Test
    public void   parseToQuestionList1() {

        String input = "<行号>6</行号>\n" +
                "<题号>1</题号>\n" +
                "<题型>单选题</题型>\n" +
                "<分值>3</分值>\n" +
                "<选项个数>4</选项个数>\n" +
                "\n" +
                "<行号>8</行号>\n" +
                "<题号>2</题号>\n" +
                "<题型>单选题</题型>\n" +
                "<分值>3</分值>\n" +
                "<选项个数>4</选项个数>\n" +
                "\n" +
                "<行号>10</行号>\n" +
                "<题号>3</题号>\n" +
                "<题型>单选题</题型>\n" +
                "<分值>3</分值>\n" +
                "<选项个数>4</选项个数>";
        List<QuestionTypeAIVO> questionList = new ArrayList<>();

        // 改进主匹配正则，确保正确分割题目块
        Pattern lineNumberPattern = Pattern.compile("<行号>(\\d+)</行号>.*?(?=<行号>|$)", Pattern.DOTALL);
        Matcher lineNumerbMatcher = lineNumberPattern.matcher(input);

        while (lineNumerbMatcher.find()) {
            String questionBlock = lineNumerbMatcher.group(); // 单个题的完整文本块
            QuestionTypeAIVO question = new QuestionTypeAIVO();

            int startLineNumber = Integer.parseInt(lineNumerbMatcher.group(1));
            question.setStartLineNumber(startLineNumber);

            // 2. 提取题号
            Pattern questionNumberPattern = Pattern.compile("<题号>(\\d+).*?</题号>");
            Matcher questionNumberMatcher = questionNumberPattern.matcher(questionBlock);
            if (questionNumberMatcher.find()) {
                question.setQuestionNumberInt(Integer.parseInt(questionNumberMatcher.group(1)));
            }

            // 3. 提取题型
            Pattern typePattern = Pattern.compile("<题型>(.*?)</题型>");
            Matcher typeMatcher = typePattern.matcher(questionBlock);
            if (typeMatcher.find()) {
                question.setQuestionTypeStr(typeMatcher.group(1));
            }

            // 4. 提取分值
            Pattern scorePattern = Pattern.compile("<分值>(\\d+).*?</分值>");
            Matcher scoreMatcher = scorePattern.matcher(questionBlock);
            if (scoreMatcher.find()) {
                question.setScore(MapUtil.getDouble(scoreMatcher.group(1)));
            }


            // 5. 提取选项个数
            Pattern optionCountPattern = Pattern.compile("<选项个数>(\\d+)</选项个数>");
            Matcher optionCountMatcher = optionCountPattern.matcher(questionBlock);
            if (optionCountMatcher.find()) {
                question.setOptionCount(Integer.parseInt(optionCountMatcher.group(1)));
            }
        }
    }




    /**
     * 去除水印
     * 使用反射替换变量
     */
    public static void removeWaterMark() throws Exception {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
