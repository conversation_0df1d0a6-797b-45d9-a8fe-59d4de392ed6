package com.dongni.common.docx.aspose;

import com.aspose.words.*;
import com.dongni.common.font.FontRegister;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStoragePutResult;
import com.dongni.commons.utils.verify.Verify;
import com.dongni.tiku.common.util.MapUtil;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * word转html 使用asposeWord
 * 需要字体支持:
 *   simsun.ttc    宋体 支持中文
 *   times.ttf     公式中的英文字母 Times New Roman
 *   timesbd.ttf   公式中的英文字母 Times New Roman
 *   timesbi.ttf   公式中的英文字母 Times New Roman
 *   timesi.ttf    公式中的英文字母 Times New Roman
 *   symbol.ttf    公式中的运算符号 加减乘除 Symbol
 *   mtextra.ttf   公式中的三角形符号 圆形符号
 *   cambria.ttc   网上说对于复杂的数学公式能够提供较好的显示效果但是目前没看到有啥特别的
 */
@Service
public class WordToHtmlServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(WordToHtmlServiceTest.class);

    public String wordToHtml(File file1) {

        String wordUrl = "upload/entrustPath/2025/8/46601/60001/_1755685082425/20242025学年广西南宁市天桃实验学校九年级（下）单元作业数学试卷（二）（3月份）学生用卷2.docx";
        // System.out.println(wordUrl);

        // 下载一个word 解析后产生 html images/001.png images/002.png
        // 模拟将其上传到临时文件中
        AtomicReference<String> htmlRef = new AtomicReference<>();
        List<FileStoragePutResult> fileStoragePutResultList = new ArrayList<>();
        AtomicReference<Boolean> hasImage = new AtomicReference<>();
        hasImage.set(true);
        // 批量上传文件到临时文件夹中
        try {
            fileStoragePutResultList = FileStorageTemplate.batchPut(fileStorageBatchPut -> {
                // 上传临时目录 执行后会被自行清理
                // D:\home\test\FileStorageTemplateTest\temp_file\20250806\517123d51a7949ec8e8f36816b63c74d_760767346
                String putRootPath = fileStorageBatchPut.getRootPath();
                // 下载一个文件 随便下载一个意思一下啦
                FileStorageTemplate.get(wordUrl
                        , file -> {
                            // 假装解析出 html
                            try {
                                String html = batchConversion(file1, putRootPath);
                                // 从文件读取
                                org.jsoup.nodes.Document doc = Jsoup.parse(new File(html), "UTF-8");
                                htmlRef.set(doc.html());

                            } catch (Exception e) {
                                throw new RuntimeException(e);
                            }
                        });
                File directory = new File(putRootPath + "images");
                String[] files = directory.list();
                if (files != null && files.length > 0) {
                    // 自动过期
                    fileStorageBatchPut.setAutoExpire(true);
                    // 3天
                    fileStorageBatchPut.setExpireDays(3);
                    // 上传的目录 D:\home\test\FileStorageTemplateTest\temp_file\20250806\517123d51a7949ec8e8f36816b63c74d_760767346\images\
                    fileStorageBatchPut.setDir(new File(putRootPath + "images"));
                }else {
                    hasImage.set(false);
                }
            });
        }catch (Exception e){
            if(hasImage.get()){
                logger.error(e.getMessage());
            }
        }

        String html = htmlRef.get();
        for (FileStoragePutResult fileStoragePutResult : fileStoragePutResultList) {
            //System.out.println(" ------------------------ ");
            // D:\home\test\FileStorageTemplateTest\temp_file\20250806\d6d4be16feb1488fbca2da5ca55bd7eb_76411440\images\test2.jpg
            //System.out.println(fileStoragePutResult.getFile().getAbsolutePath());
            // test2.jpg
            //System.out.println(fileStoragePutResult.getFileName());
            // expire/1/d6d4be16feb1488fbca2da5ca55bd7eb_76411440/test2.jpg
            //System.out.println(fileStoragePutResult.getFilePath());
            String htmlSrc = "images" + "/" + fileStoragePutResult.getFileName();
            // http://cdn.dongni100.com/expire/1/d6d4be16feb1488fbca2da5ca55bd7eb_76411440/test2.jpg
            String publicSrc = FileStorageTemplate.getPublicUrl(fileStoragePutResult.getFilePath());
            html = html.replace(htmlSrc, publicSrc);
            htmlRef.set(html);
        }

        return html;
    }


    /**
     * 批量转换
     */
    public String batchConversion(File wordFile,String outputDir)  {
        // 21.6
        String htmlFilePath = "";
        //File wordFile = new File(inputFileName);
        try {
            removeWaterMark();
            String wordFileName = wordFile.getName();     // 10086.docx
            // 构建输出路径  因为需要将公式放大五倍 所以需要生成两次
            // 先输出 0.10086.docx.html  0.images/001.png       放大五倍的图片，html会被第二次输出覆盖
            // 再输出 0.10086.docx.html  0.imagesAbandon/001.png 原始大小的图片 但是这些是废弃的
            wordFileName.replace(".docx", ".html");
            wordFileName.replace(".doc", ".html");
            //String htmlFileName = "0." + wordFileName.replace(".docx", ".html").replace(".doc", ".html");
            String htmlFileName = "0." + wordFileName + ".html";  // 0.10086.docx.html

            String htmlImageDirName = "images";                 // 0.images
            String htmlImageAbandonDirName = "imagesAbandon";   // 0.imagesAbandon
            Path htmlImageDirPath = Files.createDirectories(Paths.get(outputDir, htmlImageDirName));
            Path htmlImageAbandonDirPath = Files.createDirectories(Paths.get(outputDir, htmlImageAbandonDirName));

            htmlFilePath = Paths.get(outputDir, htmlFileName).toString(); // ${outputDir}/0.10086.docx.html
            String htmlImageDir = htmlImageDirPath.toString();                   // ${outputDir}/0.image/
            String htmlImageAbandonDir = htmlImageAbandonDirPath.toString();     // ${outputDir}/0.imagesAbandon/

            // 加载Word文档
            Document doc = new Document(wordFile.getAbsolutePath());
            // 复制一份放大的公式 对于公式放大5倍处理
            Document docAmplify = doc.deepClone();
            // 遍历所有图片节点
            NodeCollection shapes = docAmplify.getChildNodes(NodeType.SHAPE, true);
            for (Shape shape : (Iterable<Shape>) shapes) {
                if (shape.hasImage()) {
                    int imageType = shape.getImageData().getImageType();
                    if (imageType == ImageType.WMF || imageType == ImageType.EMF) {
                        shape.setWidth(shape.getWidth() * 5.00);
                    }
                }
            }

            // 配置HTML保存选项
            HtmlSaveOptions options = new HtmlSaveOptions(SaveFormat.HTML);
            options.setOfficeMathOutputMode(HtmlOfficeMathOutputMode.MATH_ML); // 1 = 转换为MathML
            options.setCssStyleSheetType(CssStyleSheetType.INLINE);
            options.setImagesFolderAlias(htmlImageDirName); // html中src引用的  "0.images"
            options.setImageResolution(96);                 // 图片分辨率DPI 默认96
            options.setImageSavingCallback(imageSavingArgs -> {
                // 图片文件名称 默认为 文件名 + "." + 三位数需要 + 后缀 如 10086.docx.001.png
                String imageFileName = imageSavingArgs.getImageFileName(); // 10086.docx.001.png
                // 移除掉文件名 + "."
                String saveImageFileName = imageFileName.replace(wordFileName + ".", ""); // 001.png
                imageSavingArgs.setImageFileName(saveImageFileName);
            });
            options.setExportHeadersFootersMode(ExportHeadersFootersMode.NONE);  // 不输出页眉页脚
            options.setPrettyFormat(true); // 格式化输出

            // 先输出放大的
            options.setImagesFolder(htmlImageDir);        // ${outputDir}/0.images/         保存图片的文件夹
            docAmplify.save(htmlFilePath, options);       // ${outputDir}/0.10086.docx.html
            // 再输出不放大的
            options.setImagesFolder(htmlImageAbandonDir); // ${outputDir}/0.imagesAbandon/  不要的图片文件夹
            doc.save(htmlFilePath, options);              // ${outputDir}/0.10086.docx.html 覆盖掉第一次输出的
            // System.out.println("转换成功: " + wordFile.getName() + " -> " + new File(htmlFilePath).getAbsolutePath());
        } catch (Exception e) {
            //System.err.println("转换失败: " + wordFile.getName());
            e.printStackTrace();
        }
        // System.out.println("转换完成!");
        return htmlFilePath;

    }



    /**
     * 去除水印
     * 使用反射替换变量
     */
    public static void removeWaterMark() throws Exception {
        try {
            Class<?> zzodClass = Class.forName("com.aspose.words.zzod");
            Constructor<?> constructors = zzodClass.getDeclaredConstructors()[0];
            constructors.setAccessible(true);
            Object instance = constructors.newInstance(null, null);
            Field zzWws = zzodClass.getDeclaredField("zzWws");
            zzWws.setAccessible(true);
            zzWws.set(instance, 1);
            Field zzVZC = zzodClass.getDeclaredField("zzVZC");
            zzVZC.setAccessible(true);
            zzVZC.set(instance, 1);

            Class<?> zz83Class = Class.forName("com.aspose.words.zz83");
            constructors.setAccessible(true);
            constructors.newInstance(null, null);

            Field zzZY4 = zz83Class.getDeclaredField("zzZY4");
            zzZY4.setAccessible(true);
            ArrayList<Object> zzwPValue = new ArrayList<>();
            zzwPValue.add(instance);
            zzZY4.set(null, zzwPValue);

            Class<?> zzXuRClass = Class.forName("com.aspose.words.zzXuR");
            Field zzWE8 = zzXuRClass.getDeclaredField("zzWE8");
            zzWE8.setAccessible(true);
            zzWE8.set(null, 128);
            Field zzZKj = zzXuRClass.getDeclaredField("zzZKj");
            zzZKj.setAccessible(true);
            zzZKj.set(null, false);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 去除水印
     * 使用反射替换变量
     */
    public void removeWaterMark1() throws Exception {
        Class<?> aClass = Class.forName("com.aspose.words.zzXyu");
        Field zzZXG = aClass.getDeclaredField("zzZXG");
        zzZXG.setAccessible(true);
        Field modifiersField = zzZXG.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(zzZXG, zzZXG.getModifiers() & ~Modifier.FINAL);
        zzZXG.set(null,new byte[]{76, 73, 67, 69, 78, 83, 69, 68});
    }


}
