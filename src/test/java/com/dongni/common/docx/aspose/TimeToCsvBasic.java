package com.dongni.common.docx.aspose;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

public class TimeToCsvBasic {
    // CSV 文件路径（可自定义，如 "D:/logs/time_record.csv"）
    public static  String CSV_PATH = "time_record.csv";
    // 时间格式化器（Java 8+，线程安全，推荐）
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

    public static void main(String[] args) {
        // 1. 准备要写入的数据（示例：序号、记录时间、操作描述）
        List<List<String>> dataList = new ArrayList<>();

        // 2. 动态添加行数据（无需提前知道行数和列数）
        List<String> row1 = new ArrayList<>();
        row1.add("1");
        row1.add("2025-08-25 10:00:00");
        row1.add("启动程序");
        dataList.add(row1); // 添加第一行

        List<String> row2 = new ArrayList<>();
        row2.add("2");
        row2.add("2025-08-25 10:01:30");
        row2.add("执行任务A");
        dataList.add(row2); // 添加第二行

        // 可以继续添加更多行...
        List<String> row3 = new ArrayList<>();
        row3.add("3");
        row3.add("2025-08-25 10:03:15");
        row3.add("完成任务B");
        dataList.add(row3);

        // 3. 将 List 转换为 String[][] 二维数组（如果需要数组类型）
        String[][] dataArray = new String[dataList.size()][];
        for (int i = 0; i < dataList.size(); i++) {
            // 内层 List 转换为 String[]
            List<String> row = dataList.get(i);
            dataArray[i] = row.toArray(new String[0]); // 0 表示由集合自动确定长度
        }

        // 2. 写入 CSV（追加模式，避免覆盖历史数据）
        writeToCsv(dataArray, true);
    }

    /**
     * 获取当前格式化时间
     */
    private static String getCurrentTime() {
        // LocalDateTime 是 Java 8+ 新增的时间类，无时区问题，推荐使用
        LocalDateTime now = LocalDateTime.now();
        return now.format(TIME_FORMATTER);
    }

    /**
     * 写入 CSV 文件
     * @param data 二维数组：每行对应 CSV 的一行，每列对应一个字段
     * @param append 是否追加模式（true：追加到文件末尾；false：覆盖文件）
     */
    public static void writeToCsv(String[][] data, boolean append) {
        // 自动关闭流（try-with-resources 语法，无需手动 close）
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(CSV_PATH, append))) {
            // 首次写入时，先写 CSV 表头（若文件为空或覆盖模式）
            if (!append || new java.io.File(CSV_PATH).length() == 0) {
                writer.write("序号,记录时间,操作描述"); // CSV 表头
                writer.newLine(); // 换行
            }

            // 遍历数据，逐行写入 CSV
            for (String[] row : data) {
                // 拼接 CSV 行：用逗号分隔列，若字段含逗号需用双引号包裹（这里简化处理，复杂场景需加判断）
                String csvRow = String.join(",", row);
                writer.write(csvRow);
                writer.newLine(); // 换行（避免所有数据挤在一行）
            }

            System.out.println("数据成功写入 CSV，路径：" + CSV_PATH);

        } catch (IOException e) {
            System.err.println("写入 CSV 失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}