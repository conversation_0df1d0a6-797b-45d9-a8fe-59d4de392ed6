package com.dongni.common.docx.aspose;


import com.dongni.common.utils.EscapeUtils;
import com.dongni.common.utils.ResourceUtils;
import com.dongni.common.utils.VMTool;
import com.dongni.exam.homework.bean.vo.QuestionTypeAIVO;
import com.dongni.exam.mark.bigmodel.bean.LLMChatResult;
import com.dongni.exam.mark.bigmodel.helper.LLMChatHelper;
import com.dongni.tiku.common.util.MapUtil;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.FileWriter;
import java.lang.reflect.Modifier;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@RunWith(SpringRunner.class)  // JUnit 4 的 Spring 支持
@SpringBootTest
public class AutoHomeWordAIService {
    @Autowired
    LLMChatHelper llmChatHelper;
    @Autowired
    private WordToHtmlServiceTest wordToHtmlServiceTest;


    private final Logger log = LoggerFactory.getLogger(getClass());

    @Test
    public void test() throws Exception {
        //21.6
        removeWaterMark();
        // 输入和输出目录
        String inputDir = "word";    // Word文档所在目录
        String outputDir = "html";   // HTML输出目录
        String copyOutputDir = "copyhtml";   // HTML输出目录
        // 确保输出目录存在
        Files.createDirectories(Paths.get(outputDir));
        Files.createDirectories(Paths.get(copyOutputDir));

        File[] wordFiles = new File(inputDir).listFiles((dir, name) ->
                name.toLowerCase().endsWith(".docx") || name.toLowerCase().endsWith(".doc"));

        if (wordFiles == null || wordFiles.length == 0) {
            System.out.println("在目录 " + inputDir + " 中未找到Word文档");
            return;
        }

        for (File wordFile : wordFiles) {
            String html = wordToHtmlServiceTest.wordToHtml(wordFile);
            String htmlOutput = questionType(html);
            // 3. 生成目标HTML文件名（基于原Word文件名，替换后缀为.html）
            String fileName = wordFile.getName();
            String htmlFileName = fileName.substring(0, fileName.lastIndexOf(".")) + ".html";
            String outputHtmlFileName = fileName.substring(0, fileName.lastIndexOf(".")) + "Output.html";
            File targetFile = new File(outputDir, htmlFileName);
            File outputTargetFile = new File(outputDir, outputHtmlFileName);
            // 4. 写入HTML内容到本地文件
            try (FileWriter writer = new FileWriter(targetFile)) {
                writer.write(html);
                System.out.println("HTML文件已生成：" + targetFile.getAbsolutePath());
            }catch (Exception e){
                e.printStackTrace();
            }

            // 4. 写入HTML内容到本地文件
            try (FileWriter writer = new FileWriter(outputTargetFile)) {
                writer.write(htmlOutput);
                System.out.println("HTML文件已生成：" + outputTargetFile.getAbsolutePath());
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        System.out.println(123);
    }

    public String questionType(String html){
        String questionPromptTpl = getReadFileToString(getDirPath() + "prompt_bot_question.vm");
        Map<String, Object> descMap = new HashMap<>();
        descMap.put("desc",html);
        // 生成请求体并校验JSON合法性
        String prompt = VMTool.parse(questionPromptTpl, descMap);
        String requestDataTpl = getReadFileToString(getDirPath() + "request_bot_native.vm");
        Map<String, Object> requestDataMap = new HashMap<>();
        requestDataMap.put("content", EscapeUtils.escape(prompt));
        String requestData = VMTool.parse(requestDataTpl, requestDataMap);

//        if (!JSONUtil.isValid(requestData)) {
//            System.err.println("无效的requestData：" + requestData);
//            return;
//        }
        String outPutHtml = "";
        // 调用方法并获取结果（添加超时）
        Mono<LLMChatResult> monoResult = llmChatHelper.chatResponseMemo(requestData);
        String content = "";
        QuestionTypeAIVO questionTypeAIVO = new QuestionTypeAIVO();
        try {
            // 阻塞获取结果，超时120秒（覆盖请求超时+重试）
            LLMChatResult result = monoResult.block(Duration.ofSeconds(500));
            if (result != null) {
                content = result.getContent();
                // 使用正则匹配提取标签内容
                questionTypeAIVO = new QuestionTypeAIVO();

                List<QuestionTypeAIVO> questionTypeAIVOS = parseToQuestionList(content);
                // if (sameQuestionType.contains("否")) {
                //  questionTypeAIVO.setMessage("检测到可能不是同一个题型");
                //throw new CommonException(ResponseStatusEnum.DATA_ERROR, "检测到不是同一个题型");
                //  }

                List<QuestionTypeAIVO> paragraphs = new ArrayList<>();
                String regex = "<(p|table)[^>]*>(.*?)</\\1>";

                Pattern pattern = Pattern.compile(regex, Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(html);

                while (matcher.find()) {
                    QuestionTypeAIVO questionSeparate = new QuestionTypeAIVO();
                    questionSeparate.setContent(matcher.group(0));
                    //System.out.println("分割内容: " + questionSeparate.content);
                    paragraphs.add(questionSeparate);
                }


                for (QuestionTypeAIVO questionSeparate : paragraphs) {

                    Document doc = Jsoup.parse(questionSeparate.getContent());
                    String text = doc.text();
                    questionSeparate.setText(text);
                    if (!questionSeparate.getContent().startsWith("<p")){
                        continue;
                    }
                    Pattern patternNumber = Pattern.compile("^\\d+");  // 匹配开头的1个或多个数字
                    Matcher matcherNumber = patternNumber.matcher(text);
                    if (matcherNumber.find()) {
                        String leadingDigits = matcherNumber.group();  // 提取匹配的部分
                        if(Integer.parseInt(leadingDigits) < 300){
                            questionSeparate.setNumber(Integer.parseInt(leadingDigits));
                        }
                        //System.out.println("开头的数字: " + leadingDigits);  // 输出: 123
                    } else {
                        //System.out.println("不以数字开头");
                    }
                }

                for (QuestionTypeAIVO paragraph : paragraphs) {
                    Integer number = paragraph.getNumber();
                    for (QuestionTypeAIVO map : questionTypeAIVOS) {
                        if(number == null){
                            break;
                        }
                        //System.out.println(map.getQuestionNumber());
                        Integer questionNumber = map.getQuestionNumberInt();
                        if(number.equals(questionNumber)){
                            paragraph.setOptionCount(MapUtil.getInt(map.getOptionCount(),0));
                            paragraph.setScore(MapUtil.getDouble(map.getScore(),0.0));
                            paragraph.setQuestionTypeStr(MapUtil.getString(map.getQuestionTypeStr(),null));
                            // paragraph.setAnswer(MapUtil.getString(map.get("answer"),null));
                            //   paragraph.setAnalysis(MapUtil.getString(map.get("analysis"),null));
                        }
                    }
                }
                outPutHtml +="<html>\n<body>\n" ;
                for (int i = 0; i < paragraphs.size(); i++) {
                    outPutHtml += "<!-- Paragraph " + (i + 1) + " -->\n";
                    if(paragraphs.get(i).getNumber() != null) {
                        outPutHtml += "<!-- QuestionNumber " + paragraphs.get(i).getNumber() + " -->\n";
                        outPutHtml +="<p>----------------------------------------------------------------------------</p>";
                        outPutHtml += ("<p>检测题目为:" + paragraphs.get(i).getNumber() +
                                "  检测类型为:" + paragraphs.get(i).getQuestionTypeStr() +
                                "  检测选项个数为:" + paragraphs.get(i).getOptionCount() +
                                "  检测分数为:" + paragraphs.get(i).getScore() +
                                "</p>\n");
                    }
                    outPutHtml += paragraphs.get(i).getContent() + "\n\n";
                }
                outPutHtml += "</body>\n</html>";

            }
        } catch (Exception e){
            log.error("获取结果失败：" + e.getMessage());
            e.printStackTrace();
        }
        System.out.println(outPutHtml);
        return outPutHtml;
    }

    private static String extractTag(String content, String tagName) {
        Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">");
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(1).trim() : "";
    }

    private String getReadFileToString(String resourcePath) {
        return ResourceUtils.readResourceFile2String(resourcePath);
    }


    String getDirPath() {
        return "/llm/word/blankfill/";
    }


    public static List<QuestionTypeAIVO> parseToQuestionList(String input) {
        List<QuestionTypeAIVO> questionList = new ArrayList<>();

        // 1. 按题号分隔符拆分每个题的文本块（匹配<题号>X</题号>开头的块）
        Pattern questionPattern = Pattern.compile("<题号>(\\d+)</题号>.*?(?=<题号>|$)", Pattern.DOTALL);
        Matcher questionMatcher = questionPattern.matcher(input);

        while (questionMatcher.find()) {
            String questionBlock = questionMatcher.group(); // 单个题的完整文本块
            QuestionTypeAIVO question = new QuestionTypeAIVO();

            // 2. 提取题号（已在上面的group(1)中获取）
            int questionNumber = Integer.parseInt(questionMatcher.group(1));
            question.setQuestionNumberInt(questionNumber);

            // 3. 提取题型
            Pattern typePattern = Pattern.compile("<题型>(.*?)</题型>");
            Matcher typeMatcher = typePattern.matcher(questionBlock);
            if (typeMatcher.find()) {
                question.setQuestionTypeStr(typeMatcher.group(1));
            }

            // 4. 提取分值
            Pattern scorePattern = Pattern.compile("<分值>(\\d+).*?</分值>");
            Matcher scoreMatcher = scorePattern.matcher(questionBlock);
            if (scoreMatcher.find()) {
                question.setScore(Double.parseDouble(scoreMatcher.group(1)));
            }


            // 5. 提取选项个数
            Pattern optionCountPattern = Pattern.compile("<选项个数>(\\d+)</选项个数>");
            Matcher optionCountMatcher = optionCountPattern.matcher(questionBlock);
            if (optionCountMatcher.find()) {
                question.setOptionCount(Integer.parseInt(optionCountMatcher.group(1)));
            }

            // 6. 将解析后的对象添加到列表
            questionList.add(question);
        }

        return questionList;
    }


    /**
     * 去除水印
     * 使用反射替换变量
     */
    public static void removeWaterMark() throws Exception {
        Class<?> aClass = Class.forName("com.aspose.words.zzXyu");
        java.lang.reflect.Field zzZXG = aClass.getDeclaredField("zzZXG");
        zzZXG.setAccessible(true);
        java.lang.reflect.Field modifiersField = zzZXG.getClass().getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(zzZXG, zzZXG.getModifiers() & ~Modifier.FINAL);
        zzZXG.set(null,new byte[]{76, 73, 67, 69, 78, 83, 69, 68});
    }
}
