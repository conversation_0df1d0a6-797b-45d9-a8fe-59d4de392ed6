package com.dongni.common.http;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR> <br>
 * 2022/01/18 <br>
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RestTemplateTest {
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Test
    public void test() {
        request(2000);
        request(5000);
        request(70000);
        request(120000, 0);
        request(120000, -1);
        request(15000, 5000);
    }
    
    private void request(int sleep) {
        request(sleep, null, null);
    }
    
    private void request(int sleep, Integer readTimeout) {
        request(sleep, readTimeout, null);
    }
    
    private void request(int sleep, Integer readTimeout, Integer connectTimeout) {
        System.out.println(" ------------------------------------- ");
        System.out.println(sleep);
        System.out.println(readTimeout);
        System.out.println(connectTimeout);
        long start = System.currentTimeMillis();
        String url = "http://localhost:8080/test?sleep=" + sleep;
        String result = "";
        
        try {
            if (readTimeout == null && connectTimeout == null) {
                result = restTemplate.getForObject(url, String.class);
            } else {
                result = HttpTimeout.execute(
                        HttpTimeout.of(readTimeout, connectTimeout),
                        () -> restTemplate.getForObject(url, String.class)
                );
            }
        } catch (Exception e) {
            System.out.println("异常了: " + e.getClass().getTypeName() + ": " + e.getMessage());
        }
        System.out.println("result: " + result);
        long end = System.currentTimeMillis();
        System.out.println("cost: " + (end - start));
        
    }
}
