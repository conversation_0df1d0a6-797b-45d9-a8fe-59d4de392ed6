package com.dongni.common.verify.image;

import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.InputStream;
import java.util.Map;
import java.util.Random;

/**
 * Create by sapluk <br/>
 * time 10:09 2019/06/11 <br/>
 * description: <br/>
 *
 */
public class VerifyImageUtilTest {

    @Test
    public void test() throws Exception {

        Random random = new Random();
        int sourceNo = random.nextInt(20) + 1;

        InputStream sourceInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/sources/" + sourceNo + ".png");
        InputStream templateInnerInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/templates/puzzle-inner.png");
        InputStream templateBorderInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/templates/puzzle-border.png");

        Map<String, Object> verifyImageCutMap = VerifyImageUtil.verifyImageCut(sourceInputStream, templateInnerInputStream, templateBorderInputStream, true);

        BufferedImage backgroundImage = (BufferedImage) verifyImageCutMap.get("backgroundImage");
        BufferedImage activeImage = (BufferedImage) verifyImageCutMap.get("activeImage");

        ImageIO.write(backgroundImage, "png", new File("/home/<USER>/test/backgroundImage.png"));
        ImageIO.write(activeImage, "png", new File("/home/<USER>/test/activeImage.png"));

        String backgroundImageBase64 = VerifyImageUtil.getBase64(backgroundImage, "png");
        String activeImageBase64 = VerifyImageUtil.getBase64(activeImage, "png");
    }

    /**
     * 基准
     *    1  10000   403.814    40.4ms
     * @throws Exception
     */
    @Test
    public void test1() throws Exception {

        long start = System.currentTimeMillis();

        int i = 0;
        for (; i < 10000; i++) {
            Random random = new Random();
            int sourceNo = random.nextInt(20) + 1;

            InputStream sourceInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/sources/" + sourceNo + ".png");
            InputStream templateInnerInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/templates/puzzle-inner.png");
            InputStream templateBorderInputStream = getClass().getClassLoader().getResourceAsStream("static/verify/image/templates/puzzle-border.png");

            Map<String, Object> verifyImageCutMap = VerifyImageUtil.verifyImageCut(sourceInputStream, templateInnerInputStream, templateBorderInputStream, true);

            BufferedImage backgroundImage = (BufferedImage) verifyImageCutMap.get("backgroundImage");
            BufferedImage activeImage = (BufferedImage) verifyImageCutMap.get("activeImage");

//            ImageIO.write(backgroundImage, "png", new File("/home/<USER>/test/ooo/" + i + "backgroundImage.png"));
//            ImageIO.write(activeImage, "png", new File("/home/<USER>/test/ooo/" + i + "activeImage.png"));

            String backgroundImageBase64 = VerifyImageUtil.getBase64(backgroundImage, "png");
            String activeImageBase64 = VerifyImageUtil.getBase64(activeImage, "png");

            System.out.println(i + " : "
                    + verifyImageCutMap.get("offsetX") + " | "
                    + verifyImageCutMap.get("offsetY") + " | "
                    + backgroundImage.getWidth() + " - " + backgroundImage.getHeight() + " - " + backgroundImageBase64.length() + " | "
                    + activeImage.getWidth() + " - " + activeImage.getHeight() + " - " + activeImageBase64.length()
            );
        }
        long end = System.currentTimeMillis();
        System.out.println(((end - start) / 1000.0)+ " - " + ((end - start) / 1000.0 / i));
    }
}
