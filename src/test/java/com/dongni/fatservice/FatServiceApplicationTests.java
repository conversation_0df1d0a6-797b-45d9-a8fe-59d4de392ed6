package com.dongni.fatservice;

import com.dongni.analysis.bean.AnalysisMongodb;
import com.dongni.analysis.view.monitor.school.strategy.impl.MonitorSchoolStudentImpl;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

//@RunWith(SpringRunner.class)
//@SpringBootTest
public class FatServiceApplicationTests {


    @Autowired
    private MonitorSchoolStudentImpl monitorSchoolStudent;

    @Autowired
    private AnalysisMongodb analysisMongodb;

    @Test
    public void test() {

        String a = "ab";
        String[] split = a.split("");
        System.out.println();

    }

}
