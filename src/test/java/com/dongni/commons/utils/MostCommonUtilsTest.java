package com.dongni.commons.utils;

import com.dongni.basedata.system.account.util.RSAUtil;
import com.pugwoo.wooutils.string.Base64;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * <AUTHOR> <br/>
 * @date 2019/10/09 <br/>
 *
 */
public class MostCommonUtilsTest {
    
    private static final Logger log = LoggerFactory.getLogger(MostCommonUtilsTest.class);
    
    /**
     * md5加密
     */
    @Test
    public void md5() {
        String origin = "123456";
        System.out.println(MD5Util.md5(origin));
        System.out.println(MD5Util.md5BySalt(origin));
    }
    
    /**
     * 可适用于懂你开放平台验证信息的编码解码
     *
     * 登录 Basic {{base64(clientId:clientSecret)}}
     */
    @Test
    public void base64ForAuth() {
        
        String clientId = "teewon";
        String clientSecret = "123456";
        
        String authBasic = Base64.encode(clientId + ":" + clientSecret);
        System.out.println(authBasic);
        System.out.println(Base64.decode(authBasic, "utf8"));
    }
    
    /**
     * id编码校验 适用于对第三方id参数进行处理校验
     */
    @Test
    public void idCheck() {
        String fieldName = "schoolId";
        long origin = 500;
        long encrypt = 48807983;
        
        long encryptOrigin = IdCheckUtil.encrypt(fieldName, origin);
        System.out.println(IdCheckUtil.decrypt(fieldName, encryptOrigin) + " --> " + encryptOrigin);
        System.out.println(encrypt + " --> " + IdCheckUtil.decrypt(fieldName, encrypt));
    }
    
    /**
     * seaweedfs filemeta 用于计算dirhash
     */
    @Test
    public void seaweedfsDirHash() {
        // seaweedfs.filemeta.directory
        String directory = "/upload/answerCard/2019/4/25/532/540/-1259613821_DN0425000863";
        // seaweedfs.filemeta.dirhash
        long dirhash = HashUtil.hashLongFirst64Bits(directory);
        System.out.println(dirhash);
    }
    
    @Test
    public void testRsa() {
        System.out.println(RSAUtil.generateKeyPair(1024));
        
        System.out.println("公钥加密——私钥解密");
        String source = "高可用架构对于互联网服务基本是标配。";
        System.out.println("\r加密前文字：\r\n" + source);
        String aData = RSAUtil.encrypt(source);
        System.out.println("加密后文字：\r\n" + aData);
        String dData = RSAUtil.decrypt(aData);
        System.out.println("解密后文字: \r\n" + dData);
        
        String aa = "mjMpReWokq8eYi8MuSj5EGZNChw46RsCJ+tS1Mrr9JwvthY2y3wmljwA32krsMmxImeJ+HE2mYiGK9yL1VmtM22F6gnvHm1dlYWhaQLoRl8lARR/6zM0A2qtuOr13yvR2op+VLbIdMORUR8bcRe+GyY1x+7Kdwyp5kgOv05jrhY=";
        System.out.println("解密后文字111: \r\n" + RSAUtil.decrypt(aa));
        
        AtomicLong index = new AtomicLong();
        
        Stream.generate(index::incrementAndGet).limit(1000).parallel().forEach(i -> {
            try {
                String data = "123456";
                String encrypt = RSAUtil.encrypt(data);
                String decrypt = RSAUtil.decrypt(encrypt);
            } catch (Exception e) {
                log.error("error: {}", i, e);
            }
        });
    }
    
    /**
     * https://scm.nicezhuanye.com/confluence/pages/viewpage.action?pageId=58830836
     * 懂你脱敏数据配置
     * 密码: 随机生成一个密码，如 abcdefg
     * 并获取其 md5bySalt 值，如md5bySalt("abcdefg") = "809FDB7E29C97168380A4281D653BC4D"
     * 再进行Base64编码，得到 "ODA5RkRCN0UyOUM5NzE2ODM4MEE0MjgxRDY1M0JDNEQ="
     * 在对应环境的properties设置
     * dongni.sensitive.info.secret.key=ODA5RkRCN0UyOUM5NzE2ODM4MEE0MjgxRDY1M0JDNEQ=
     */
    @Test
    public void genSensitiveSecretKey() throws NoSuchFieldException, IllegalAccessException {
        // 随机设置一个密码
        String password = "DongNi2024@TencentQCloud";
        System.out.println("密码: " + password);
        
        // 将密码进行md5BySalt
        String passwordMd5BySalt = MD5Util.md5BySalt(password);
        // Base64编码
        String sensitiveSecretKey = java.util.Base64.getEncoder().encodeToString(passwordMd5BySalt.getBytes(StandardCharsets.UTF_8));
        
        System.out.println("秘钥: " + sensitiveSecretKey);
        
        Class<SensitiveInfoUtil> sensitiveInfoUtilClass = SensitiveInfoUtil.class;
        Field secretKeyField = sensitiveInfoUtilClass.getDeclaredField("SECRET_KEY");
        secretKeyField.setAccessible(true);
        secretKeyField.set(null, sensitiveSecretKey);
        
        String plainText = "13800138000";
        System.out.println("原文: " + plainText);
        String encryptText = SensitiveInfoUtil.aesEncrypt(plainText);
        System.out.println("加密: " + encryptText);
        String decryptText = SensitiveInfoUtil.aesDecrypt(encryptText);
        System.out.println("解密: " + decryptText);
    }
}
