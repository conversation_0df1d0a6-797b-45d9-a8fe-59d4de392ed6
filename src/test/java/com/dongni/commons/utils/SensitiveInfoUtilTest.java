package com.dongni.commons.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Create by sapluk <br/>
 * time 11:27 2019/07/09 <br/>
 * description: <br/>
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SensitiveInfoUtilTest {

    @Test
    public void test() {

        aesDecrypt("yFVE28OwowirLffUFmZfGw==");
        aesDecrypt("hHvLqQG2WUTmvaFWMvNeyA==");
        aesDecrypt("cGSfa+lDqZed5CXl1twByg==");
        aesDecrypt("WYQc9RU+PluIl8HkJE6EzQ==");
        aesDecrypt("NWl8LwybifX5Gtw8D8Sx/w==");
        aesDecrypt("hwB//SHJGQ5ZQoL0B9lNiA==");
        aesDecrypt("4pipYVVe4Ajr8utb5d1FBA==");
        aesDecrypt("hHvLqQG2WUTmvaFWMvNeyA==");

        aesEncrypt("3002");
        aesEncrypt("3660");
    }

    private void aesDecrypt(String aesString) {
        String srcString;
        try {
            srcString = SensitiveInfoUtil.aesDecrypt(aesString);
        } catch (Exception e) {
            srcString = "解密失败";
        }

        System.out.println("解密: " + aesString + " : " + srcString);
    }

    private void aesEncrypt(String srcString) {
        String aesString;
        try {
            aesString = SensitiveInfoUtil.aesEncrypt(srcString);
        } catch (Exception e) {
            aesString = "加密失败";
        }
        System.out.println("加密: " + srcString + " : " + aesString);
    }
}
