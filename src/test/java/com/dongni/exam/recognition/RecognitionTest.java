package com.dongni.exam.recognition;

import com.dongni.exam.recognition.bean.RecogQnsAndStusBO;
import com.dongni.exam.recognition.service.NewRecognitionService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2025/04/08
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FatServiceApplication.class)
public class RecognitionTest {

    @Autowired
    private NewRecognitionService newRecognitionService;

    @Test
    public void getRecognitionInfo() {
        RecogQnsAndStusBO recogQnsAndStusBO = newRecognitionService.getRecognitionQnsAndStus(120071L,1);
        System.out.println(recogQnsAndStusBO.getQns());
    }
}
