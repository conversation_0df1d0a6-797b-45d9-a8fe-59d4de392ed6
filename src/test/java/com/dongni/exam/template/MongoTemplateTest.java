package com.dongni.exam.template;


import com.dongni.FatServiceApplication;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.tiku.bean.TikuMongodb;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Projections.*;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FatServiceApplication.class)
public class MongoTemplateTest {
    @Autowired
    TikuMongodb tikuMongodb;

    @Autowired
    private ExamRepository examRepository;
    @Test
    public void newInsertDoc() {
        Long sourceExamUploaderId = 4453882L;
        Long sourceTemplateCode = 13775L;
        Long newExamUploaderId = -1L;
        Long newTemplateCode = 13777L;
		Long courseId = 3L;

        List<Long> examIds = Arrays.asList(
                1078049452L,
                1749168097L,
                1749138094L,
                541178529L,
                541178530L,
                1480702639L,
                943831715L,
                1883385818L,
                138525360L,
                1480702641L,
                1480702628L,
                1346484914L,
                4337619L,
                1883355813L,
                1078107419L,
                406960820L,
                1614920373L,
                1480702646L,
                943861732L,
                1078079444L,
                943861718L,
                1883385822L
        );

        for(Long newExamId: examIds) {
            Map<String, Object> params = new HashMap<>();
            params.put("examId", newExamId);
			params.put("courseId", courseId);
            List<Map<String, Object>> list = examRepository.selectList("ExamMaintainMapper.getExamPaperInfo", params);
            if(CollectionUtils.isNotEmpty(list)) {
                Long newPaperId = Long.valueOf(list.get(0).get("paperId").toString());
                newExamUploaderId = -1L;
                copyAndInsertTemplate(sourceTemplateCode, sourceExamUploaderId, newExamId, newExamUploaderId,
                        newPaperId, newTemplateCode);
                newTemplateCode ++;
            }
        }
    }


    /**
     * 复制并修改文档后插入新记录
     * @param sourceTemplateCode 原模板代码（查询条件）
     * @param sourceExamUploaderId 原上传者ID（查询条件）
     * @param newExamId 新考试ID（插入字段）
     * @param newPaperId 新试卷ID（插入字段）
     * @param newTemplateCode 新模板代码（插入字段）
     * @return InsertOneResult 插入操作结果
     */
    public void copyAndInsertTemplate(
            long sourceTemplateCode,
            long sourceExamUploaderId,
            long newExamId,
            long newExamUploaderId,
            long newPaperId,
            long newTemplateCode) {

        // 1. 构建查询条件
        Bson query = and(
                eq("templateCode", sourceTemplateCode),
                eq("examUploaderId", sourceExamUploaderId)
        );

        // 2. 获取集合引用
        MongoCollection<Document> collection =
                tikuMongodb.getMongoDatabase().getCollection("answerCardTemplateManual");

        // 3. 查询原文档
        Document originDoc = collection.find(query).first();

        if (originDoc == null) {
            throw new RuntimeException("未找到匹配的原始文档");
        }

        // 4. 复制并修改文档（深拷贝避免污染原对象）
        Document newDoc = new Document(originDoc);

        // 删除_id字段（确保插入新记录）
        newDoc.remove("_id");

        // 修改字段
        newDoc.put("examId", newExamId);
        newDoc.put("examUploaderId", newExamUploaderId); // 保持原值
        newDoc.put("paperId", newPaperId);
        newDoc.put("templateCode", newTemplateCode);

        // 5. 插入新文档
        collection.insertOne(newDoc);
    }
}
