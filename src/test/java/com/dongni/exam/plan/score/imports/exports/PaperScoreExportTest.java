package com.dongni.exam.plan.score.imports.exports;

import com.dongni.common.report.excel.ExportExcel;
import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/01 <br/>
 *
 */
public class PaperScoreExportTest {

    @Test
    public void exportToLocalPath() {
        List<Map<String, Object>> examScoreInfList = new ArrayList<>();
        examScoreInfList.add(MapUtil.of(
                "courseName", "刀塔",
                "fullMark", "300",
                "subList", Stream.of(
                        MapUtil.of("courseName", "补刀", "fullMark", "100",
                                "subList", Stream.of(
                                        MapUtil.of("courseName", "正补", "fullMark", "60"),
                                        MapUtil.of("courseName", "反补", "fullMark", "40")
                                ).collect(toList())),
                        MapUtil.of("courseName", "推塔", "fullMark", "100"),
                        MapUtil.of("courseName", "抢人头", "fullMark", "100")
                )
                        .collect(toList())
                )
        );
        examScoreInfList.add(MapUtil.of("courseName", "英雄联盟", "fullMark", "100"));
        examScoreInfList.add(MapUtil.of("courseName", "吃鸡", "fullMark", "100"));
    
        ExportExcel report = new PaperScoreExport("demo", examScoreInfList);
        System.out.println(report.exportToLocalPath("/home/<USER>/test/发布考试导入模式/总分模式" + System.currentTimeMillis()));
    }
}
