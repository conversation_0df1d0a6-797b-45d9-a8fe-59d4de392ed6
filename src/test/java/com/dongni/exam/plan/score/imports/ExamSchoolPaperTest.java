package com.dongni.exam.plan.score.imports;

import com.dongni.FatServiceApplication;
import com.dongni.exam.plan.bean.bo.ExamSchoolPaperBO;
import com.dongni.exam.plan.service.ExamSchoolPaperService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/7/11 14:58
 * @description:
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FatServiceApplication.class)
public class ExamSchoolPaperTest {

    @Autowired
    private ExamSchoolPaperService examSchoolPaperService;

    @Test
    public void getExamSchoolPaper() {
        ExamSchoolPaperBO bo = new ExamSchoolPaperBO();
        bo.setExamId(420302).setPaperId(1612109976799737L).setSchoolId(12);
        bo = examSchoolPaperService.getExamSchoolPaperBO(bo);
        System.out.println(bo.getExamSchoolPaperStatus());
    }
}
