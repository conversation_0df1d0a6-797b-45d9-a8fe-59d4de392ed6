package com.dongni.exam.plan.score.imports.exports;

import com.dongni.common.report.excel.ExportExcel;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/01 <br/>
 *
 */
public class QuestionScoreExportTest {

    @Test
    public void exportToLocal() {
        Map<String, Object> map = new HashMap<>();
        map.put("courseName", "语文");
        map.put("fullMark", "150");
        
        ExportExcel report = new QuestionScoreExport("demo", map);
        System.out.println(report.exportToLocalPath("/home/<USER>/test/发布考试导入模式/小题分模式" + System.currentTimeMillis()));
    }
}
