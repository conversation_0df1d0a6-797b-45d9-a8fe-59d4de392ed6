package com.dongni.exam.plan.score.imports.service;

import com.dongni.common.report.excel.ExportExcelTemplate;
import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <br/>
 * @date 2021/04/07 <br/>
 *
 */
public class ScoreImportExamTemplateServiceTest {
    
    @Test
    public void exportPaperScoreTemplate() {
        String examName = "某某考试-总分导入模式";
        List<Map<String, Object>> courseList = new ArrayList<>();
        courseList.add(MapUtil.of("courseName", "语文", "paperName", "语文1", "fullMark", "1"));
        courseList.add(MapUtil.of("courseName", "语文", "paperName", "语文2", "fullMark", "2"));
        courseList.add(MapUtil.of("courseName", "数学", "paperName", "数学", "fullMark", "3"));
        courseList.add(MapUtil.of("courseName", "理综", "paperName", "理综", "fullMark", "4"));
        courseList.add(MapUtil.of("courseName", "物理", "paperName", "物理", "fullMark", "5"));
        
        Map<String, Object> data = new HashMap<>();
        data.put("courseList", courseList);
        new ExportExcelTemplate("score-import/byPaper.xls", data)
                .exportToLocalPath(examName, "/home/<USER>/text/jett/" + System.currentTimeMillis(), "");
        
    }
    
    @Test
    public void exportQuestionScoreTemplate() {
        String examName = "某某考试-小题分导入模式";
        Map<String, Object> data = new HashMap<>();
        data.put("courseName", "数学");
        data.put("paperName", "文数");
        data.put("fullMark", "123");
        new ExportExcelTemplate("score-import/byQuestion.xls", data)
                .exportToLocalPath(examName, "/home/<USER>/text/jett/" + System.currentTimeMillis(), "");
    }
}
