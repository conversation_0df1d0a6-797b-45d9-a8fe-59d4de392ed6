package com.dongni.exam.plan.score.imports.service;

import com.dongni.FatServiceApplication;
import com.dongni.exam.schedule.ExamResultStatusUpgradeSchedule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FatServiceApplication.class)
public class NewExamResultServiceTest {

    @Autowired
    private ExamResultStatusUpgradeSchedule examResultStatusUpgradeSchedule;

    @Test
    public void upgradeExamResultStatus() {
        examResultStatusUpgradeSchedule.upgradeExamResultStatus();
    }

    @Test
    public void isAfter5AM() {
        boolean v = examResultStatusUpgradeSchedule.isBefore(15);
        System.out.println("value v " + String.valueOf(v));
    }
}
