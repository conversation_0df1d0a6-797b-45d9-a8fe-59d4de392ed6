package com.dongni.exam.mark.read.intelligent;

import com.dongni.common.report.excel.ExportExcelTemplate;
import com.dongni.tiku.common.util.MapUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <br>
 * 2021/11/10 <br>
 *
 */
public class ExamMarkReadIntelligentTest {
    
    @Test
    public void exportExamMarkReadIntelligent() {
        
        String exportName = "智能分配-某某考试-某某试卷";
        
        List<Map<String, Object>> teacherList = new ArrayList<>();
        teacherList.add(MapUtil.of("schoolId", 1, "schoolName", "一中", "courseId", 2,"courseName", "语文", "teacherName", "张三1","teacherPhone", "1234567891", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 1, "schoolName", "一中", "courseId", 2,"courseName", "语文", "teacherName", "张三2","teacherPhone", "1234567892", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 1, "schoolName", "一中", "courseId", 3,"courseName", "数学", "teacherName", "张三3","teacherPhone", "1234567893", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 1, "schoolName", "一中", "courseId", 3,"courseName", "数学", "teacherName", "张三4","teacherPhone", "1234567894", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 2, "schoolName", "二中", "courseId", 2,"courseName", "语文", "teacherName", "张三5","teacherPhone", "1234567895", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 2, "schoolName", "二中", "courseId", 2,"courseName", "语文", "teacherName", "张三6","teacherPhone", "1234567896", "workload", 700));
        teacherList.add(MapUtil.of("schoolId", 2, "schoolName", "二中", "courseId", 3,"courseName", "数学", "teacherName", "张三7","teacherPhone", "1234567897", "workload", 700));
        teacherList.sort(Comparator
                .<Map<String, Object>, Long>comparing(o -> MapUtil.getLong(o, "courseId"))
                .thenComparing(o -> MapUtil.getLong(o, "schoolId"))
        );
        
        int studentCount = 123456;
        Map<Long, Double> readBlockId2workload = new HashMap<>();
        readBlockId2workload.put(101L, 0.1);
        readBlockId2workload.put(201L, 0.1);
        readBlockId2workload.put(301L, 0.1);
        readBlockId2workload.put(401L, 1.1);
        readBlockId2workload.put(501L, 2.0);
        readBlockId2workload.put(601L, 3.0);
        readBlockId2workload.put(701L, 4.0);
        
        List<Map<String, Object>> readBlockList = new ArrayList<>();
        readBlockList.add(MapUtil.of("studentCount", 123, "readBlockId", 101, "readBlockName", "1", "courseId", 2, "courseName", "语文"));
        readBlockList.add(MapUtil.of("studentCount", 123, "readBlockId", 201, "readBlockName", "2", "courseId", 2, "courseName", "语文"));
        readBlockList.add(MapUtil.of("studentCount", 456, "readBlockId", 301, "readBlockName", "3,4", "courseId", 3, "courseName", "数学"));
        readBlockList.add(MapUtil.of("studentCount", 456, "readBlockId", 401, "readBlockName", "5", "courseId", 3, "courseName", "数学"));
        readBlockList.add(MapUtil.of("studentCount", 123, "readBlockId", 501, "readBlockName", "6,7", "courseId", 2, "courseName", "语文"));
        readBlockList.add(MapUtil.of("studentCount", 456, "readBlockId", 601, "readBlockName", "8", "courseId", 3, "courseName", "数学"));
        readBlockList.add(MapUtil.of("studentCount", 123, "readBlockId", 701, "readBlockName", "9", "courseId", 2, "courseName", "语文"));
        
        readBlockList.sort(Comparator
                .<Map<String, Object>, Long>comparing(o -> MapUtil.getLong(o, "courseId"))
                .thenComparing(o -> MapUtil.getLong(o, "readBlockId"))
        );
        
        for (Map<String, Object> readBlock : readBlockList) {
            long readBlockId = MapUtil.getLong(readBlock, "readBlockId");
            double questionWorkload = readBlockId2workload.get(readBlockId);
            readBlock.put("readBlockWorkload", questionWorkload);
            readBlock.put("readBlockDisplay", readBlockId + "-" + MapUtil.getString(readBlock, "readBlockName"));
        }
        
        Map<String, Object> data = new HashMap<>();
        data.put("teacherList", teacherList);
        data.put("readBlockList", readBlockList);
        new ExportExcelTemplate("read-intelligent/examMarkIntelligent.xls", data)
                .exportToLocalPath(exportName, "/home/<USER>/test/jett/" + System.currentTimeMillis(), null);
        
    }
}
