package com.dongni.exam.mark.ai;

import com.dongni.FatServiceApplication;
import com.dongni.exam.mark.ai.composition.entity.AICompositionItem;
import com.dongni.exam.mark.ai.composition.service.AICompositionSchedule;
import com.dongni.exam.mark.ai.composition.service.impl.AICompositionServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/9 17:34
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FatServiceApplication.class)
public class AICompositionTest {

    @Autowired
    private AICompositionServiceImpl aiCompositionService;

    @Test
    public void insertComposition() {
        AICompositionItem item = new AICompositionItem();
        item.setPaperReadId(1729382256911055251L);
        item.setExamItemId(246682031);
        item.setRequestDateTime(new Date());
        item.setCreateDateTime(new Date());
        item.setGrade(8);
        item.setEngType(0);
        item.setUrl("upload/answerCard/2024/9/25/571120/587817/572898_DN0605000018/cardContent-4501-1727248034813.jpg");
        List<AICompositionItem> itemList = new ArrayList<>();
        itemList.add(item);
        aiCompositionService.handleAIComposition(itemList);
    }

    @Autowired
    private AICompositionSchedule aiCompositionSchedule;
    @Test
    public void getCompositionResult() {
        aiCompositionSchedule.doParse(false);
    }
}
