package com.dongni.exam.mark.ai;

import com.dongni.FatServiceApplication;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.common.utils.FileUtil;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageBatchGet;
import com.dongni.exam.mark.ai.apis.APIConst;
import com.dongni.exam.mark.ai.apis.OfficeBaiDu;
import com.dongni.exam.mark.ai.bean.ExamItem;
import com.dongni.exam.mark.ai.bean.vo.EICVO;
import com.dongni.exam.mark.ai.service.AIMarkCollectService;
import com.dongni.exam.mark.ai.utils.OCRAPIUtil;
import com.dongni.exam.newcard.bean.DTO.CoursePaperReadDTO;
import com.dongni.exam.newcard.bean.IntelligenceItem;
import com.dongni.exam.newcard.service.NewExamItemService;
import com.dongni.exam.plan.dao.NewExamResultDao;
import com.dongni.exam.plan.service.ExamItemService;
import com.dongni.exam.plan.service.NewExamResultService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR>
 * @date 2024/6/11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FatServiceApplication.class)
public class AIMarkTest {

    private static final Logger logger = LoggerFactory.getLogger(AIMarkTest.class);
    /**
     * 测试
     */

    @Autowired
    private AIMark aiMark;

    @Autowired
    private ExamItemService examItemService;

    @Test
    public void AIMark() {
        long now = System.currentTimeMillis();
        List<ExamItem> items = getExamItem(5476377147373373674L);
        if (items.size() < 1) {
            return;
        }
        aiMark.setOCR_API_NAME(APIConst.OCR_API_NAME_BAIDU);
        aiMark.setUseOfficeAPI(false);
        aiMark.aiTest(items);
        System.out.println(System.currentTimeMillis() - now);
    }


    @Test
    public void checkAnswer() throws IOException {
        List<Long> ids = Arrays.asList(
//                5764607523524357567L,
//                5764607523524357566L,
//                5764607523524357565L,
//                5764607523524357564L,
//                5764607523524357563L,
//                5764607523524357562L,
//                5764607523524357561L,
//                5764607523524357560L,
//                5764607523524357559L,
//                5764607523524357558L,
//                5764607523524357557L,
//                5764607523524357556L,
//                5764607523524357555L,
//                5764607523524357554L,
//                5764607523524357553L,
//                5764607523524357552L,
//                5764607523524357551L,
//                5764607523524357550L,
//                5764607523524357549L,
//                5764607523524357548L,
//                5764607523524357547L,
//                5764607523524357546L,
//                5764607523524357545L,
//                5764607523524357544L,
//                5764607523524357543L,
//                6052837899676096482L,
//                6052837899676096492L,
//                6052837899676096480L,
//                6052837899676096484L,
//                6052837899676096493L,
//                6052837899676096487L,
//                6052837899676096488L,
//                6052837899676096481L,
//                6052837899676096483L,
//                6052837899676096491L,
//                6052837899676096485L,
//                4899916395069219737L,
//                4899916395069219738L,
//                4899916395069219740L,
//                4899916395069219741L,
//                4899916395069219742L,
//                4323455642765795664L,
//                4323455642765795668L,
                4323455642765795661L,
                4323455642765795665L,
                4323455642765795669L,
                4323455642765795662L,
                4323455642765795666L,
                4323455642765795660L,
                4323455642765795663L,
                4323455642765795667L,
                8070450532737985613L,
                8070450532737985612L,
                8070450532737985611L,
                8070450532737985610L,
                8070450532737985609L,
                8070450532737985608L,
                8070450532737985607L,
                8070450532737985606L,
                8070450532737985605L,
                8070450532737985604L,
                8070450532737985603L,
                8070450532737985602L,
                8070450532737985601L,
                8070450532737985600L,
                8070450532737985599L,
                8070450532737985598L,
                8070450532737985597L,
                8070450532737985596L,
                8070450532737985595L,
                8070450532737985594L,
                8070450532737985593L,
                8070450532737985592L,
                8070450532737985591L,
                8070450532737985590L,
                2594073385855431079L,
                2594073385855431075L,
                5476377147372578159L,
                5476377147372578163L,
                5476377147372578158L,
                6917529028131233682L,
                6917529028131233681L,
                6917529028131233680L,
                6917529028131233679L,
                6917529028131233678L,
                6917529028131233677L,
                6917529028131233676L,
                6917529028131233675L,
                6917529028131233674L,
                6917529028131233673L,
                8070450532738048691L,
                8070450532738048690L,
                8070450532738048689L,
                8070450532738048688L,
                8070450532738048687L,
                8070450532738048686L,
                8070450532738048685L,
                8070450532738048684L,
                8070450532738048683L,
                8070450532738048682L
                );
        File rootDir = new File("/home/<USER>/Desktop/0312/");
        FileUtils.deleteDirectory(rootDir);
        for(int i = 0; i < ids.size(); i++) {
            long now = System.currentTimeMillis();
            Long id = ids.get(i);
            List<ExamItem> items = getExamItem(id);
            if (items.size() < 1) {
                return;
            }

            BatchDataUtil.execute(items, itemList -> aiMark.checkAnsweredItems(itemList, rootDir.getAbsolutePath()), 15);
            long cost = System.currentTimeMillis() - now;
            int size = items.size();
            System.out.println("paperReadId = " + id + ", cost = " + cost + ", avg = " + (cost / size) + ", size = " + size);
        }

    }
    @Resource
    private NewExamResultDao newExamResultDao;

    @Autowired
    private NewExamResultService newExamResultService;


    private List<ExamItem> getExamItem(long id) {
        Map<String, Object> pa = new HashMap<>();
        pa.put("paperReadId", id);
        List<ExamItem> items = examItemService.getTestExamItems(pa);
        return items;
    }

    @Test
    public void testCombineImages() {
        String path = "/home/<USER>/Desktop/1.jpg";
        List<OCRExamItem> itemList = new ArrayList<>();
        for(int i = 0; i < 10; i++) {
            OCRExamItem it = new OCRExamItem();
            it.setUrl(path);
            it.setItemId(i);
            it.setPaperReadId(1000);
            it.setFile(new File(path));
            it.init();
            itemList.add(it);
        }
        aiMark.combineImages(itemList, false, false);
    }

    /**
     * 抹除题号.
     */
    @Test
    public void eraseQN() throws IOException {
        String root = "/home/<USER>/Desktop/智能批改数据统计/576460752791626302/imgs";
        List<OCRExamItem> items = getFolderFiles(root);
        items = items.size() > 250 ? items.subList(0, 250) : items;
        int x = 0, y = 0, w = 38, h = 78;
        items.forEach(it -> {
            it.setQNLeft(x);
            it.setQNTop(y);
            it.setQNWidth(w);
            it.setQNHeight(h);
        });

        List<CombineImage> newImages = aiMark.combineImages(items, false, true);
        for (int j = 0; j < newImages.size(); j++) {
            ImageIO.write(newImages.get(j).getFile(), "png", new File(root.replace("imgs", "") + "eraseQN" + j + ".png"));
        }
    }


    @Test
    public void eraseQN3() {
        File root = new File("E:\\ocr2\\");
        List<Long> ids = Arrays.asList(864691128944028894L);
        File courseRoot = new File(root.getAbsolutePath() + File.separator + "英语");
        for(Long paperReadId : ids) {
            List<IntelligenceItem> itemList = newExamItemService.getExamItemListByPaperReadId(paperReadId);
            if (itemList.size() == 0) {
                continue;
            }
            File subFile = createAIFolder(courseRoot, paperReadId);
            List<OCRExamItem> ocrExamItemList = itemList.stream().map(it -> {
                OCRExamItem x = new OCRExamItem();
                x.setItemId(it.getExamItemId());
                x.setUrl(it.getUrl());
                x.setRv(it.getRv());
                x.setQNLeft(0);
                x.setQNTop(0);
                x.setQNWidth(161);
                x.setQNHeight(11);
                return x;
            }).collect(Collectors.toList());
            downloadExamItemList2(subFile, ocrExamItemList, paperReadId);
        }
    }

    private void downloadExamItemList2(File subFile, List<OCRExamItem> ocrExamItemList, long paperReadId) {
        FileStorageTemplate.batchGet(ocrExamItemList, (FileStorageBatchGet fileStorageBatchGet) -> {
            ocrExamItemList.forEach(OCRExamItem::init);
            AtomicInteger out = new AtomicInteger(0);
            BatchDataUtil.execute(ocrExamItemList, items -> {
                out.incrementAndGet();
                List<CombineImage> images = aiMark.combineImages(items, false, true);
                for (int i = 0; i < images.size(); i++) {
                    CombineImage image = images.get(i);
                    try {
                        ImageIO.write(image.getFile(), "png", new File(subFile.getAbsoluteFile() + File.separator + paperReadId + "_" + out.get() + "_" + String.valueOf(i + 1) + ".png"));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

            }, 240);

        });
    }

    @Test
    public void eraseQN2() throws IOException {
        String root = "/home/<USER>/Desktop/智能批改数据统计/576460752791626302/imgs";
        List<OCRExamItem> items = getFolderFiles(root);
        items = items.size() > 250 ? items.subList(0, 250) : items;
        List<CombineImage> newImages = aiMark.combineImages(items, false, false);
        for (int j = 0; j < newImages.size(); j++) {
            ImageIO.write(newImages.get(j).getFile(), "png", new File(root.replace("imgs", "")  + "qn_" + j + ".png"));
        }
    }

    private List<OCRExamItem> getFolderFiles(String path) {
        File root = new File(path);
        List<String> images = Arrays.asList(root.list());
        images = images.stream().filter(v -> v.contains("image_")).collect(Collectors.toList());
        images.sort(Comparator.comparing(v -> {
            String index = v.substring(0, v.lastIndexOf(".")).split("_")[1];
            return Integer.valueOf(index);
        }));
        AtomicInteger id = new AtomicInteger(1);
        List<OCRExamItem> items = (List<OCRExamItem>) images.stream().map(x -> {
            OCRExamItem item = new OCRExamItem();
            File f = new File(root.getAbsolutePath() + File.separator + x);
            item.setLocalFilePath(f.getAbsolutePath());
            item.setItemId(id.getAndIncrement());
            item.setFile(f);
            item.init();
            return item;
        }).collect(Collectors.toList());
        return items;
    }

    @Autowired
    private NewExamItemService newExamItemService;

    @Autowired
    private OfficeBaiDu officeBaiDu;

    @Test
    public void ocrPrintNumbers() throws IOException {
        String root = "D:\\fillings\\imgs";
        List<OCRExamItem> items = getFolderFiles(root);
        List<CombineImage> combineImages = aiMark.combineImages(items, false, false);
        for (int i = 0; i < combineImages.size(); i++) {
            CombineImage image = combineImages.get(i);
            image.setRecognitionType(1);
            OCRAPIUtil.exec(image, 3, img -> officeBaiDu.exec(img, false, false));
            List<OCRExamItem> ocrExamItemList = image.getItems().stream().map(x -> x.getOcrExamItem()).collect(Collectors.toList());
            AtomicInteger s = new AtomicInteger(0);
            ocrExamItemList.forEach(x -> {
                if (StringUtils.isNotBlank(x.getRecognitionValue())) {
                    x.setRv(x.getRecognitionValue());
                } else {
                    x.setRv("not found.");
                    s.getAndIncrement();
                }
            });

            System.out.println("total size: = " +  ocrExamItemList.size() + ", not found = " + s.get());
            List<CombineImage> newImages = aiMark.combineImages(ocrExamItemList, true, false);
            for (int j = 0; j < newImages.size(); j++) {
                ImageIO.write(newImages.get(j).getFile(), "png", new File(root +"/c_" + i + "_" + j + ".png"));
            }
        }
    }


    @Test
    public void downloadIntelligenceImages() {
        long minPaperReadId = Long.MAX_VALUE;
//      List<Long> paperReadIds = newExamItemService.getIntelligencePRIds(minPaperReadId);
        File root = new File("E:\\ocr\\");
        int action = 0;
        if(action == 1) {
            List<String> courseNames = Arrays.asList("生物", "数学", "物理", "化学");
            List<CoursePaperReadDTO> coursePaperReadIdList = newExamItemService.getIntelligencePRIds(courseNames);
            Map<String, List<CoursePaperReadDTO>> courseListInfo = coursePaperReadIdList.stream()
                    .collect(groupingBy(CoursePaperReadDTO::getCourseName));
            for (Map.Entry<String, List<CoursePaperReadDTO>> entry : courseListInfo.entrySet()) {
                String courseName = entry.getKey();
                List<CoursePaperReadDTO> values = entry.getValue();
                File courseRoot = new File(root.getAbsolutePath() + File.separator + courseName);
                for (CoursePaperReadDTO value : values) {
                    long paperReadId = value.getPaperReadId();
                    List<IntelligenceItem> itemList = newExamItemService.getExamItemListByPaperReadId(paperReadId);
                    if (itemList.size() == 0) {
                        continue;
                    }
                    downloadExamItemList(courseRoot, itemList, paperReadId);
                }
            }
        } else {
            List<Long> ids = Arrays.asList(864691128944028894L, 864691128944028892L);
            File courseRoot = new File(root.getAbsolutePath() + File.separator + "英语");
            for(Long paperReadId : ids) {
                List<IntelligenceItem> itemList = newExamItemService.getExamItemListByPaperReadId(paperReadId);
                if (itemList.size() == 0) {
                    continue;
                }
                downloadExamItemList(courseRoot, itemList, paperReadId);
            }

        }
    }

    private void downloadExamItemList(File rootDir, List<IntelligenceItem> itemList, long paperReadId) {
        File subFile = createAIFolder(rootDir, paperReadId);
        List<OCRExamItem> ocrExamItemList = itemList.stream().map(it -> {
            OCRExamItem x = new OCRExamItem();
            x.setItemId(it.getExamItemId());
            x.setUrl(it.getUrl());
            x.setRv(it.getRv());
            return x;
        }).collect(Collectors.toList());
        FileStorageTemplate.batchGet(ocrExamItemList, (FileStorageBatchGet fileStorageBatchGet) -> {
            ocrExamItemList.forEach(OCRExamItem::init);
            AtomicInteger out = new AtomicInteger(0);
            BatchDataUtil.execute(ocrExamItemList, items -> {
                out.incrementAndGet();
                List<CombineImage> images = aiMark.combineImages(items, false, false);
                List<CombineImage> resultImages = aiMark.combineImages(items, true, false);
                for (int i = 0; i < images.size(); i++) {
                    CombineImage image = images.get(i);
                    CombineImage img = resultImages.get(i);
                    try {
                        ImageIO.write(image.getFile(), "png", new File(subFile.getAbsoluteFile() + File.separator + paperReadId + "_" + out.get() + "_" + String.valueOf(i + 1) + ".png"));
                        ImageIO.write(img.getFile(), "png", new File(subFile.getAbsoluteFile() + File.separator + paperReadId + "_rv_" + out.get() + "_" + String.valueOf(i + 1) + ".png"));
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

            }, 240);

        });
    }

    private File createAIFolder(File root, long paperReadId) {
        boolean isRoot = root.exists();
        if (!isRoot) {
            isRoot = root.mkdir();
        }
        File subFile = new File(root, String.valueOf(paperReadId));
        if (isRoot) {
            if (subFile.exists()) {
                deleteDirectory(subFile);
                System.out.println("文件夹已存在并被删除: " + subFile.getAbsolutePath());
            }
            if (subFile.mkdir()) {
                System.out.println("文件夹创建成功: " + subFile.getAbsolutePath());
            } else {
                isRoot = false;
                System.out.println("文件夹创建失败: " + subFile.getAbsolutePath());
            }
        }

        return subFile;
    }

    public static void graphics2DDrawText(String srcImgPath, String outPath, String rv) {
        // 定义输入输出文件
        File input = new File(srcImgPath);
        File output = new File(outPath);

        try {
            // 读取图片
            BufferedImage image = ImageIO.read(input);
            // 创建Graphics2D对象
            Graphics2D g2d = (Graphics2D) image.getGraphics();
            // 设置字体样式、大小和颜色
            g2d.setFont(new Font("Arial", Font.BOLD, 40));
            g2d.setColor(Color.RED); // 文字颜色
            // 在图片上绘制文字
            int x = 50; // 文字起始X坐标
            int y = 50; // 文字起始Y坐标
            g2d.drawString(rv, x, y);
            // 释放资源
            g2d.dispose();
            // 将修改后的图片写入文件
            ImageIO.write(image, "png", output);
            System.out.println("文本已成功添加到图片。");

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 递归删除文件夹及其内容
     *
     * @param file 要删除的文件或文件夹
     * @return 删除是否成功
     */
    private static boolean deleteDirectory(File file) {
        if (file.isDirectory()) {
            // 获取文件夹下的所有文件和子文件夹
            File[] entries = file.listFiles();
            if (entries != null) {
                for (File entry : entries) {
                    // 递归删除每个条目
                    deleteDirectory(entry);
                }
            }
        }
        // 删除当前文件或文件夹
        return file.delete();
    }

    @Autowired
    private OCRAPIUtil ocrapiUtil;
    @Test
    public void doAPPId() {
        ocrapiUtil.getAccessToken();
    }

    @Autowired
    private AIMarkCollectService aiMarkCollectService;

    @Test
    public void initCollect() {
        aiMarkCollectService.initCollect();
    }

    @Test
    public void collect() {
        aiMarkCollectService.collect();
    }

    @Test
    public void insertQns() {
//        17,1640782217971515,4,英语,215,1001
        List<EICVO> list = new ArrayList<>();
        EICVO it = new EICVO();
        it.setExamId(17);
        it.setPaperId(1640782217971515L);
        it.setQuestionNumber(1001);
        list.add(it);
        aiMarkCollectService.insetAIMarkQns(list);
    }



    @Test
    public void downloadOCRImages() {
        List<String> courseNames = Arrays.asList("化学", "生物", "语文", "数学", "物理", "英语");
        for (String courseName : courseNames) {
            aiMarkCollectService.downloadOCRImages(courseName, new File("/home/<USER>/ocr/0304/"));
        }
    }

}
