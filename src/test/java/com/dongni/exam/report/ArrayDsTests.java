package com.dongni.exam.report;

import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.report.ArrayDsTests.CourseAvgRank.CourseScoreRank;
import com.hqjl.bi.annotations.BIClass;
import com.hqjl.bi.annotations.BIColumn;
import com.hqjl.bi.annotations.BIColumnType;
import com.hqjl.bi.annotations.BIColumnType.Type;
import com.hqjl.bi.beam.dto.ArrayDsDTO;
import com.hqjl.bi.client.etl.ArrayDsHelper;
import java.util.Arrays;
import java.util.List;

public class ArrayDsTests {

	public static void main(String[] args) {
		List<CourseScoreRank> all = Arrays.asList(
			new CourseScoreRank("总分", 580D, null),
			new CourseScoreRank("语文", 110D, null),
			new CourseScoreRank("数学", 120D, null)
		);
		List<CourseScoreRank> courseScoreRanks = Arrays.asList(
			new CourseScoreRank("总分", 580D, 1),
			new CourseScoreRank("语文", 110D, 2),
			new CourseScoreRank("数学", 120D, 3)
		);
		ArrayDsDTO arrayDsDTO = ArrayDsHelper.collection2ArrayDs(
			Arrays.asList(
				new CourseAvgRank("全体", all),
				new CourseAvgRank("166中学", courseScoreRanks)
			)
			, CourseAvgRank.class);
		System.out.println(JSONUtil.toJson(arrayDsDTO));
	}

	public static class CourseAvgRank {

		@BIColumn("当前对象")
		private String schoolName;
		@BIColumnType(value = Type.COLUMN_FLAT_LIST, identifyKey = "courseName")
		@BIColumn
		List<CourseScoreRank> courseScoreRanks;

		public CourseAvgRank(String schoolName, List<CourseScoreRank> courseScoreRanks) {
			this.schoolName = schoolName;
			this.courseScoreRanks = courseScoreRanks;
		}

		@BIClass
		public static class CourseScoreRank {
			private String courseName;
			@BIColumn(value = "均分",format = "0.00")
			private Double score;
			@BIColumn("排名")
			private Integer rank;

			public CourseScoreRank(String courseName, Double score, Integer rank) {
				this.courseName = courseName;
				this.score = score;
				this.rank = rank;
			}

			public String getCourseName() {
				return courseName;
			}

			public Double getScore() {
				return score;
			}

			public Integer getRank() {
				return rank;
			}
		}
	}
}
