package com.dongni.exam.report.pdf;


import com.dongni.common.http.RestService;
import com.dongni.commons.filestorage.entity.FileStorageRequest;
import com.dongni.commons.filestorage.service.IFileStorageOperateService;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.exam.report.pdf.PDFRenderHelper.ReportParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
public class PDFRenderUtilTest {

	@Autowired
	private PDFRenderHelper pdfRenderHelper;
	@Autowired
	private RestService restService;
	@Autowired
	private IFileStorageOperateService fileStorageOperateService;

	@Test
	public void getPdfUrl() {
		kugou();
	}

	private void kugou() {
		try {
			ReportParam reportParam = new ReportParam();
			reportParam
				.setExamId(5976684L)
				.setReportId(0)
				.setDiagnosisTime(1641540617000L)
				.setReportName("校级整体报告")
				.setReportType("school")
				.setSchoolId(548L)
				.setCourseId(null)
				.setClassId(null)
				.setToken("2b038b9703d848e3afda04a9e71430f3___1");
			String htmlUrl = restService.getUrl("https://dalao.dongni100.com/dongnireport/index.html",
				JSONUtil.parseToMap(JSONUtil.toJson(reportParam)));
			System.out.println(htmlUrl);
			String pdfUrl = pdfRenderHelper.getPdfUrl(reportParam.getReportName(),reportParam, null);
			FileStorageRequest fileStorageRequest = new FileStorageRequest();
			fileStorageRequest.setFilePath(pdfUrl);
			fileStorageRequest.setLocalFilePath("/home/<USER>/Downloads/校级整体报告.pdf");
			fileStorageRequest.setFileName(reportParam.getReportName());
			fileStorageOperateService.get(fileStorageRequest);
		} catch (Exception e) {
			LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
		}
	}
}
