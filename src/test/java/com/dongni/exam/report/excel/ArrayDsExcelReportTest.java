package com.dongni.exam.report.excel;

import com.dongni.common.report.excel.simple.ArrayDsExcelReport;
import com.hqjl.bi.annotations.BIClass;
import com.hqjl.bi.annotations.BIColumn;
import com.hqjl.bi.annotations.BIColumnType;
import org.junit.Test;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2023/7/25 下午 03:25
 * @Version 1.0.0
 */
public class ArrayDsExcelReportTest {
    @Test
    public void test() {
        Course course1 = new Course(1L, "语文", 192.3132D, 0.5323D);
        Course course2 = new Course(2L, "数学", 92.543D, 1D);

        Course course3 = new Course(1L, "语文", null, null);
        Course course4 = new Course(2L, "数学", 738D, 0.1D);
        Course course5 = new Course(3L, "英语", 354d, 0.343141312);

        School school = new School(321L, new Date(), "测试学校", 7786L,
                "admin", 3, Arrays.asList(course1, course2));
        School school2 = new School(543L, new Date(), "测试学校2", 3216L,
                "admin1", 2, Arrays.asList(course3, course4, course5));
        ArrayDsExcelReport excelReport = new ArrayDsExcelReport("测试导出", Arrays.asList(school, school2));
        excelReport.exportToLocalPath("C:\\Users\\<USER>\\Desktop\\");
    }

    @BIClass
    private static class School {
        @BIColumn("学校id")
        private long schoolId;

        @BIColumn("学校姓名")
        private String schoolName;

        // =============================相同groupKey需按顺序放一起===========================================
        @BIColumn(value = "创建时间", groupKey = "创建信息", format = "yyyy-MM-dd")
        private Date createDateTime;

        @BIColumn(value = "创建人id", groupKey = "创建信息")
        private long creatorId;

        @BIColumn(value = "创建人姓名", groupKey = "创建信息")
        private String creatorName;
        // ================================================================================================

        @BIColumn(value = "年级类型")
        private int stage;

        @BIColumn(sortedOptionKey = "courseId")
        @BIColumnType(value = BIColumnType.Type.COLUMN_FLAT_LIST, identifyKey = "courseName")
        private List<Course> courseList;

        public School(long schoolId, Date createDateTime, String schoolName, long creatorId, String creatorName,
                      int stage, List<Course> courseList) {
            this.schoolId = schoolId;
            this.createDateTime = createDateTime;
            this.schoolName = schoolName;
            this.creatorId = creatorId;
            this.creatorName = creatorName;
            this.stage = stage;
            this.courseList = courseList;
        }
    }

    @BIClass
    private static class Course {
        @BIColumn(value = "课程id")
        private long courseId;

        @BIColumn("课程名称")
        private String courseName;

        @BIColumn(value = "分数")
        private Double score;

        @BIColumn(value = "得分率", format = "0.00%")
        private Double rate;

        public Course(long courseId, String courseName, Double score, Double rate) {
            this.courseId = courseId;
            this.courseName = courseName;
            this.score = score;
            this.rate = rate;
        }
    }
}
