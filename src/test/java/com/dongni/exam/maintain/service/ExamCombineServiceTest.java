package com.dongni.exam.maintain.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.junit.Assert.*;

/**
 * (请添加描述)
 *
 * <AUTHOR>
 * @Date 2024/8/21 周三 下午 05:43
 * @Version 1.0.0
 */
//@SpringBootTest
//@RunWith(SpringRunner.class)
public class ExamCombineServiceTest {
    @Autowired
    private ExamCombineService examCombineService;

    @Test
    public void testAlignedDfs() {
        Map<Integer, List<Integer>> mark2PaperMappingMap = new HashMap<>();
        mark2PaperMappingMap.put(51, Arrays.asList(51));
        mark2PaperMappingMap.put(151, Arrays.asList(151));
        mark2PaperMappingMap.put(251, Arrays.asList(251, 351));
        mark2PaperMappingMap.put(351, Arrays.asList(451));
        mark2PaperMappingMap.put(451, Arrays.asList(451));
        mark2PaperMappingMap.put(551, Arrays.asList(551));
        mark2PaperMappingMap.put(651, Arrays.asList(551, 651));
        mark2PaperMappingMap.put(751, Arrays.asList(651));

        Map<Integer, List<Integer>> paper2MarkMappingMap = new HashMap<>();
        paper2MarkMappingMap.put(51, Arrays.asList(51));
        paper2MarkMappingMap.put(151, Arrays.asList(151));
        paper2MarkMappingMap.put(251, Arrays.asList(251));
        paper2MarkMappingMap.put(351, Arrays.asList(251));
        paper2MarkMappingMap.put(451, Arrays.asList(351, 451));
        paper2MarkMappingMap.put(551, Arrays.asList(551, 651));
        paper2MarkMappingMap.put(651, Arrays.asList(651, 751));

        Set<Integer> res1 = new HashSet<>();
//        examCombineService.alignedDfs(51, res1, new HashSet<>(), new HashSet<>(), mark2PaperMappingMap, paper2MarkMappingMap);
        assertArrayEquals(new int[]{51}, res1.stream().sorted().mapToInt(i -> i).toArray());

        Set<Integer> res2 = new HashSet<>();
//        examCombineService.alignedDfs(251, res2, new HashSet<>(), new HashSet<>(), mark2PaperMappingMap, paper2MarkMappingMap);
        assertArrayEquals(new int[]{251}, res2.stream().sorted().mapToInt(i -> i).toArray());

        Set<Integer> res3 = new HashSet<>();
//        examCombineService.alignedDfs(351, res3, new HashSet<>(), new HashSet<>(), mark2PaperMappingMap, paper2MarkMappingMap);
        assertArrayEquals(new int[]{351,451}, res3.stream().sorted().mapToInt(i -> i).toArray());

        Set<Integer> res4 = new HashSet<>();
//        examCombineService.alignedDfs(551, res4, new HashSet<>(), new HashSet<>(), mark2PaperMappingMap, paper2MarkMappingMap);
        assertArrayEquals(new int[]{551,651,751}, res4.stream().sorted().mapToInt(i -> i).toArray());
    }
}