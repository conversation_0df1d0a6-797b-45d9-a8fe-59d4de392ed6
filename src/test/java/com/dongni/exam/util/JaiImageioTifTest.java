package com.dongni.exam.util;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.commons.utils.FileUtil;
import com.dongni.commons.utils.IoUtil;
import com.dongni.tiku.common.util.MapUtil;
import com.github.jaiimageio.impl.plugins.tiff.TIFFJPEGCompressor;
import com.github.jaiimageio.impl.plugins.tiff.TIFFLZWCompressor;
import com.github.jaiimageio.impl.plugins.tiff.TIFFNullCompressor;
import com.github.jaiimageio.impl.plugins.tiff.TIFFRLECompressor;
import com.github.jaiimageio.impl.plugins.tiff.TIFFT4Compressor;
import com.github.jaiimageio.impl.plugins.tiff.TIFFT6Compressor;
import com.github.jaiimageio.plugins.tiff.BaselineTIFFTagSet;
import com.github.jaiimageio.plugins.tiff.TIFFCompressor;
import com.github.jaiimageio.plugins.tiff.TIFFImageWriteParam;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageReader;
import javax.imageio.ImageWriter;
import javax.imageio.stream.FileImageInputStream;
import javax.imageio.stream.FileImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.Locale;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * 2022/12/20
 */
public class JaiImageioTifTest {
    
    private final static Logger log = LoggerFactory.getLogger(JaiImageioTifTest.class);
    
    /** 测试的文件的存放目录 如果是windows，在当前项目所在盘符下的对应路径 */
    private final static String TEST_LOCAL_DIR_PATH = "/home/<USER>/test/fat-service/compress/tif/";
    
    private File getFile(String url) {
        String fileName = url.substring(url.lastIndexOf("/") + 1);
        return ImageCompressUtilTest.getFile(url, TEST_LOCAL_DIR_PATH + fileName);
    }
    
    private final static Map<Integer, String> compressionValue2Name = MapUtil.of(
            BaselineTIFFTagSet.COMPRESSION_NONE, "NONE",
            BaselineTIFFTagSet.COMPRESSION_CCITT_RLE, "CCITT_RLE",
            BaselineTIFFTagSet.COMPRESSION_CCITT_T_4, "CCITT_T_4",
            BaselineTIFFTagSet.COMPRESSION_CCITT_T_6, "CCITT_T_6",
            BaselineTIFFTagSet.COMPRESSION_LZW, "LZW",
            BaselineTIFFTagSet.COMPRESSION_OLD_JPEG, "OLD_JPEG",
            BaselineTIFFTagSet.COMPRESSION_JPEG, "JPEG",
            BaselineTIFFTagSet.COMPRESSION_ZLIB, "ZLIB",
            BaselineTIFFTagSet.COMPRESSION_PACKBITS, "PACKBITS",
            BaselineTIFFTagSet.COMPRESSION_DEFLATE, "DEFLATE"
    );
    
    @Test
    public void test() throws Exception {
        log.info("{}", new File(TEST_LOCAL_DIR_PATH).getAbsolutePath());
        
        // tif 黑白1bit  LZW压缩算法 大小为110KB
        File srcFile1BitLZW = getFile("https://cdntest.dongni100.com/test/images/compress/tif/001_bw1bit_LZW_0001.TIF");
        // 将压缩的保存为未压缩的 就变成了下面那个459KB
        compress_1_NONE(srcFile1BitLZW);
        
        // tif 黑白1bit  未使用压缩算法 大小为459KB
        File srcFile1BitNONE = getFile("https://cdntest.dongni100.com/test/images/compress/tif/011_bw1bit_NONE_0001.TIF");
        // tif 彩色24bit 未使用压缩算法 大小为11MB
        File srcFile24BitNONE = getFile("https://cdntest.dongni100.com/test/images/compress/tif/021_color24bit_NONE.tif");
        
        // bw1bit_NULL_0001.TIF -> bw1bit_NULL_0001_2_CCITT_RLE.TIF, 459.989 KB -> 129.189 KB ==> 28.09 %      !!!但是图片看起来有问题啊
        compress_2_CCITT_RLE(srcFile1BitNONE);
        // compress_2_CCITT_RLE(srcFile24BitNONE);  // javax.imageio.IIOException: Bits per sample must be 1 for RLE compression!
        
        // bw1bit_NONE_0001.TIF -> bw1bit_NONE_0001_3_CCITT_T_4.TIF, 459.989 KB -> 91.785 KB ==> 19.95 %
        compress_3_CCITT_T_4(srcFile1BitNONE);
        // compress_3_CCITT_T_4(srcFile24BitNONE);  // javax.imageio.IIOException: Bits per sample must be 1 for T4 compression!
        
        // bw1bit_NULL_0001.TIF -> bw1bit_NULL_0001_4_CCITT_T_6.TIF, 459.989 KB -> 52.596 KB ==> 11.43 %
        compress_4_CCITT_T_6(srcFile1BitNONE);
        // compress_4_COMPRESSION_CCITT_T_6(srcFile24BitNONE); // javax.imageio.IIOException: Bits per sample must be 1 for T6 compression!
        
        // bw1bit_NONE_0001.TIF -> bw1bit_NONE_0001_5_CCITT_LZW.TIF, 459.989 KB -> 107.632 KB ==> 23.40 %
        compress_5_LZW(srcFile1BitNONE);
        // color24bit_NONE.tif -> color24bit_NONE_5_CCITT_LZW.tif, 11.017 MB -> 955.300 KB ==> 8.47 %
        compress_5_LZW(srcFile24BitNONE);
        
        // compress_7_JPEG(srcFile1BitNONE);  // javax.imageio.IIOException: Can only JPEG compress 8- and 24-bit images!
        // color24bit_NONE.tif -> color24bit_NONE_7_JPEG.tif, 11.017 MB -> 759.422 KB ==> 6.73 %               !!!但是图片看起来有问题啊
        compress_7_JPEG(srcFile24BitNONE);
        
        // 看起来
        // 1bit  CCITT_T_6 最好
        // 8-24bit JPEG 最好
    }
    
    /**
     *
     * @param srcFile 原图
     * @return 不压缩的文件
     */
    private File compress_1_NONE(File srcFile) {
        return compress(srcFile, TIFFNullCompressor::new);
    }
    /**
     * javax.imageio.IIOException: Bits per sample must be 1 for RLE compression!
     * @param srcFile 待压缩的文件
     * @return 压缩的文件
     */
    private File compress_2_CCITT_RLE(File srcFile) {
        return compress(srcFile, TIFFRLECompressor::new);
    }
    /**
     * javax.imageio.IIOException: Bits per sample must be 1 for T4 compression!
     * @param srcFile 待压缩的文件
     * @return 压缩的文件
     */
    private File compress_3_CCITT_T_4(File srcFile) {
        return compress(srcFile, TIFFT4Compressor::new);
    }
    /**
     * javax.imageio.IIOException: Bits per sample must be 1 for T6 compression!
     * @param srcFile 待压缩的文件
     * @return 压缩的文件
     */
    private File compress_4_CCITT_T_6(File srcFile) {
        return compress(srcFile, TIFFT6Compressor::new);
    }
    /**
     *
     * @param srcFile 待压缩的文件
     * @return 压缩的文件
     */
    private File compress_5_LZW(File srcFile) {
        return compress(srcFile, () -> new TIFFLZWCompressor(BaselineTIFFTagSet.PREDICTOR_NONE));
    }
    /**
     * javax.imageio.IIOException: Can only JPEG compress 8- and 24-bit images!
     * @param srcFile 待压缩的文件
     * @return 压缩的文件
     */
    private File compress_7_JPEG(File srcFile) {
        return compress(srcFile, TIFFJPEGCompressor::new);
    }
    
    // private File compress_6_OLD_JPEG(File srcFile) {
    //     // 不支持了 只能读 不能压缩了
    //     return null;
    // }
    // private File compress_8_ZLIB(File srcFile) {
    //     // 玩不动 够用了
    //     // return compress(srcFile, tiffImageWriteParam -> {
    //     //     tiffImageWriteParam.setCompressionType("ZLib");
    //     //     return new TIFFZLibCompressor(tiffImageWriteParam, BaselineTIFFTagSet.PREDICTOR_NONE);
    //     // });
    //     return null;
    // }
    
    // private File compress(File srcFile, int compressionValue, String compressionName, MyConsumer<File> consumer) {
    //     try {
    //         String fileName = srcFile.getName();                                                    // test.png
    //         String fileExtension = fileName.substring(fileName.lastIndexOf("."));               // .png
    //         String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf(".")); // test
    //         String compressFileName = fileNameWithoutExtension + "_" + compressionValue + "_" + compressionName + fileExtension;
    //         String filePath = TEST_LOCAL_DIR_PATH + compressFileName;
    //         File compressFile = FileUtil.getFile(filePath);
    //         if (compressFile.exists()) {
    //             compressFile.delete();
    //         }
    //         consumer.accept(compressFile);
    //
    //         long srcLength = srcFile.length();
    //         long compressLength = compressFile.length();
    //         if (srcLength > compressLength) {
    //             log.info("{} -> {}, {} -> {} ==> {}",
    //                     srcFile.getName(), compressFile.getName(),
    //                     DisplayUtil.displayFileSize(srcLength),
    //                     DisplayUtil.displayFileSize(compressLength),
    //                     DisplayUtil.displayPercent(compressLength, srcLength)
    //             );
    //         } else {
    //             log.warn("{} -> {}, {} -> {} ==> {}",
    //                     srcFile.getName(), compressFile.getName(),
    //                     DisplayUtil.displayFileSize(srcLength),
    //                     DisplayUtil.displayFileSize(compressLength),
    //                     DisplayUtil.displayPercent(compressLength, srcLength)
    //             );
    //         }
    //         return compressFile;
    //     } catch (Exception e) {
    //         log.error("压缩异常: {} : {}", compressionValue, compressionName, e);
    //         return null;
    //     }
    // }
    
    private File compress(File srcFile, MySupplier<TIFFCompressor> supplier) {
        return compress(srcFile, tiffImageWriteParam -> supplier.get());
    }
    
    private File compress(File srcFile, MyFunction<TIFFImageWriteParam, TIFFCompressor> function) {
        int compressionValue = Integer.MIN_VALUE;
        String compressionName = null;
        try {
            String fileName = srcFile.getName();                                                    // test.png
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));               // .png
            String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf(".")); // test
            
            TIFFImageWriteParam tiffImageWriteParam = new TIFFImageWriteParam(Locale.getDefault());
            tiffImageWriteParam.setCompressionMode(TIFFImageWriteParam.MODE_EXPLICIT);
            
            TIFFCompressor tiffCompressor = function.apply(tiffImageWriteParam);
            compressionValue = tiffCompressor.getCompressionTagValue();
            tiffImageWriteParam.setTIFFCompressor(tiffCompressor);
            String compressionType = tiffCompressor.getCompressionType();
            if (StringUtils.isNotBlank(compressionType)) {
                tiffImageWriteParam.setCompressionType(compressionType);
            }
            
            compressionName = compressionValue2Name.get(compressionValue);
            String compressFileName = fileNameWithoutExtension + "_" + compressionValue + "_" + compressionName + fileExtension;
            String filePath = TEST_LOCAL_DIR_PATH + compressFileName;
            File compressFile = FileUtil.getFile(filePath);
            if (compressFile.exists()) {
                compressFile.delete();
            }
            
            FileImageInputStream fiis = null;
            FileImageOutputStream fios = null;
            try {
                fiis = ImageCompressUtil.getFileImageInputStream(srcFile);
                ImageReader imageReader = ImageCompressUtil.getImageReaderTiff(fiis);
                BufferedImage srcImage = ImageCompressUtil.getBufferedImage(imageReader);
                
                fios = ImageCompressUtil.getFileImageOutputStream(compressFile);
                ImageWriter imageWriter = ImageCompressUtil.getImageWriterTiff(fios);
                
                IIOImage iioImage = new IIOImage(srcImage, null, null);
                imageWriter.write(null, iioImage, tiffImageWriteParam);
            } finally {
                IoUtil.close(fiis);
                IoUtil.close(fios);
            }
            
            long srcLength = srcFile.length();
            long compressLength = compressFile.length();
            if (srcLength > compressLength) {
                log.info("{} -> {}, {} -> {} ==> {}",
                        srcFile.getName(), compressFile.getName(),
                        DisplayUtil.storageSize(srcLength),
                        DisplayUtil.storageSize(compressLength),
                        DisplayUtil.percent(compressLength, srcLength)
                );
            } else {
                log.warn("{} -> {}, {} -> {} ==> {}",
                        srcFile.getName(), compressFile.getName(),
                        DisplayUtil.storageSize(srcLength),
                        DisplayUtil.storageSize(compressLength),
                        DisplayUtil.percent(compressLength, srcLength)
                );
            }
            return compressFile;
        } catch (Exception e) {
            log.error("压缩异常: {} : {}", compressionValue, compressionName, e);
            return null;
        }
    }

    @FunctionalInterface
    private interface MySupplier<T> {
        T get() throws Exception;
    }
    
    @FunctionalInterface
    private interface MyFunction<T, R> {
        R apply(T t) throws Exception;
    }
}
