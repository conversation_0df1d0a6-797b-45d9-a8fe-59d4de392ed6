package com.dongni.exam.util;

import com.dongni.commons.utils.FileUtil;
import com.dongni.commons.utils.IoUtil;
import com.dongni.exam.gray.enumeration.GrayResultTypeEnum;
import com.sun.imageio.plugins.png.PNGMetadata;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.FileImageInputStream;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.awt.image.DataBuffer;
import java.awt.image.IndexColorModel;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * PNG 文件头数据块（IHDR）
 * name      byte
 * Width     4     图像宽度，以像素为单位
 * Height    4     图像高度，以像素为单位
 * Bit depth 1     图像深度：
 *                   索引彩色图像：1，2，4或8
 *                   灰度图像：1，2，4，8或16
 *                   真彩色图像：8或16
 * ColorType 1     颜色类型：
 *                    0：灰度图像，1，2，4，8或16
 *                    2：真彩色图像，8或16
 *                    3：索引彩色图像，1，2，4或8
 *                    4：带α通道数据的灰度图像，8或16
 *                    6：带α通道数据的真彩色图像，8或16
 *
 * <AUTHOR>
 * 2022/12/27
 */
public class PngTest {
    
    private final static Logger log = LoggerFactory.getLogger(PngTest.class);
    
    public final static DateTimeFormatter YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /** 测试的文件的存放目录 如果是windows，在当前项目所在盘符下的对应路径 */
    private final static String TEST_LOCAL_DIR_PATH = "/home/<USER>/test/fat-service/compress/pngbit/";
    
    /**
     * 测试位深
     */
    @Test
    public void testBit() {
        String yyyyMMdd_HHmmss = LocalDateTime.now().format(YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER);
        String testLocalDir = TEST_LOCAL_DIR_PATH + yyyyMMdd_HHmmss + "/";
        File testLocalDirFile = FileUtil.getDir(testLocalDir);
        log.info("{}", testLocalDirFile.getAbsolutePath());
        
        List<String> fileUrlList = Stream
                .of(
                        // "https://cdntest.dongni100.com/test/images/compress/png_010001_bw1bit_0001.png",
                        // "https://cdntest.dongni100.com/test/images/compress/png_040001_gray4bit_0001.png",
                        // "https://cdntest.dongni100.com/test/images/compress/png_080001_bw8bit_0001.png",
                        "https://cdntest.dongni100.com/test/images/compress/png_240001_color24bit_0001.png",
                        // "https://cdntest.dongni100.com/test/images/compress/png_240001_color24bit_0002.png",
                        // "https://cdntest.dongni100.com/test/images/compress/png_240002_indexed8bit_0001.png",
                        // "https://cdntest.dongni100.com/test/images/compress/png_240002_indexed8bit_0002.png",
                        null
                )
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Integer> localFileName2Count = new HashMap<>();
        for (String fileUrl : fileUrlList) {
            // test.png
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            // .png
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            // test
            String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf("."));
            int count = localFileName2Count.merge(fileName, 1, Integer::sum);
            if (count > 1) {
                // test___2.png  test___3.png  ...
                fileName = fileNameWithoutExtension + "___" + count + fileExtension;
            }
            
            String saveFileName = testLocalDir + fileName;
            try {
                File srcFile = ImageCompressUtilTest.getFile(fileUrl, saveFileName);
                FileImageInputStream fiis = null;
                try {
                    fiis = ImageCompressUtil.getFileImageInputStream(srcFile);
                    ImageReader imageReader = ImageCompressUtil.getImageReaderPng(fiis);
                    PNGMetadata imageMetadata = (PNGMetadata) ImageCompressUtil.getImageMetadata(imageReader);
                    // 指一个通道的位数
                    int bitDepth = imageMetadata.IHDR_bitDepth;
                    
                    // 这个是指所有通道的位深和
                    int bitsPerSampleValueSum = 0;
                    BufferedImage bufferedImage = ImageCompressUtil.getBufferedImage(imageReader);
                    int[] sampleSizes = bufferedImage.getSampleModel().getSampleSize();
                    for (int sampleSize : sampleSizes) {
                        bitsPerSampleValueSum += sampleSize;
                    }
                    
                    //     static final int PNG_COLOR_GRAY = 0;       灰度图像，1，2，4，8或16
                    //     static final int PNG_COLOR_RGB = 2;        真彩色图像，8或16
                    //     static final int PNG_COLOR_PALETTE = 3;    索引彩色图像，1，2，4或8
                    //     static final int PNG_COLOR_GRAY_ALPHA = 4; 带α通道数据的灰度图像，8或16
                    //     static final int PNG_COLOR_RGB_ALPHA = 6;  带α通道数据的真彩色图像，8或16
                    int colorType = imageMetadata.IHDR_colorType;
                    
                    Map<Integer, Set<Integer>> step2rgbSet = new LinkedHashMap<>();
                    Stream.of(1, 3, 5, 15, 17, 51).forEach(step -> {
                        step2rgbSet.put(step, new HashSet<>());
                    });
                    int width = bufferedImage.getWidth();
                    int height = bufferedImage.getHeight();
                    for (int x = 0; x < width; x++) {
                        for (int y = 0; y < height; y++) {
                            int srcRgb = bufferedImage.getRGB(x, y);
                            Color srcColor = new Color(srcRgb);
                            step2rgbSet.forEach((step, rgbSet) -> {
                                rgbSet.add(getColor(srcColor, step).getRGB());
                            });
                        }
                    }
                    
                    // 如果是 PNG_COLOR_RGB=2 + bitDepth=8 -> 所有位深和为24
                    log.info("{} : {} : {} : {} - {}",
                            fileUrl, fileName, colorType, bitDepth, bitsPerSampleValueSum
                    );
    
    
                    File compressFile = new File(saveFileName + "_output_0_" + fileExtension);
                    GrayResultTypeEnum compressResult = ImageCompressUtil.compress(srcFile, compressFile);
                    if (compressResult != GrayResultTypeEnum.SUCCESS) {
                        if (compressResult == GrayResultTypeEnum.POOR_EFFECT) {
                            compressFile.renameTo(new File(saveFileName + "_output_0_★★★★POOR_EFFECT★★★★" + fileExtension));
                        } else {
                            File errorFile = new File(saveFileName + "_output_0_★★★★" + compressResult +  "★★★★" + fileExtension);
                            ImageIO.write(new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_BINARY), "png", errorFile);
                        }
                    }
                    for (Map.Entry<Integer, Set<Integer>> entry : step2rgbSet.entrySet()) {
                        int step = entry.getKey();
                        Set<Integer> rgbSet = entry.getValue();
                        int rgbSize = rgbSet.size();
                        int bits = 0;
                        int tmpSize = rgbSize - 1;
                        while (tmpSize != 0) {
                            tmpSize = tmpSize >> 1;
                            bits++;
                        }
                        log.info("{} : {} : {}", step, rgbSize, bits);
                        
                        if (bits > Byte.SIZE) {
                            File errorFile = new File(saveFileName + "_output_" + step + "_★★★★size_out_of★★★★" + fileExtension);
                            ImageIO.write(new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY), "png", errorFile);
                            continue;
                        }
                        
                        int[] colorMap = new int[rgbSize];
                        int i = 0;
                        List<Integer> rgbSortList = rgbSet.stream().sorted().collect(Collectors.toList());
                        for (Integer rgb : rgbSortList) {
                            Color color = new Color(rgb);
                            int red = color.getRed();
                            int green = color.getGreen();
                            int blue = color.getBlue();
                            colorMap[i++] = (red << 16) | (green << 8) | blue;
                        }
                        IndexColorModel icm = new IndexColorModel(bits, rgbSize, colorMap, 0, false, -1, DataBuffer.TYPE_BYTE);
                        BufferedImage newImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_INDEXED, icm);
                        for (int x = 0; x < width; x++) {
                            for (int y = 0; y < height; y++) {
                                newImage.setRGB(x, y, bufferedImage.getRGB(x, y));
                            }
                        }
                        File stepFile = new File(saveFileName + "_output_" + step + "_" + bits +"_" + fileExtension);
                        ImageIO.write(newImage, "png", stepFile);
    
                        IndexColorModel icm8 = new IndexColorModel(8, rgbSize, colorMap, 0, false, -1, DataBuffer.TYPE_BYTE);
                        BufferedImage newImage8 = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_INDEXED, icm8);
                        for (int x = 0; x < width; x++) {
                            for (int y = 0; y < height; y++) {
                                newImage8.setRGB(x, y, bufferedImage.getRGB(x, y));
                            }
                        }
                        File stepFile2 = new File(saveFileName + "_output_" + step + "_" + 8 +"_" + fileExtension);
                        ImageIO.write(newImage8, "png", stepFile2);
                        
                        if (step == 51) {
                            Set<Integer> rgbSet2 = Arrays.stream(colorMap).boxed().collect(Collectors.toSet());
                            // Create a 6x6x6 color cube, has not gray
                            int[] colorMap2 = new int[6 * 6 * 6];
                            int colorMapIndex = 0;
                            for (int r = 0; r < 256; r += 51) {
                                for (int g = 0; g < 256; g += 51) {
                                    for (int b = 0; b < 256; b += 51) {
                                        int rgb = (r << 16) | (g << 8) | b;
                                        if (rgbSet2.contains(rgb)) {
                                            colorMap2[colorMapIndex++] = rgb;
                                        } else {
                                            colorMap2[colorMapIndex++] = 0;
                                        }
                                    }
                                }
                            }
                            IndexColorModel icm2 = new IndexColorModel(8, 6*6*6, colorMap2, 0, false, -1, DataBuffer.TYPE_BYTE);
                            BufferedImage newImage2 = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_INDEXED, icm2);
                            for (int x = 0; x < width; x++) {
                                for (int y = 0; y < height; y++) {
                                    newImage2.setRGB(x, y, bufferedImage.getRGB(x, y));
                                }
                            }
                            File stepFile3 = new File(saveFileName + "_output_" + step + "_8_216_" + fileExtension);
                            ImageIO.write(newImage2, "png", stepFile3);
                            
                            System.out.println(Arrays.stream(colorMap).boxed().collect(Collectors.toList()));
                            System.out.println(Arrays.stream(colorMap2).boxed().collect(Collectors.toList()));
                        }
                        
                    }
                } finally {
                    IoUtil.close(fiis);
                }
            } catch (Exception e) {
                log.error("{} : {}", fileUrl, fileName, e);
            }
        }
    }
    
    /**
     * @param i 1 3 5 15 17 51
     * @return color
     */
    private Color getColor(Color color, int i) {
        int red = color.getRed();
        int green = color.getGreen();
        int blue = color.getBlue();
        red = i * (red / i + ((red % i) > (i / 2) ? 1 : 0));
        green = i * (green / i + ((green % i) > (i / 2) ? 1 : 0));
        blue = i * (blue / i + ((blue % i) > (i / 2) ? 1 : 0));
        return new Color(red, green, blue);
    }
    
    
    @Test
    public void testColorModel() throws IOException {
        Color[] colors = {
                new Color(0xFF0000),
                new Color(0x00FFFF),
                new Color(0x00FFA0),
                new Color(0x00FFC0),
                new Color(0x0000A0),
                new Color(0x0000C0),
                new Color(0x0000FF),
                new Color(0xADD8E6),
                new Color(0x800080),
                new Color(0xFFFF00),
                new Color(0x00FF00),
                new Color(0xFF00FF),
                new Color(0xFFFFFF),
                new Color(0xC0C0C0),
                new Color(0x808080),
                new Color(0x000000),
                new Color(0xFFA500),
                new Color(0xA52A2A),
                new Color(0x800000),
                new Color(0x008000),
                new Color(0x808000),
        };
        
        byte ff = (byte) 0xff;
        byte[] rBytes = {(byte) 0xf1, (byte) 0x01, (byte) 0x11, (byte) 0x81};
        byte[] gBytes = {(byte) 0x02, (byte) 0xf2, (byte) 0x12, (byte) 0x82};
        byte[] bBytes = {(byte) 0x03, (byte) 0x13, (byte) 0xf3, (byte) 0x83};
        
        IndexColorModel cm = getIndexColorModelAndTestSetRGBs(2, 4, rBytes, gBytes, bBytes);
        BufferedImage customByteIndexedBi = new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_INDEXED, cm);
        ColorModel customByteIndexedColorModel = customByteIndexedBi.getColorModel();
        
        // Create a 6x6x6 color cube
        int[] colorMap = new int[6 * 6 * 6];
        int colorMapIndex = 0;
        for (int r = 0; r < 256; r += 51) {
            for (int g = 0; g < 256; g += 51) {
                for (int b = 0; b < 256; b += 51) {
                    colorMap[colorMapIndex++] = (r << 16) | (g << 8) | b;
                }
            }
        }
        
        IndexColorModel cm2 = new IndexColorModel(8, 216, colorMap, 0, false, -1, DataBuffer.TYPE_BYTE);
        BufferedImage customByteIndexedBi2 = new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_INDEXED, cm2);
        ColorModel customByteIndexedColorModel2 = customByteIndexedBi2.getColorModel();
        
        BufferedImage defaultByteIndexedBi = new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_INDEXED);
        ColorModel defaultByteIndexedColorModel = defaultByteIndexedBi.getColorModel();
        
        BufferedImage intRgbBi = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        ColorModel intRgbColorModel = intRgbBi.getColorModel();
        
        ColorModel[] colorModels = {intRgbColorModel, defaultByteIndexedColorModel, customByteIndexedColorModel, customByteIndexedColorModel2};
        // ColorModel[] colorModels = {customByteIndexedColorModel};
        
        int sizePerColor = 128;
        int sizeWhite = 5;
        BufferedImage outputBufferedImage = new BufferedImage(
                sizePerColor * colorModels.length + sizeWhite * (colorModels.length - 1),
                sizePerColor * colors.length + sizeWhite * (colors.length - 1),
                BufferedImage.TYPE_INT_RGB
        );
        for (int j = 0; j < colors.length; j++) {
            Color color = colors[j];
            int rgb = color.getRGB();
            System.out.printf("0x%x   ", rgb);
            for (int i = 0; i < colorModels.length; i++) {
                
                ColorModel colorModel = colorModels[i];
                Object dataElements = colorModel.getDataElements(rgb, null);
                testRbg(rgb);
                int outputRgb = colorModel.getRGB(dataElements);
                System.out.printf("0x%x   ", outputRgb);
                
                for (int x = sizePerColor * i + sizeWhite * i, xLen = sizePerColor * (i + 1) + sizeWhite * i; x < xLen; x++) {
                    for (int y = sizePerColor * j + sizeWhite * j, jLen = sizePerColor * (j + 1) + sizeWhite * j; y < jLen; y++) {
                        outputBufferedImage.setRGB(x, y, outputRgb);
                    }
                }
            }
            System.out.println();
        }
        
        ImageIO.write(outputBufferedImage, "png", FileUtil.getFile("/home/<USER>/test/fat-service/colorModel/对比.png"));
        
        
        System.out.println(" --------------------------------------------------------------- ");
        List<String> nameList = Stream.of("intRgb", "defaultByteIndexed", "customByteIndexed", "customByteIndexed2").collect(Collectors.toList());
        List<ColorModel> colorModelList = Stream.of(intRgbColorModel, defaultByteIndexedColorModel, customByteIndexedColorModel, customByteIndexedColorModel2).collect(Collectors.toList());
        List<Set<Integer>> rgbSetList = Stream.generate((Supplier<Set<Integer>>) HashSet::new).limit(4).collect(Collectors.toList());
        
        int rgbCount = 0;
        for (int red = 0; red < 256; red++) {
            for (int green = 0; green < 256; green++) {
                for (int blue = 0; blue < 256; blue++) {
                    Color color = new Color(red, green, blue);
                    int rgb = color.getRGB();
                    rgbCount++;
                    for (int i = 0, iLen = nameList.size(); i < iLen; i++) {
                        ColorModel colorModel = colorModelList.get(i);
                        Set<Integer> rgbSet = rgbSetList.get(i);
                        
                        Object dataElements = colorModel.getDataElements(rgb, null);
                        int outputRgb = colorModel.getRGB(dataElements);
                        rgbSet.add(outputRgb);
                    }
                }
            }
        }
        System.out.println("rgbCount = " + rgbCount);
        for (int i = 0, iLen = nameList.size(); i < iLen; i++) {
            String name = nameList.get(i);
            ColorModel colorModel = colorModelList.get(i);
            Set<Integer> rgbSet = rgbSetList.get(i);
            int mapSize = -1;
            if (colorModel instanceof IndexColorModel) {
                IndexColorModel indexColorModel = (IndexColorModel) colorModel;
                mapSize = indexColorModel.getMapSize();
            }
            System.out.println("name = " + name + "; rgbSet.size = " + rgbSet.size() + "; mapSize = " + mapSize);
        }
        
        
        List<String> nameList2 = Stream.of("0_intRgb", "1_defaultByteIndexed", "3_customByteIndexed", "2_customByteIndexed2").collect(Collectors.toList());
        {
            List<BufferedImage> outputBufferedImageList = Stream.of(
                            new BufferedImage(4096, 4096, BufferedImage.TYPE_INT_RGB),
                            new BufferedImage(4096, 4096, BufferedImage.TYPE_BYTE_INDEXED),
                            new BufferedImage(4096, 4096, BufferedImage.TYPE_BYTE_INDEXED, cm),
                            new BufferedImage(4096, 4096, BufferedImage.TYPE_BYTE_INDEXED, cm2)
                    )
                    .collect(Collectors.toList());
            
            int x = 0;
            int y = 0;
            for (int i = 0, iLen = 4096 * 4096; i < iLen; i++) {
                Color color = new Color(i);
                for (BufferedImage bufferedImage : outputBufferedImageList) {
                    bufferedImage.setRGB(x, y, color.getRGB());
                }
                x++;
                if (x == 4096) {
                    x = 0;
                    y++;
                }
            }
            for (int i = 0; i < nameList2.size(); i++) {
                String name = nameList2.get(i);
                BufferedImage bufferedImage = outputBufferedImageList.get(i);
                ImageIO.write(bufferedImage, "png", FileUtil.getFile("/home/<USER>/test/fat-service/colorModel/4096_4096_" + name + ".png"));
            }
        }
        
        {
            List<BufferedImage> outputBufferedImageList2 = Stream.of(
                            new BufferedImage(256, 256 * 256, BufferedImage.TYPE_INT_RGB),
                            new BufferedImage(256, 256 * 256, BufferedImage.TYPE_BYTE_INDEXED),
                            new BufferedImage(256, 256 * 256, BufferedImage.TYPE_BYTE_INDEXED, cm),
                            new BufferedImage(256, 256 * 256, BufferedImage.TYPE_BYTE_INDEXED, cm2)
                    )
                    .collect(Collectors.toList());
            for (int red = 0; red < 256; red++) {
                int x = red;
                for (int greed = 0; greed < 256; greed++) {
                    for (int blue = 0; blue < 256; blue++) {
                        int y = greed * 256 + blue;
                        Color color = new Color(red, greed, blue);
                        for (BufferedImage bufferedImage : outputBufferedImageList2) {
                            bufferedImage.setRGB(x, y, color.getRGB());
                        }
                    }
                }
            }
            for (int i = 0; i < nameList2.size(); i++) {
                String name = nameList2.get(i);
                BufferedImage bufferedImage = outputBufferedImageList2.get(i);
                ImageIO.write(bufferedImage, "png", FileUtil.getFile("/home/<USER>/test/fat-service/colorModel/256_" + (256 * 256) + "_" + name + ".png"));
            }
        }
        
        {
            List<BufferedImage> bufferedImageList = Stream.of(
                            new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB),
                            new BufferedImage(1, 1, BufferedImage.TYPE_USHORT_565_RGB),
                            new BufferedImage(1, 1, BufferedImage.TYPE_USHORT_555_RGB),
                            new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_INDEXED),
                            new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_INDEXED, cm2),
                            new BufferedImage(1, 1, BufferedImage.TYPE_USHORT_GRAY),
                            new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_GRAY),
                            new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_BINARY),
                            null
                    )
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            int bufferedImageSize = bufferedImageList.size();
            int sizeSplit = 5;
            BufferedImage outputBufferedImage2 = new BufferedImage(
                    256 * bufferedImageSize + sizeSplit * (bufferedImageSize - 1),
                    256 * 256 + sizeSplit * (256 - 1),
                    BufferedImage.TYPE_INT_RGB
            );
            for (int red = 0; red < 256; red++) {
                for (int greed = 0; greed < 256; greed++) {
                    int yOffset = sizeSplit * greed;
                    for (int blue = 0; blue < 256; blue++) {
                        int y = greed * 256 + blue + yOffset;
                        Color color = new Color(red, greed, blue);
                        int rgb = color.getRGB();
                        for (int i = 0; i < bufferedImageList.size(); i++) {
                            BufferedImage bufferedImage = bufferedImageList.get(i);
                            ColorModel colorModel = bufferedImage.getColorModel();
                            Object dataElements = colorModel.getDataElements(rgb, null);
                            int outputRgb = colorModel.getRGB(dataElements);
                            int x = red + i * 256 + i * sizeSplit;
                            outputBufferedImage2.setRGB(x, y, outputRgb);
                        }
                    }
                }
            }
            ImageIO.write(outputBufferedImage2, "png", FileUtil.getFile(("/home/<USER>/test/fat-service/colorModel/compare/compare.png")));
        }
    }
    
    private void testRbg(int rgb) {
        int red = (rgb >> 16) & 0xff;
        int green = (rgb >> 8) & 0xff;
        int blue = rgb & 0xff;
        int pix = 0;
        int smallestError = Integer.MAX_VALUE;
        int[] lut = this.rgbs;
        int lutrgb;
        for (int i = 0; i < map_size; i++) {
            lutrgb = lut[i];
            if (lutrgb == rgb && lutrgb != 0) {
                pix = i;
                smallestError = 0;
                break;
            }
        }
        
        StringBuilder stringBuilder = new StringBuilder();
        if (smallestError != 0) {
            for (int i = 0; i < map_size; i++) {
                stringBuilder.append("i=").append(i).append(":");
                
                lutrgb = lut[i];
                if (lutrgb == 0) {
                    stringBuilder.append("lutrgb==0").append(";");
                    stringBuilder.append("\n");
                    continue;
                }
                stringBuilder.append("smallestError=").append(smallestError).append(";");
                int lutRed = (lutrgb >> 16) & 0xff;
                stringBuilder.append("Red=").append(String.format("0x%x", lutRed)).append(";");
                int tmp = lutRed - red;
                stringBuilder.append("RedTmp=").append(tmp).append(";");
                int currentError = tmp * tmp;
                stringBuilder.append("RedError=").append(currentError).append(";");
                if (currentError < smallestError) {
                    int lutGreen = (lutrgb >> 8) & 0xff;
                    stringBuilder.append("Green=").append(String.format("0x%x", lutGreen)).append(";");
                    tmp = lutGreen - green;
                    stringBuilder.append("GreenTmp=").append(tmp).append(";");
                    currentError += tmp * tmp;
                    stringBuilder.append("GreenError=").append(currentError).append(";");
                    if (currentError < smallestError) {
                        int lutBlue = lutrgb & 0xff;
                        stringBuilder.append("Blue=").append(String.format("0x%x", lutBlue)).append(";");
                        tmp = lutBlue - blue;
                        stringBuilder.append("BlueTmp=").append(tmp).append(";");
                        currentError += tmp * tmp;
                        stringBuilder.append("BlueError=").append(currentError).append(";");
                        if (currentError < smallestError) {
                            pix = i;
                            stringBuilder.append("pix=").append(pix).append(";");
                            smallestError = currentError;
                        }
                    }
                }
                stringBuilder.append("\n");
            }
        }
        
        System.out.printf("============== 0x%x pix=%d \n%s", rgb, pix, stringBuilder);
        
    }
    
    private int[] rgbs;
    private int map_size;
    
    private IndexColorModel getIndexColorModelAndTestSetRGBs(int bits, int size, byte r[], byte g[], byte b[]) {
        IndexColorModel indexColorModel = new IndexColorModel(bits, size, r, g, b);
        map_size = size;
        rgbs = new int[calcRealMapSize(bits, size)];
        int alpha = 0xff;
        boolean allgray = true;
        for (int i = 0; i < size; i++) {
            int rc = r[i] & 0xff;
            int gc = g[i] & 0xff;
            int bc = b[i] & 0xff;
            allgray = allgray && (rc == gc) && (gc == bc);
            rgbs[i] = (alpha << 24) | (rc << 16) | (gc << 8) | bc;
        }
        return indexColorModel;
    }
    
    private int calcRealMapSize(int bits, int size) {
        int newSize = Math.max(1 << bits, size);
        return Math.max(newSize, 256);
    }
    
    @Test
    public void testOutput216() throws IOException {
        // Create a 6x6x6 color cube
        int[] colorMap = new int[6 * 6 * 6];
        int colorMapIndex = 0;
        for (int r = 0; r < 256; r += 51) {
            for (int g = 0; g < 256; g += 51) {
                for (int b = 0; b < 256; b += 51) {
                    colorMap[colorMapIndex++] = (r << 16) | (g << 8) | b;
                }
            }
        }
    
        IndexColorModel indexColorModel = new IndexColorModel(8, 216, colorMap, 0, false, -1, DataBuffer.TYPE_BYTE);
        
        int px = 10;
        BufferedImage outputBufferedImage = new BufferedImage(
                px * 6 * 36, px * 36 * 6,
                BufferedImage.TYPE_INT_RGB
        );
        
        int xOffset = 0;
        int yOffset = 0;
        for (int r = 0; r < 256; r += 51) {
            for (int g = 0; g < 256; g += 51) {
                xOffset = 0;
                for (int b = 0; b < 256; b += 51) {
                    int rgb = (r << 16) | (g << 8) | b;
                    System.out.printf("#%x ", 0xFF << 24 | (r << 16) | (g << 8) | b);
                    
                    Object dataElements = indexColorModel.getDataElements(rgb, null);
                    int outputRgb = indexColorModel.getRGB(dataElements);
                    for (int i = 0; i < 36 * px; i++) {
                        for (int j = 0; j < 6 * px; j++) {
                            outputBufferedImage.setRGB(i + xOffset, j + yOffset, outputRgb);
                        }
                    }
                    
                    xOffset += 36 * px;
                }
                System.out.println();
                yOffset += 6 * px;
            }
        }
        ImageIO.write(outputBufferedImage, "png", FileUtil.getFile(("/home/<USER>/test/fat-service/colorModel/216.png")));
    }
    
}
