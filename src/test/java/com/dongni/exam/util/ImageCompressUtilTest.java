package com.dongni.exam.util;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.commons.utils.FileUtil;
import com.dongni.commons.utils.HttpRequestUtil;
import com.dongni.exam.gray.enumeration.GrayResultTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2022/12/20
 */
public class ImageCompressUtilTest {
    
    private final static Logger log = LoggerFactory.getLogger(ImageCompressUtilTest.class);
    
    public final static DateTimeFormatter YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /** 测试的文件的存放目录 如果是windows，在当前项目所在盘符下的对应路径 */
    private final static String TEST_LOCAL_DIR_PATH = "/home/<USER>/test/fat-service/compress/";
    
    static File getFile(String url, String saveFileName) {
        File file = FileUtil.getFile(saveFileName);
        if (!file.exists()) {
            HttpRequestUtil.getFile(url, saveFileName);
        }
        return file;
    }
    
    @Test
    public void test8BitTo1Bit() {
        String testFileUrl = "https://cdntest.dongni100.com/test/images/compress/8bit.png";
        String saveFileName = TEST_LOCAL_DIR_PATH + "8bit.png";
        File file = getFile(testFileUrl, saveFileName);
        
        String yyyyMMdd_HHmmss = LocalDateTime.now().format(YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER);
        for (int i = 0; i < 256; i++) {
            ImageCompressPngUtil.blackWhiteGrayThreshold = i;
            GrayResultTypeEnum resultType = ImageCompressUtil.compress(
                    file,
                    new File(saveFileName + "_output_" + yyyyMMdd_HHmmss + "/" + i + ".png ")
            );
            if (resultType != GrayResultTypeEnum.SUCCESS) {
                System.out.println(resultType + " " + i);
            }
        }
    }
    
    @Test
    public void test() {
        String yyyyMMdd_HHmmss = LocalDateTime.now().format(YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER);
        String testLocalDir = TEST_LOCAL_DIR_PATH + yyyyMMdd_HHmmss + "/";
        File testLocalDirFile = FileUtil.getDir(testLocalDir);
        log.info("{}", testLocalDirFile.getAbsolutePath());
        
        List<String> fileUrlList = Stream
                .of(
                        "https://cdntest.dongni100.com/test/images/compress/png_010001_bw1bit_0001.png",
                        "https://cdntest.dongni100.com/test/images/compress/png_040001_gray4bit_0001.png",
                        
                        // png 8bit 转 1bit
                        "https://cdntest.dongni100.com/test/images/compress/png_080001_bw8bit_0001.png",         // 16.84 %
                        
                        // png 24bit 转indexed 8bit PAL8
                        "https://cdntest.dongni100.com/test/images/compress/png_240001_color24bit_0001.png",     // 11.88 %
                        "https://cdntest.dongni100.com/test/images/compress/png_240001_color24bit_0002.png",     // 8.40 %
                        
                        // png 上面那个压的 不需要再压了 因为是indexed的
                        "https://cdntest.dongni100.com/test/images/compress/png_240002_indexed8bit_0001.png",
                        "https://cdntest.dongni100.com/test/images/compress/png_240002_indexed8bit_0002.png",
                        
                        // tif 黑白1位 未使用压缩算法 使用T6压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_01010001_bw1bit_NONE_0001.TIF",  // 11.43 %
                        // tif 黑白1位 T6压缩算法  不需要压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_01040000_bw1bit_T6.TIF",

                        // tif 黑白1位 LZW压缩算法  使用T6压缩后占用空间约为原来的52%
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050000_bw1bit_lzw.tif",        // 47.78 %
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050001_bw1bit_LZW_0001.TIF",   // 52.24 %
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050001_bw1bit_LZW_0002.TIF",   // 52.40 %

                        // tif 灰度4位 LZW压缩算法   不需要压缩              非黑白的图片有使用压缩算的不需要进行压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_04050001_gray4bit_LZW_0001.TIF",
                        "https://cdntest.dongni100.com/test/images/compress/tif_04050001_gray4bit_LZW_0002.TIF",

                        // tif 彩色24位 未使用压缩算法  使用LZW对其进行压缩后占用空间约为原来的8.47%
                        "https://cdntest.dongni100.com/test/images/compress/tif_24010001_color24bit_NONE.tif",   // 8.47 %
                        // tif 彩色24位 JPEG压缩算法 整张图片只有红黑白三色   非黑白的图片有使用压缩算的不需要进行压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_24070001_color24bit_rbw_JPEG_0001.tif",
                        "https://cdntest.dongni100.com/test/images/compress/tif_24070001_color24bit_rbw_JPEG_0002.tif",
                        
                        null
                )
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Integer> localFileName2Count = new HashMap<>();
        for (String fileUrl : fileUrlList) {
            // test.png
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            // .png
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            // test
            String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf("."));
            int count = localFileName2Count.merge(fileName, 1, Integer::sum);
            if (count > 1) {
                // test___2.png  test___3.png  ...
                fileName = fileNameWithoutExtension + "___" + count + fileExtension;
            }
            
            String saveFileName = testLocalDir + fileName;
            try {
                File scrFile = getFile(fileUrl, saveFileName);
                File compressFile = new File(saveFileName + "_output" + fileExtension);
                GrayResultTypeEnum resultType = ImageCompressUtil.compress(scrFile, compressFile);
                
                if (resultType == GrayResultTypeEnum.SUCCESS) {
                    long srcSize = scrFile.length();
                    long compressSize = compressFile.length();
                    log.info("{} : {} : {} : {} / {} = {}",
                            fileUrl, fileName, resultType,
                            DisplayUtil.storageSize(compressSize),
                            DisplayUtil.storageSize(srcSize),
                            DisplayUtil.percent(compressSize, srcSize)
                    );
                } else if (resultType == GrayResultTypeEnum.POOR_EFFECT) {
                    long srcSize = scrFile.length();
                    long compressSize = compressFile.length();
                    log.warn("{} : {} : {} : {} / {} = {}",
                            fileUrl, fileName, resultType,
                            DisplayUtil.storageSize(compressSize),
                            DisplayUtil.storageSize(srcSize),
                            DisplayUtil.percent(compressSize, srcSize)
                    );
                    compressFile.renameTo(new File(saveFileName + "_output_★★★★POOR_EFFECT★★★★" + fileExtension));
                } else {
                    log.error("{} : {} : {}", fileUrl, fileName, resultType);
                    File errorFile = new File(saveFileName + "_output_★★★★" + resultType +  "★★★★" + fileExtension);
                    ImageIO.write(new BufferedImage(1, 1, BufferedImage.TYPE_BYTE_BINARY), "png", errorFile);
                }
            } catch (Exception e) {
                log.error("{} : {}", fileUrl, fileName, e);
            }
        }
    }
}
