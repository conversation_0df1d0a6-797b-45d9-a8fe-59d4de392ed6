package com.dongni.exam.util;

import com.dongni.common.utils.DisplayUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.FileUtil;
import com.dongni.commons.utils.HttpRequestUtil;
import com.dongni.commons.utils.IoUtil;
import com.dongni.exam.card.util.ImageUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 * 2022/12/27
 */
public class TifToPngTest {
    
    private final static Logger log = LoggerFactory.getLogger(TifToPngTest.class);
    
    public final static DateTimeFormatter YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    
    /** 测试的文件的存放目录 如果是windows，在当前项目所在盘符下的对应路径 */
    private final static String TEST_LOCAL_DIR_PATH = "/home/<USER>/test/fat-service/tif2png/";
    
    static File getFile(String url, String saveFileName) {
        File file = FileUtil.getFile(saveFileName);
        if (!file.exists()) {
            HttpRequestUtil.getFile(url, saveFileName);
        }
        return file;
    }
    
    @Test
    public void test() {
        String yyyyMMdd_HHmmss = LocalDateTime.now().format(YYYYMMDD_HHMMSS_DATE_TIME_FORMATTER);
        String testLocalDir = TEST_LOCAL_DIR_PATH + yyyyMMdd_HHmmss + "/";
        File testLocalDirFile = FileUtil.getDir(testLocalDir);
        log.info("{}", testLocalDirFile.getAbsolutePath());
        
        List<String> fileUrlList = Stream
                .of(
                        // png 8bit 转 1bit
                        "https://cdntest.dongni100.com/test/images/compress/png_08000001_8bit.png",
                    
                        // tif 黑白1位 未使用压缩算法 使用T6压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_01010001_bw1bit_NONE_0001.TIF",  // 11.43 %
                        // tif 黑白1位 T6压缩算法  不需要压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_01040000_bw1bit_T6.TIF",
                    
                        // tif 黑白1位 LZW压缩算法  使用T6压缩后占用空间约为原来的52%
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050000_bw1bit_lzw.tif",        // 47.78 %
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050001_bw1bit_LZW_0001.TIF",   // 52.24 %
                        "https://cdntest.dongni100.com/test/images/compress/tif_01050001_bw1bit_LZW_0002.TIF",   // 52.40 %
                    
                        // tif 灰度4位 LZW压缩算法   不需要压缩              非黑白的图片有使用压缩算的不需要进行压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_04050001_gray4bit_LZW_0001.TIF",
                        "https://cdntest.dongni100.com/test/images/compress/tif_04050001_gray4bit_LZW_0002.TIF",
                    
                        // tif 彩色24位 未使用压缩算法  使用LZW对其进行压缩后占用空间约为原来的8.47%
                        "https://cdntest.dongni100.com/test/images/compress/tif_24010001_color24bit_NONE.tif",   // 8.47 %
                        // tif 彩色24位 JPEG压缩算法 整张图片只有红黑白三色   非黑白的图片有使用压缩算的不需要进行压缩
                        "https://cdntest.dongni100.com/test/images/compress/tif_24070001_color24bit_rbw_JPEG_0001.tif",
                        "https://cdntest.dongni100.com/test/images/compress/tif_24070001_color24bit_rbw_JPEG_0002.tif",
                    
                        null
                )
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Map<String, Integer> localFileName2Count = new HashMap<>();
        for (String fileUrl : fileUrlList) {
            // test.png
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            // .png
            String fileExtension = fileName.substring(fileName.lastIndexOf("."));
            // test
            String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf("."));
            int count = localFileName2Count.merge(fileName, 1, Integer::sum);
            if (count > 1) {
                // test___2.png  test___3.png  ...
                fileName = fileNameWithoutExtension + "___" + count + fileExtension;
            }
        
            String saveFileName = testLocalDir + fileName;
            try {
                File tifFile = getFile(fileUrl, saveFileName);
                String imageType = ImageUtil.getImageType(tifFile);
                if (imageType == null) {
                    log.error("文件不是图片格式: {} : {}", fileUrl, fileName);
                    continue;
                }
                if (ImageUtil.isPngByType(imageType)) {
                    log.info("图片格式本来就是png: {} : {}", fileUrl, fileName);
                    continue;
                }
                if (!ImageUtil.isTifByType(imageType)) {
                    log.warn("图片格式不是tif格式: {} : {}", fileUrl, fileName);
                    continue;
                }
                
                File pngFile = new File(saveFileName + ".png");
                
                FileOutputStream fileOutputStream = null;
                try {
                    BufferedImage bufferedImage = ImageIO.read(tifFile);
                    fileOutputStream = new FileOutputStream(pngFile);
                    ImageIO.write(bufferedImage, "png", fileOutputStream);
                } finally {
                    IoUtil.close(fileOutputStream);
                }
                log.info("tif转png: {} : {} -> {}", fileUrl, fileName, pngFile.getName());
    
                File jpgFile = new File(saveFileName + ".jpg");
                
                try {
                    BufferedImage bufferedImage = ImageIO.read(tifFile);
                    fileOutputStream = new FileOutputStream(jpgFile);
                    ImageIO.write(bufferedImage, "jpeg", fileOutputStream);
                } finally {
                    IoUtil.close(fileOutputStream);
                }
                log.info("tif转jpg: {} : {} -> {}", fileUrl, fileName, jpgFile.getName());
                
                log.info("tif: {}; png: {}; jpg: {}",
                        DisplayUtil.storageSize(tifFile.length()),
                        DisplayUtil.storageSize(pngFile.length()),
                        DisplayUtil.storageSize(jpgFile.length())
                );
            } catch (Exception e) {
                log.error("{} : {}", fileUrl, fileName, e);
            }
        }
    }
    
    /**
     * 将tif格式图像转化为png格式
     *
     * @param rootPath 生成的文件根目录
     * @param originFile 源文件
     */
    public static File convertTifToPng(String rootPath, File originFile) {
        BufferedImage bufferedImage;
        FileOutputStream fileOutputStream = null;
        File file;
        try {
            bufferedImage = ImageIO.read(originFile);
            // 用tif结尾在浏览器也能查看，这样能避免修改原文件名导致的oss异常等问题
            file = Paths.get(rootPath, System.currentTimeMillis() + ".tif").toFile();
            fileOutputStream = new FileOutputStream(file);
            ImageIO.write(bufferedImage, "png", fileOutputStream);
        } catch (IOException e) {
            throw new CommonException(ResponseStatusEnum.FILE_ERROR, "tif转png失败", e);
        } finally {
            IoUtil.close(fileOutputStream);
        }
        return file;
    }
}
