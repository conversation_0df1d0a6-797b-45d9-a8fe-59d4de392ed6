package com.dongni.exam.filevacuum.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * 2023/01/30
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageVacuumAnswerCardCorrectOldServiceTest {
    
    @Autowired
    private FileStorageVacuumAnswerCardCorrectOldService fileStorageVacuumAnswerCardCorrectOldService;
    
    @Test
    public void test() {
        fileStorageVacuumAnswerCardCorrectOldService.vacuumTask(null);
    }
}
