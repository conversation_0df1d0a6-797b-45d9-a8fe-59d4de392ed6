package com.dongni.exam.filevacuum.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class FileStorageVacuumAnswerCardServiceTest {
    
    @Autowired
    private FileStorageVacuumAnswerCardService fileStorageVacuumAnswerCardService;
    
    @Test
    public void test() {
        fileStorageVacuumAnswerCardService.vacuumTask(false, null);
    }
}
