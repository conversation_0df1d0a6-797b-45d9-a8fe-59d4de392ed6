package com.dongni.exam.answercard;

import cn.hutool.core.util.ReUtil;
import org.junit.Test;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:34
 */

public class CompositionTrainTest2 {

    @Test
    public void test() {
        String a = "upload/answerCard/\\d+/\\d+/\\d+/\\d+/\\d+/([0-9,]+/)?(renew/[0-9]+/)?[a-zA-Z0-9-_]+/studentNumber[0-9-]*\\.png";
        String b = "upload/answerCard/2024/4/2/1/2/2_482945_LJOBJ7J8_5_DN0705000019/studentNumber.png";
        System.out.println(ReUtil.isMatch(a, b));
    }
}
