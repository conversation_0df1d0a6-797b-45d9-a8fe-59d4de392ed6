/*
package com.dongni.exam.answercard;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.dongni.config.clean.SeaweedfsRes;
import com.dongni.config.clean.SeaweedfsRes.Entry;
import com.esslib.cover.oss.aliyun.oss.OSS;
import com.esslib.cover.oss.aliyun.oss.OSSClientBuilder;
import com.esslib.cover.oss.aliyun.oss.model.ListObjectsV2Request;
import com.esslib.cover.oss.aliyun.oss.model.ListObjectsV2Result;
import com.esslib.cover.oss.aliyun.oss.model.OSSObjectSummary;
import com.google.common.collect.Lists;
import com.hqjl.common.exception.HqjlRunTimeException;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

*/
/**
 * <AUTHOR>
 * @date 2024/9/26 10:31
 *//*


public class AnswerCardCleanSimpleTest {

    @Test
    public void clean() {
        OSS ossClient = new OSSClientBuilder().build("https://".concat("oss-cn-shenzhen.aliyuncs.com"), "Q45J2pUct7bl4ILE", "bgUgdLh83RP570bpuJavdhFW2VAiXf");
        ListObjectsV2Request listObjectsV2Request = new ListObjectsV2Request("dongni-product").withMaxKeys(500);
        listObjectsV2Request.setPrefix("upload/answerCard/2024/1/");
        listObjectsV2Request.setContinuationToken(null);
        ListObjectsV2Result result = ossClient.listObjectsV2(listObjectsV2Request);
        List<OSSObjectSummary> objectSummaries = result.getObjectSummaries();
        result.getContinuationToken();
    }

    @Test
    public void clean2() {
        boolean success = false;
        Exception exception = null;
        for (int i = 0; i < 3; i++) {
            try {
                int a = 1 / 0;
                success = true;
                break;
            } catch (Exception e) {
                try {
                    TimeUnit.SECONDS.sleep(2);
                } catch (InterruptedException interruptedException) {
                    throw new HqjlRunTimeException("答题卡同步程序:线程中断异常", interruptedException);
                }
                exception = e;
            }
        }
        if (!success) {
            throw new HqjlRunTimeException("答题卡同步程序:数据插入异常", exception);
        }
    }

    private boolean aa(int a) {
        if (a > 3) {
            return false;
        }
        ++a;
        aa(a);
        return true;
    }


    @Test
    public void test() {
        List<Entry> entryList = new ArrayList<>();
        getAllFiles("/upload/answerCard/2024/10/15/339/3006/",
                entryList,
                new AtomicReference<>(""), 1);
        System.out.println(entryList);
        //seaWeed返回的文件路径前面都有/
        ArrayList<Entry> newArrayList = Lists.newArrayList(entryList);
        entryList.clear();
        System.out.println(newArrayList);
    }

    */
/**
     * @param path
     * @param entryList
     * @param startPath 文件开始位置，前面必须要有/
     * @param startSign
     *//*

    private void getAllFiles(String path, List<Entry> entryList, AtomicReference<String> startPath, int startSign) {
        List<Entry> entries = listFiles(path);
        for (Entry entry : entries) {
            if (startSign == 1 && StringUtils.isNoneBlank(startPath.get())) {
                if (startPath.get().contains(entry.getFullPath())) {
                    startSign = 0;
                } else {
                    continue;
                }
            }
            //开始文件之后的才进行保存
            if (StringUtils.isNoneBlank(startPath.get()) && entry.getFullPath().contains(startPath.get())) {
                startPath.set("");
                continue;
            }
            if (entry.getFileSize() == 0L) {
                getAllFiles(entry.getFullPath(), entryList, startPath, startSign);
            } else if (StringUtils.isBlank(startPath.get())) {
                entryList.add(entry);
                if (entryList.size() == 500) {
                    //todo 保存数据库
                }
            }
        }
    }

    private List<Entry> listFiles(String path) {
        String url = "https://dongnitest5.dongni100.com/dnfiler/filer/" + path + "?limit=100";
        List<Entry> entryList = new ArrayList<>();
        String requestUrl = url;
        while (true) {
            int count = 1;
            String body = "";
            while (count <= 3) {
                try {
                    body = HttpRequest.get(requestUrl)
                            .header("dongni-test5", "c23e5957fdad498287962cd963180d41656df921ed5e4f3383a108f673fab7dfc0607d5427234172820922361fe48ad4")//头信息，多个头信息多次调用此方法即可
                            .header("Accept", "application/json")
                            .execute().body();
                    break;
                } catch (Exception e) {
                    ++count;
                }
            }
            if (StringUtils.isNoneBlank(body)) {
                SeaweedfsRes seaweedfsRes = JSON.parseObject(body, SeaweedfsRes.class);
                Boolean shouldDisplayLoadMore = Optional.ofNullable(seaweedfsRes.getShouldDisplayLoadMore()).orElse(false);
                entryList.addAll(Optional.ofNullable(seaweedfsRes.getEntries()).orElse(Collections.emptyList()));
                if (!shouldDisplayLoadMore) {
                    break;
                } else {
                    requestUrl = url + "&" + "lastFileName=" + seaweedfsRes.getLastFileName();
                }
            } else {
                break;
            }
        }
        return entryList;
    }


}
*/
