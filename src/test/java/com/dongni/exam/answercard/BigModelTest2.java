package com.dongni.exam.answercard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hqjl.aicenterclient.bean.vo.DigestResult;
import com.hqjl.aicenterclient.utils.auth.DigestUtils;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.junit.Test;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/25 18:27
 */

public class BigModelTest2 {

    @Test
    public void test() {
        DigestResult digestResult = DigestUtils.generateDigest("fat-service", "76b890ecde8585ac47eb45ce93fafe2b", System.currentTimeMillis());
        OkHttpClient client = new OkHttpClient()
                .newBuilder()
                .readTimeout(1, TimeUnit.MINUTES)
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        String parameter = JSON.toJSONString(Collections.singletonMap("parameter", digestResult));
        RequestBody body = RequestBody.create(mediaType, parameter);
        Request request = new Request.Builder()
                .url("https://newcollector.hqjltech.com/aicenterserv/auth/getAuthCode")
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        try {
            Response response = client.newCall(request).execute();
            String string = response.body().string();
            JSONObject jsonObject = JSON.parseObject(string);
            Integer code = jsonObject.getInteger("code");
            if (code == 0) {
                JSONObject result = jsonObject.getJSONObject("result");
                System.out.println(result.getString("authCode"));
            } else {

                throw new RuntimeException("智能批改获取认证状态存在问题");
            }
        } catch (Exception e) {
            throw new RuntimeException("智能批改获取认证失败", e);
        }
    }


    @Test
    public void test2() {
    }


}
