package com.dongni.exam.answercard;

import com.alibaba.fastjson.JSON;
import com.dongni.exam.mark.bigmodel.bean.AiExamQuestion;
import com.dongni.exam.mark.bigmodel.service.BigModelService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025/4/25 18:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BigModelTest {
    @Autowired
    private BigModelService bigModelService;

    static String json = "{\n" +
            "  \"examId\": 38,\n" +
            "  \"paperReadId\": 72,\n" +
            "  \"examItemId\": 46,\n" +
            "  \"unitType\": 4,\n" +
            "\"url\":\"33_acv2/answerCard/2025/4/25/138073818/1157874/992889_A3/1-2_8.jpg\",\n" +
            "  \"questionUrl\": \"33_acv2/answerCard/2025/4/25/138073818/1157874/992889_A3/1-2_8.jpg\",\n" +
            "  \"gradeType\": 10,\n" +
            "  \"courseId\": 34,\n" +
            "  \"courseName\": \"语文\",\n" +
            "  \"questionDesc\": \"\",\n" +
            "  \"markStd\": \"\",\n" +
            "  \"markStdUrl\": \"33_acv2/answerCard/2025/4/25/138073818/1157874/992889_A3/1-2_8.jpg\"\n" +
            "}";

    @Test
    public void test() {
        AiExamQuestion aiExamQuestion = JSON.parseObject(json, AiExamQuestion.class);
        aiExamQuestion.setScoreValue(10.0);
        bigModelService.scheduledAuth();
        bigModelService.aiReadOver(Collections.singletonList(aiExamQuestion));
    }



}
