package com.dongni.exam.answercard;

import com.dongni.common.listener.AnswerCardUtil;
import com.dongni.exam.mark.ai.composition.entity.TrainDTO;
import com.dongni.exam.mark.ai.composition.service.CompositionTrainService;
import com.dongni.exam.newcard.dao.CompositionTrainMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/10/24 16:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CompositionTrainTest {
    @Autowired
    private CompositionTrainService compositionTrainService;
    @Autowired
    private CompositionTrainMapper mapper;

    @Test
    public void test1() {
        String url = "{\"fileSuffixList\":[\"cardContent-1-1732617377538.jpg\",\"cardContent-1-1732617382113.jpg\"],\"folder\":\"upload/answerCard/2024/11/26/660634/677193/662287_201090101_LHO80WYO_1_DN0515000001/\"}";
        File stitchingImage = AnswerCardUtil.getStitchingImage(url, 0.8d);
    }

    @Test
    public void test() {
        ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(5);
        scheduledExecutorService.scheduleWithFixedDelay((() -> compositionTrainService.getTrainResult()), 1, 3, java.util.concurrent.TimeUnit.SECONDS);
        TrainDTO para = new TrainDTO();
        para.setRandom(true);
        para.setPageSize(10);
        para.setExecuteSign("1029");
        compositionTrainService.trainGoodExamData(para);
        while (true) {
            try {
                TimeUnit.MINUTES.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (compositionTrainService.getQueueSize() == 0) {
                return;
            }
        }
    }
}
