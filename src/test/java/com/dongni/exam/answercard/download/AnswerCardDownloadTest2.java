package com.dongni.exam.answercard.download;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> <br>
 * 2022/01/20 <br>
 * 用于下载答题卡
 *   不依赖于环境的 需提供完整的路径
 */
public class AnswerCardDownloadTest2 {
    
    RestTemplate restTemplate = new RestTemplate();
    
    @Test
    public void downloadReportAnswerCard() {
        
        List<String> filePathList = Stream.of(
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001299.TIF",
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001300.TIF",
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001301.TIF",
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001302.TIF",
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001303.TIF",
                "upload/answerCard/2022/1/17/510/986/-1999887350_DN0116001304.TIF",
        null
        ).collect(toList());
        
        String sheetName = "861-3";
        String host = "http://dalao.dongni100.com:60826/filer/";
        String targetPath = "/home/<USER>/download/answerCard/" + sheetName + "/";
        boolean isForceReplace = false;
        List<Map<String, Object>> fileInfos = filePathList.stream()
                .filter(StringUtils::isNotBlank)
                .<Map<String, Object>>map(filePath -> MapUtil.of("filePath", host + filePath))
                .collect(toList());
    
        boolean allSuccess = downloadFileByPaths(fileInfos, targetPath, isForceReplace);
        if (!allSuccess) {
            throw new RuntimeException("抛个异常，请检查打印信息");
        }
    }
    
    /**
     * 下载文件
     * @param fileInfos 文件信息
     *                   filePath      需要下载的文件 存放于cdn上 注意选对环境
     *                   [folderName]  文件存放在哪个文件夹下，因为可能根据科目进行存放
     *                   [fileName]    文件名 不含后缀，后缀由filePath决定
     * @param targetPath 待存放的文件目录路径，必须以 / 结尾   会拼上fileInfos.item的folderName
     *                   例: /home/<USER>/myDownload/
     *                   fileInfos.item.folderName = null   -> /home/<USER>/myDownload/
     *                   fileInfos.item.folderName = "  "   -> /home/<USER>/myDownload/
     *                   fileInfos.item.folderName = abc    -> /home/<USER>/myDownload/abc/
     * @param isForceReplace 是否重新下载覆盖
     */
    private boolean downloadFileByPaths(List<Map<String, Object>> fileInfos,
                                     String targetPath,
                                     boolean isForceReplace) {
        if (fileInfos.size() < 1) {
            throw new CommonException(ResponseStatusEnum.OSS_ERROR, "没有待识别的文件");
        }
        if (!StringUtils.endsWith(targetPath, "/")) {
            throw new CommonException(ResponseStatusEnum.OSS_ERROR, "存放目录必须以 / 字符结尾");
        }
        
        Map<String, String> notReplace = new ConcurrentHashMap<>();
        Map<String, String> failed = new ConcurrentHashMap<>();
        
        fileInfos.stream().forEach(fileInfo -> {
            final String filePath = fileInfo.get("filePath").toString();
            String fileName = (String) fileInfo.get("fileName");
            String folderName = (String) fileInfo.get("folderName");
            
            // .jpg  .TIF  .png
            String fileExtension = filePath.substring(filePath.lastIndexOf("."));
            String folderPath = "";
            if (StringUtils.isNotBlank(folderName)) {
                folderPath = folderName.trim() + "/";
            }
            
            // 不含后缀的文件名 如果不提供就在下载路径上取
            if (StringUtils.isBlank(fileName)) {
                fileName = filePath.substring(filePath.lastIndexOf("/") + 1, filePath.lastIndexOf("."));
            }
            
            String savePath = targetPath + folderPath + fileName + fileExtension;
            if (!isForceReplace) {
                File file = new File(savePath);
                if (file.exists()) {
                    notReplace.put(filePath, savePath);
                    return;
                }
            }
            
            try {
                downloadFile(filePath, savePath);
                System.out.println("download: " + filePath + " -> " + savePath);
            } catch (Exception e) {
                failed.put(filePath, e.getMessage());
            }
        });
        
        if (MapUtils.isNotEmpty(notReplace)) {
            System.out.println("\n ============================== 文件已存在不再下载的 ");
            notReplace.forEach((key, value) -> System.out.println("unDownload: " + key + " -> " + value));
        }
        
        if (MapUtils.isNotEmpty(failed)) {
            System.out.println("\n ============================== 下载异常的 ");
            failed.forEach((key, value) -> System.out.println("exception: " + key + " -> " + value));
        }
        
        return MapUtils.isEmpty(failed) && MapUtils.isEmpty(notReplace);
    }
    
    private void downloadFile(String filePath, String localFilePath) throws IOException {
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            // 获取目标资源
            ResponseEntity<byte[]> response = restTemplate.exchange(filePath, HttpMethod.GET, null, byte[].class);
            if (response.getBody() == null) {
                throw new RuntimeException("接口返回byte[]为空");
            }
            if (localFilePath.endsWith("/") || localFilePath.endsWith("\\")) {
                String filename = filePath.substring(filePath.lastIndexOf("/"));
                localFilePath = localFilePath + filename;
            }
            
            // 临时文件存储
            File file = new File(localFilePath);
            File parentFile = file.getParentFile();
            if (!parentFile.exists()) {
                if (!parentFile.mkdirs()) {
                    if (!parentFile.exists()) {
                        throw new CommonException(ResponseStatusEnum.FAILURE, "文件夹创建错误");
                    }
                }
            }
            
            // 写入文件
            inputStream = new ByteArrayInputStream(response.getBody());
            outputStream = new BufferedOutputStream(new FileOutputStream(file));
            byte[] buffer = new byte[8192];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    LoggerFactory.getLogger(getClass()).error(e.getMessage(), e);
                }
            }
        }
        
    }
}
