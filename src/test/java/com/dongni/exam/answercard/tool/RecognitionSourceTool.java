package com.dongni.exam.answercard.tool;

import com.dongni.exam.newcard.bean.DO.AnswerCardDO;
import com.dongni.exam.newcard.bean.DTO.DebugExamDTO;
import com.dongni.exam.newcard.service.NewAnswerCardZipService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * <AUTHOR> <br>
 * 2022/01/20 <br>
 * 用于下载答题卡
 *   不依赖于环境的 需提供完整的路径
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class RecognitionSourceTool {

    RestTemplate restTemplate = new RestTemplate();

    @Autowired
    private NewAnswerCardZipService newAnswerCardZipService;

    /**
     * RecAnswerCardInfo 结构体
     */
    public static class RecAnswerCardInfo {
        private Long examUploaderId;
        private Long paperId;
        private Long templateCode;
        private String answerFileNames;  // 多张图片名逗号分隔

        // --- getters & setters ---
        public Long getExamUploaderId() { return examUploaderId; }
        public void setExamUploaderId(Long examUploaderId) { this.examUploaderId = examUploaderId; }
        public Long getPaperId() { return paperId; }
        public void setPaperId(Long paperId) { this.paperId = paperId; }
        public Long getTemplateCode() { return templateCode; }
        public void setTemplateCode(Long templateCode) { this.templateCode = templateCode; }
        public String getAnswerFileNames() { return answerFileNames; }
        public void setAnswerFileNames(String answerFileNames) { this.answerFileNames = answerFileNames; }

        @Override
        public String toString() {
            return "RecAnswerCardInfo{" +
                    "examUploaderId=" + examUploaderId +
                    ", paperId=" + paperId +
                    ", templateCode=" + templateCode +
                    ", answerFileNames='" + answerFileNames + '\'' +
                    '}';
        }
    }
    @Test
    public void generateSourceAnswerCardUpdSql() throws IOException {
        List<RecAnswerCardInfo> list = processLikelyBlankFiles("D:\\好专业\\项目\\2025项目\\大规模测试\\testinput.txt");
        StringBuffer examUploaderIds = new StringBuffer();
        examUploaderIds.append("(");
        for(RecAnswerCardInfo info : list){
            examUploaderIds.append(info.getExamUploaderId() + ",");
        }
        examUploaderIds.deleteCharAt(examUploaderIds.length()-1).append(")");
        System.out.println("examUploaderIds:\n"+examUploaderIds + "\n" );

        StringBuffer sql = new StringBuffer();
        for(RecAnswerCardInfo info : list){
            if(info.getTemplateCode() !=null) {
                sql.append("UPDATE t_exam_test_task_allblank s SET s.recognize_filenames = '" + info.getAnswerFileNames() + "' where exam_uploader_id = " + info.getExamUploaderId()  + " and template_code = " + info.getTemplateCode()+ ";")
                        .append("\n");
            }else if(info.getPaperId() != null){
                sql.append("UPDATE t_exam_test_task_allblank s SET s.recognize_filenames = '" + info.getAnswerFileNames() + "' where exam_uploader_id = " + info.getExamUploaderId() + " and paper_id = " + info.getPaperId() + ";")
                        .append("\n");
            }else {
                System.err.println("格式不对: " + info);
            }
        }
        RecognitionDebugTool.bufferedWriteFile("D:\\好专业\\项目\\2025项目\\大规模测试\\疑似空白卷sql.txt", sql.toString());

    }

    /**
     * 主处理函数
     * @param filePath 输入文件路径
     * @return 每场考试对应的 RecAnswerCardInfo 列表
     */
    public List<RecAnswerCardInfo> processLikelyBlankFiles(String filePath) throws IOException {
        // 用于聚合——key 格式 examUploaderId+"_"+(paperId!=null?paperId:templateCode)
        Map<String, RecAnswerCardInfoBuilder> builderMap = new LinkedHashMap<>();

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty()) continue;

                String[] parts = line.split("_");
                if (parts.length < 4) continue;  // 格式不对就跳过

                // 1. 解析 examUploaderId
                Long examUploaderId = Long.valueOf(parts[1]);

                // 2. 第3段：根据长度判断 paperId / templateCode
                String idPart = parts[2];
                Long paperId = null;
                Long templateCode = null;
                if (idPart.length() > 10) {
                    paperId = Long.valueOf(idPart);
                } else {
                    templateCode = Long.valueOf(idPart);
                }
                final Long paperId2 = paperId;
                final Long templateCode2 = templateCode;

                // 3. 拼接出文件名（第4段开始直到末尾，以 "_" 再拼回来）
                String fileName = String.join("_", Arrays.copyOfRange(parts, 3, parts.length));

                // 4. 构造 DebugExamDTO 并调用外部方法拿到这张答题卡的 AnswerCardDO 列表
                DebugExamDTO dto = new DebugExamDTO();
                dto.setExamUploaderId(examUploaderId);
                if(paperId != null) {
                    dto.setPaperId(paperId);
                }
                if(templateCode != null) {
                    dto.setTemplateCode(templateCode);
                }
                dto.setAnswerFileName(fileName);

                List<AnswerCardDO> cards = newAnswerCardZipService.getCardList(dto);

                // 5. 聚合到同一个考试
                String key = examUploaderId + "_" + (paperId != null ? paperId : templateCode);
                RecAnswerCardInfoBuilder b = builderMap.computeIfAbsent(key, k -> {
                    RecAnswerCardInfoBuilder nb = new RecAnswerCardInfoBuilder();
                    nb.info.setExamUploaderId(examUploaderId);
                    nb.info.setPaperId(paperId2);
                    nb.info.setTemplateCode(templateCode2);
                    return nb;
                });

                for (AnswerCardDO card : cards) {
                    b.append(card.getFileName());
                }
            }
        }

        // 构造最终结果
        List<RecAnswerCardInfo> result = new ArrayList<>();
        for (RecAnswerCardInfoBuilder b : builderMap.values()) {
            result.add(b.build());
        }
        return result;
    }

    /**
     * 辅助 builder，用来累积文件名并最终输出 RecAnswerCardInfo
     */
    private static class RecAnswerCardInfoBuilder {
        RecAnswerCardInfo info = new RecAnswerCardInfo();
        Set<String> fileNameSet = new LinkedHashSet<>();

        void append(String fileName) {
            fileNameSet.add(fileName);
        }

        RecAnswerCardInfo build() {
            String result = String.join(",", fileNameSet);
            info.setAnswerFileNames(result);
            return info;
        }
    }

}
