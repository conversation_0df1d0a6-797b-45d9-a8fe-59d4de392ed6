package com.dongni.exam.answercard.tool;

import com.dongni.commons.utils.spring.SpringContextUtil;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

public class RecognitionDebugTool {

    private static String strPatternStr1 = "[a-zA-Z0-9]+";
    private static String strKeyPatternStr = "@String\\[(.*)\\]:";
    private static String strValuePatternStr = ":@String\\[(.*)\\]";
    private static String intPatternStr = "@Integer\\[(.*)\\]";
    private static String longPatternStr = "@Long\\[(.*)\\]";
    private static String doublePatternStr = "@Double\\[(.*)\\]";

    public static void mkdirOCR(){

        System.out.println("创建手写考号目录");
        File data = new File( "/data/");
        if(!data.exists()){
            data.mkdirs();
        }
        File alldnrecognitiontestcase = new File( "/data/alldnrecognitiontestcase/");
        if(!alldnrecognitiontestcase.exists()){
            alldnrecognitiontestcase.mkdirs();
        }

        System.out.println("创建手写考号目录----grid");

        String handWriteNumber = "/data/alldnrecognitiontestcase/grid/";
        File handWriteNumberfile = new File(handWriteNumber );
        if(!handWriteNumberfile.exists()){
            handWriteNumberfile.mkdirs();
        }

        String handWriteNumber2 = "/data/alldnrecognitiontestcase/grid/same/";
        File handWriteNumber2file = new File(handWriteNumber2 );
        if(!handWriteNumber2file.exists()){
            handWriteNumber2file.mkdirs();
        }

        String handWriteNumber23 = "/data/alldnrecognitiontestcase/grid/notsame/";
        File handWriteNumber2file3 = new File(handWriteNumber23 );
        if(!handWriteNumber2file3.exists()){
            handWriteNumber2file3.mkdirs();
        }


        String handWriteNumber1 = "/data/alldnrecognitiontestcase/nogrid/";
        File handWriteNumberfilea = new File(handWriteNumber1 );
        if(!handWriteNumberfilea.exists()){
            handWriteNumberfilea.mkdirs();
        }

        String handWriteNumber2same = "/data/alldnrecognitiontestcase/nogrid/same/";
        File handWriteNumber2filesame = new File(handWriteNumber2same );
        if(!handWriteNumber2filesame.exists()){
            handWriteNumber2filesame.mkdirs();
        }

        String handWriteNumber23not = "/data/alldnrecognitiontestcase/nogrid/notsame/";
        File handWriteNumber2file3notsame = new File(handWriteNumber23not );
        if(!handWriteNumber2file3notsame.exists()){
            handWriteNumber2file3notsame.mkdirs();
        }

        String handWriteNumber2same2 = "/data/alldnrecognitiontestcase/nogrid/fuzzymatch/";
        File handWriteNumber2filesame3 = new File(handWriteNumber2same2 );
        if(!handWriteNumber2filesame3.exists()){
            handWriteNumber2filesame3.mkdirs();
        }

        File handWriteNumber2file3notsame3 = new File("/data/alldnrecognitiontestcase/nogrid/fuzzymatch/same" );
        if(!handWriteNumber2file3notsame3.exists()){
            handWriteNumber2file3notsame3.mkdirs();
        }
        File handWriteNumber2file3notsame35 = new File("/data/alldnrecognitiontestcase/nogrid/fuzzymatch/notsame" );
        if(!handWriteNumber2file3notsame35.exists()){
            handWriteNumber2file3notsame35.mkdirs();
        }

        File handWriteNumber2filesame34 = new File("/data/alldnrecognitiontestcase/grid/fuzzymatch/" );
        if(!handWriteNumber2filesame34.exists()){
            handWriteNumber2filesame34.mkdirs();
        }

        File handWriteNumber2file3notsame34 = new File("/data/alldnrecognitiontestcase/grid/fuzzymatch/same" );
        if(!handWriteNumber2file3notsame34.exists()){
            handWriteNumber2file3notsame34.mkdirs();
        }

        File handWriteNumber2file3notsame345 = new File("/data/alldnrecognitiontestcase/grid/fuzzymatch/notsame" );
        if(!handWriteNumber2file3notsame345.exists()){
            handWriteNumber2file3notsame345.mkdirs();
        }

    }


    public static void copyFilehandwriterGrid(String srcFilePath, String destDir, String newFileName,int resultorvalue){
        File srcFile = new File(srcFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(StringUtils.isEmpty(newFileName)){
            return;
        }

        if(resultorvalue==1){

            String destFilePath2 = "/data/alldnrecognitiontestcase/grid/same/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }


            try {
                if (srcFile.exists()) {
                    System.out.println("手写考号复制图片:");

                    Files.copy(srcFile.toPath(), destFile2.toPath());


                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else{


            String destFilePath2 = "/data/alldnrecognitiontestcase/grid/notsame/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }

            String destFilePath24 = "F:\\aquestion\\ahandwritenumber\\test4\\notsame\\" +newFileName;
            File destFile24 = new File(destFilePath24);


            try {
                if (srcFile.exists()) {
                    System.out.println("手写考号复制图片:");
                    Files.copy(srcFile.toPath(), destFile2.toPath());

                    Files.copy(srcFile.toPath(), destFile24.toPath());

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    public static void copyFilehandwriterNoGrid(String srcFilePath, String destDir, String newFileName,int resultorvalue){
        File srcFile = new File(srcFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(StringUtils.isEmpty(newFileName)){
            return;
        }

        if(resultorvalue==1){

            String destFilePath2 = "/data/alldnrecognitiontestcase/nogrid/same/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }
            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else{

            String destFilePath2 = "/data/alldnrecognitiontestcase/nogrid/notsame/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }
            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

    public static void copyFilehandwriterfuzzyMatchNoGrid(String srcFilePath, String destDir, String newFileName,int resultorvalue){
        File srcFile = new File(srcFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(StringUtils.isEmpty(newFileName)){
            return;
        }

        if(resultorvalue==1){

            String destFilePath2 = "/data/alldnrecognitiontestcase/nogrid/fuzzymatch/same/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }
            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else{

            String destFilePath2 = "/data/alldnrecognitiontestcase/nogrid/fuzzymatch/notsame/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }
            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

    public static void copyFilehandwriterfuzzyMatchGrid(String srcFilePath, String destDir, String newFileName,int resultorvalue){
        File srcFile = new File(srcFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(StringUtils.isEmpty(newFileName)){
            return;
        }

        if(resultorvalue==1){

            String destFilePath2 = "/data/alldnrecognitiontestcase/grid/fuzzymatch/same/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }
            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }else{

            String destFilePath2 = "/data/alldnrecognitiontestcase/grid/fuzzymatch/notsame/" +newFileName;
            File destFile2 = new File(destFilePath2);
            if (destFile2.exists()) {
                destFile2.delete();
            }

            try {
                if (srcFile.exists()) {
                    Files.copy(srcFile.toPath(), destFile2.toPath());

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }



    public static String getFilePostfix(String fileName){
        if(fileName==null){
            return null;
        }
        int idx = Math.min(fileName.lastIndexOf("."), fileName.length()-1);
        if(idx<=0){
            return fileName;
        }
        return fileName.substring(idx);
    }

    public static String getFileNameBody(String fileName){
        if(fileName==null){
            return null;
        }
        int idx = Math.min(fileName.lastIndexOf("."), fileName.length()-1);
        if(idx<=0){
            return fileName;
        }
        return fileName.substring(0,idx);
    }

    public static String getQuestionNumber(String questionNumbers){
        if(questionNumbers==null){
            return "";
        }
        return questionNumbers.replace("[","").replace("]","");
    }

    public static String getFileNamePostfix(String fileName){
        if(fileName==null){
            return null;
        }
        int idx = Math.min(fileName.lastIndexOf("."), fileName.length()-1);
        if(idx<=0){
            return "";
        }
        return fileName.substring(idx);
    }


    public static String getFileNameBodyInFullPath(String filePath){
        String fileName = RecognitionDebugTool.getFileName(filePath);
        if(fileName==null){
            return null;
        }
        int idx = Math.min(fileName.lastIndexOf("."), fileName.length()-1);
        if(idx<=0){
            return fileName;
        }
        return fileName.substring(0,idx);
    }

    public static String getFileNameBodyExcludeResult(String fileName){
        if(fileName==null){
            return null;
        }
        int idx = Math.min(fileName.lastIndexOf("."), fileName.length()-1);
        if(idx<=0){
            return fileName;
        }
        String fileBody = fileName.substring(0,idx);
        int idx2 = Math.min(fileBody.lastIndexOf("-"), fileBody.length()-1);
        if(idx2<=0){
            return fileBody;
        }
        return fileBody.substring(0, idx2);
    }
    public static String getFileNameFirstBody(String fileName){
        if(fileName==null){
            return null;
        }
        int beginIdx = Math.max(fileName.lastIndexOf("/"),fileName.lastIndexOf("\\"));
        if(beginIdx<0){
            beginIdx=0;
        }else{
            beginIdx=beginIdx+1;
        }

        int endIdx = Math.min(fileName.indexOf("."), fileName.length()-1);
        if(endIdx<=0){
            return fileName.substring(beginIdx);
        }

        return fileName.substring(beginIdx,endIdx);
    }

    public static String getParentFileName(String filePath){
        if(filePath==null){
            return null;
        }
        File f = new File(filePath);
        if(!f.exists()){
            return null;
        }
        return f.getParentFile().getName();
    }

    public static String getFileName(String filePath){
        if(filePath==null){
            return null;
        }
        int maxIdx = Math.max(filePath.lastIndexOf("\\"), filePath.lastIndexOf("/"));
        if(maxIdx<=0){
            return filePath;
        }
        return filePath.substring(maxIdx+1);
    }

    public static String getDirPath(String filePath){
        if(filePath==null){
            return null;
        }
        int maxIdx = Math.max(filePath.lastIndexOf("\\"), filePath.lastIndexOf("/"));
        if(maxIdx<=0){
            return "";
        }
        return filePath.substring(0, maxIdx);
    }

    // \\172.18.5.60\hzy\third\3339934_6664\files\LO822BNK_3_DN1030000003\cardContent-151-1730266563688-1-D.png
    public static String getAnswerCardFileByItemPath(String itemPath){
        String dirPath = RecognitionDebugTool.getParentFilePath(itemPath, 1);
        if(dirPath.endsWith("\\") || dirPath.endsWith("/")){
            dirPath = dirPath.substring(0, dirPath.length()-1);
        }
        String answerCardPath = dirPath+".png";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".PNG";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".TIF";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".tif";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".jpg";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".JPG";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".jpeg";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        answerCardPath = dirPath+".JPEG";
        if(new File(answerCardPath).exists()){
            return answerCardPath;
        }
        return null;
    }


    public static void removeChnName(String dir){
        File dirFile = new File(dir);
        String newDir=dirFile.getParentFile().getAbsolutePath()+"/"+dirFile.getName()+"_new";
        File newFile= new File(newDir);
        if(!newFile.exists()) {
            newFile.mkdir();
        }
        List<File> fileList = new ArrayList<>();
        if(dirFile.exists() && dirFile.isDirectory()){
            File[] files = dirFile.listFiles();
            for(File f:files){
                String newpath=newDir+"/"+filterChinese(f.getName()).replace("-","");
                File newF=new File(newpath);
                f.renameTo(newF);
                fileList.add(newF);
            }
        }
        File[] srcFiles = new File[fileList.size()];
        srcFiles=fileList.toArray(srcFiles);
        File zipFile = new File(dirFile.getParentFile()+"/"+dirFile.getName()+"_new.zip");
        zipFiles(srcFiles,zipFile);
    }

    /**
     * 功能:压缩多个文件成一个zip文件
     * @param srcfile：源文件列表
     * @param zipfile：压缩后的文件
     */
    public static void zipFiles(File[] srcfile,File zipfile){
        byte[] buf=new byte[1024];
        try {
            //ZipOutputStream类：完成文件或文件夹的压缩
            ZipOutputStream out=new ZipOutputStream(new FileOutputStream(zipfile));
            for(int i=0;i<srcfile.length;i++){
                FileInputStream in=new FileInputStream(srcfile[i]);
                out.putNextEntry(new ZipEntry(srcfile[i].getName()));
                int len;
                while((len=in.read(buf))>0){
                    out.write(buf,0,len);
                }
                out.closeEntry();
                in.close();
            }
            out.close();
            System.out.println("压缩完成.");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    /**
     * 功能:解压缩
     * @param zipfile：需要解压缩的文件
     * @param descDir：解压后的目标目录
     */
    public static void unZipFiles(File zipfile,String descDir){
        try {
            ZipFile zf=new ZipFile(zipfile);
            for(Enumeration entries = zf.entries(); entries.hasMoreElements();){
                ZipEntry entry=(ZipEntry) entries.nextElement();
                String zipEntryName=entry.getName();
                InputStream in=zf.getInputStream(entry);
                OutputStream out=new FileOutputStream(descDir+zipEntryName);
                byte[] buf1=new byte[1024];
                int len;
                while((len=in.read(buf1))>0){
                    out.write(buf1,0,len);
                }
                in.close();
                out.close();
                System.out.println("解压缩完成.");
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    /**
     * 过滤掉中文
     * @param str 待过滤中文的字符串
     * @return 过滤掉中文后字符串
     */
    public static String filterChinese(String str) {
        // 用于返回结果
        String result = str;
        boolean flag = isContainChinese(str);
        if (flag) {// 包含中文
            // 用于拼接过滤中文后的字符
            StringBuffer sb = new StringBuffer();
            // 用于校验是否为中文
            boolean flag2 = false;
            // 用于临时存储单字符
            char chinese = 0;
            // 5.去除掉文件名中的中文
            // 将字符串转换成char[]
            char[] charArray = str.toCharArray();
            // 过滤到中文及中文字符
            for (int i = 0; i < charArray.length; i++) {
                chinese = charArray[i];
                flag2 = isChinese(chinese);
                if (!flag2) {// 不是中日韩文字及标点符号
                    sb.append(chinese);
                }
            }
            result = sb.toString();
        }
        return result;
    }

    /**
     * 判断字符串中是否包含中文
     * @param str
     * 待校验字符串
     * @return 是否为中文
     * @warn 不能校验是否为中文标点符号
     */
    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 判定输入的是否是汉字
     *
     * @param c
     *  被校验的字符
     * @return true代表是汉字
     */
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
            return true;
        }
        return false;
    }

    static public void deleteDirectory(File dir) throws IOException {
        if( (dir == null) || !dir.isDirectory()) {
            return;
        }
        File[ ] entries = dir.listFiles();
        int sz = entries.length;
        for(int i=0;i<sz;i++){
            if(entries[i].isDirectory( )) {
                deleteDirectory(entries[i]);
            } else {
                entries[i].delete();
            }
        }
        dir.delete();
    }


    public static String escapeExprSpecialWord(String keyword) {
        if (StringUtils.isNotBlank(keyword)) {
            String[] fbsArr = { "\\", "$", "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}", "|" };
            for (String key : fbsArr) {
                if (keyword.contains(key)) {
                    keyword = keyword.replace(key, "\\" + key);
                }
            }
        }
        return keyword;
    }

    public static void bufferedWriteFile(String filepath, String content) throws IOException {
        File file = new File(filepath);
        File dir = file.getParentFile();
        if(!dir.exists()){
            dir.mkdirs();
        }
        if(!file.exists()){
            file.createNewFile();
        }
        if(content==null){
            return;
        }
        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(filepath))) {
            bufferedWriter.write(content);
        }
    }

    public static void bufferedWriteFileAppend(String filepath, String content) throws IOException {
        File file = new File(filepath);
        if(!file.exists()){
            file.createNewFile();
        }
        if(content==null){
            return;
        }
        try (BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(filepath,true))) {
            bufferedWriter.write(content);
        }
    }

    /**
     * 追加文件内容：<p>
     * 判断文件是否存在，不存在则创建<p>
     * 使用FileOutputStream，在构造FileOutputStream时，把第二个参数设为true<p>
     *
     * @param filepath
     * @param content
     */
    public static void bufferedAppendFile(String filepath, String content) {
            File file = new File(filepath);
            // 判断文件不存在，返回
            if (!judeFileExists(file)) {
                return;
            }
            if (!file.exists()) {
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
            }
            BufferedWriter out = null;
            try {
                out = new BufferedWriter(new OutputStreamWriter(
                        new FileOutputStream(file, true)));
                out.write(content);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    out.flush();
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

    }
    // 判断文件是否存在
    public static boolean judeFileExists(File file) {
        if (file.exists()) {
//            System.out.println("File exists");
            return Boolean.TRUE;
        } else {
//            System.out.println("File not exists, please create it ...");
            return Boolean.FALSE;
        }
    }


    public static String bufferedReadFile(String filePath) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return sb.toString();
    }

    public static String getParentDir(String baseDir){
        File file = new File(baseDir);
        if(!file.exists()){
            return null;
        }
        return file.getParentFile().getAbsolutePath();
    }

    public static void createDigitDirs(File redPenScoreDir, boolean isSaveRedPenScorePic_) {
        for (int i = 0; i <= 9; i++) {
            File f = new File(redPenScoreDir.getAbsolutePath() + "/" + i);
            if (!f.exists()) {
                f.mkdir();
            }
            if(isSaveRedPenScorePic_) {
                File f2 = new File(redPenScoreDir.getAbsolutePath() + "/" + i + "_");
                if (!f2.exists()) {
                    f2.mkdir();
                }
            }
        }

        if(isSaveRedPenScorePic_) {
            File f3 = new File(redPenScoreDir.getAbsolutePath() + "/xx");
            if (!f3.exists()) {
                f3.mkdir();
            }
            File f4 = new File(redPenScoreDir.getAbsolutePath() + "/xxx");
            if (!f4.exists()) {
                f4.mkdir();
            }
        }
    }

    public static String getParentFilePath(String baseFile, int depth){
        File file = new File(baseFile);
        if(!file.exists()){
            return null;
        }
        for(int i=0;i < depth; i++) {
            file = file.getParentFile();
        }
        return file.getAbsolutePath();
    }


    public static void copyFile(String srcFilePath, String destDir){
        copyFile(srcFilePath, destDir, null);
    }


    public static void copyFile(String srcFilePath, String destDir, String newFileName){
        File srcFile = new File(srcFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(StringUtils.isEmpty(newFileName)){
            newFileName = RecognitionDebugTool.getFileName(srcFilePath);
        }
        File destDirFile = new File(destDir);
        if(!destDirFile.exists()){
            destDirFile.mkdirs();
        }

        String destFilePath = destDir +"/"+newFileName;
        File destFile = new File(destFilePath);
        if (destFile.exists()) {
            destFile.delete();
        }
        try {
            if (srcFile.exists()) {
                Files.copy(srcFile.toPath(), destFile.toPath());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public static void copyDir(String srcFilePath, String destFilePath) throws Exception {
        File srcFile = new File(srcFilePath);
        File destFile = new File(destFilePath);
        if(!srcFile.exists()){
            return;
        }
        if(!destFile.exists()){
            destFile.mkdirs();
        }
        copyDir(srcFile, destFile);
    }

    /*
     * 拷贝目录
     * srcFile 拷贝源
     * destFile 拷贝目标
     */
    public static void copyDir(File srcDirFile, File destDirFile) throws Exception {
        if (srcDirFile.isFile()) {
            // srcFile如果是一个文件的话，递归结束
            // 是文件的时候需要拷贝
            // ...一边读一边写
            // 读这个文件
            FileInputStream fis = new FileInputStream(srcDirFile);
            if(!destDirFile.exists()){
                destDirFile.mkdirs();
            }
            // 写到这个文件中
            String path = (destDirFile.getAbsolutePath().endsWith("/") ? destDirFile.getAbsolutePath() :
                    destDirFile.getAbsoluteFile()+"/") + srcDirFile.getName();;
            FileOutputStream fos = new FileOutputStream(path);
            // 一边读一边写
            byte[] b = new byte[1024 * 1024];
            int readCount=0;
            while ((readCount = fis.read(b))!=-1) {
                fos.write(b,0,readCount);
            }
            fos.close();
            fis.close();
            return;
        }else {
            // 获取源下面的子目录
            File[] files = srcDirFile.listFiles();
//		System.out.println(files.length);// 广度搜索
            for (File file : files) {
                // 获取所有文件的（包括目录和文件）绝对路径
//			System.out.println(file.getAbsolutePath());
                if (file.isDirectory()) {
                    // 新建对应的目录
//				System.out.println(file.getAbsolutePath());
                    // D:\ShellExt\installer 源目录
                    // C:\ShellExt\installer 目标目录
//                String srcDir = file.getAbsolutePath();
                    String destDir = (destDirFile.getAbsolutePath().endsWith("/") ? destDirFile.getAbsolutePath() :
                            destDirFile.getAbsoluteFile() + "/") + file.getName();
//				System.out.println(destDir);
                    File newDirFile = new File(destDir);
                    if (!newDirFile.exists()) {
                        newDirFile.mkdirs();
                    }
                }
                // 递归调用
                copyDir(file, destDirFile);
            }
        }
    }

    public static String getExceptionAllInfo(Exception e){
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        e.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try{
            out.close();
        }catch (Exception e1){
            e1.printStackTrace();
        }
        return ret;
    }

    public static String getExceptionAllInfo(Error e){
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        PrintStream pout = new PrintStream(out);
        e.printStackTrace(pout);
        String ret = new String(out.toByteArray());
        pout.close();
        try{
            out.close();
        }catch (Exception e1){
            e1.printStackTrace();
        }
        return ret;
    }
    /**
     * 从网络Url中下载文件
     *
     * @param urlStr
     * @param fileName
     * @param savePath
     * @throws IOException
     */
    public static String downLoadFromUrl(String urlStr, String fileName, String savePath) {
        try {

            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            // 防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

            // 得到输入流
            InputStream inputStream = conn.getInputStream();
            // 获取字节数组
            byte[] getData = readInputStream(inputStream);

            // 文件保存位置
            File saveDir = new File(savePath);
            if (!saveDir.exists()) {
                saveDir.mkdir();
            }
            File file = new File(saveDir + File.separator + fileName);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(getData);
            if (fos != null) {
                fos.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
            // System.out.println("info:"+url+" download success");
            return saveDir + File.separator + fileName;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";

    }

    public static String removeTrailingZeros(String input) {
        if (input.endsWith(".0")) {
            return input.substring(0, input.length() - 2);
        } else if (input.endsWith(".00")) {
            return input.substring(0, input.length() - 3);
        } else {
            return input;
        }
    }

    public static void classifyByQuestionNumber(String dirStr, String newDirStr){
        File oldDir = new File(dirStr);
        File newDir = new File(newDirStr);
        if(!oldDir.exists()){
            return;
        }
        if(!newDir.exists()){
            newDir.mkdirs();
        }
        //397_1481288324324100_250674_401.png
        File[] files = oldDir.listFiles();
        for(File f:files){
            String[] fileNameArr = RecognitionDebugTool.getFileNameBody(f.getName()).split("_");
            if(fileNameArr.length>1){
                String questionNumber = fileNameArr[fileNameArr.length-1];
                File destDir = new File(newDir.getAbsolutePath()+"/"+questionNumber);
                if(!destDir.exists()){
                    destDir.mkdir();
                }
                RecognitionDebugTool.copyFile(f.getAbsolutePath(),destDir.getAbsolutePath(), f.getName());
            }
        }


    }

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }


    public static boolean isClearableDir(String fileName){
        if(fileName.contains("files-err") || fileName.contains("files-old")
           || fileName.contains("param-old") || fileName.contains("result-new")
        ){
            return true;
        }
        String clearableDir = SpringContextUtil.getProperty("clearableDir");
        if(clearableDir!=null){
            String[] dirNames = clearableDir.split(",");
            for(String dirName: dirNames){
                if(fileName.contains(dirName)){
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isOutputClearableDir(String fileName){
        if(fileName.contains("secondOffset") || fileName.contains("matchLevel")
                || fileName.contains("centerAngle") || fileName.contains("opencv")
                || fileName.contains("redPenScore") || fileName.contains("barCode")
                || fileName.contains("subjectCheck") || fileName.contains("handWrite")
                || fileName.contains("blankcheck") || fileName.contains("errmsg")
        ){
            return true;
        }
        String clearableDir = SpringContextUtil.getProperty("clearableDir");
        if(clearableDir!=null){
            String[] dirNames = clearableDir.split(",");
            for(String dirName: dirNames){
                if(fileName.contains(dirName)){
                    return true;
                }
            }
        }
        return false;
    }

    public static int getDigitAtPosition(int num, int position) {
        String numString = Integer.toString(num);
        char digit = numString.charAt(numString.length()-position);
        return Character.getNumericValue(digit);
    }

    public static int getLen(int num){
        String numString = Integer.toString(num);
        return numString.length();
    }

        public static Map<String, String> parsePath(String path) {
        Map<String, String> resultMap = new HashMap<>();

        if (path == null || path.isEmpty()) {
            resultMap.put("error", "Invalid input path");
            return resultMap;
        }

        // 判断关键词是 /third/ 还是 /system/
        String keyword;
        if (path.contains("/third/")) {
            keyword = "/third/";
        } else if (path.contains("/system/")) {
            keyword = "/system/";
        } else {
            resultMap.put("error", "Keyword not found in the path");
            return resultMap;
        }

        // 提取关键词目录后的子目录
        String[] parts = path.split(keyword);
        if (parts.length < 2) {
            resultMap.put("error", "Invalid path format");
            return resultMap;
        }

        String subDir = parts[1].split("/")[0]; // 关键词后的第一个子目录
        String[] ids = subDir.split("_");

        // 验证子目录是否有两个ID
        if (ids.length != 2) {
            resultMap.put("error", "Subdirectory does not contain two IDs");
            return resultMap;
        }

        // 根据关键词设置返回的键值对
        if (keyword.equals("/third/")) {
            resultMap.put("examUploaderId", ids[0]);
            resultMap.put("templateCode", ids[1]);
        } else if (keyword.equals("/system/")) {
            resultMap.put("examUploaderId", ids[0]);
            resultMap.put("paperId", ids[1]);
            resultMap.put("templateCode", "0");
        }

        return resultMap;
    }

    public static Map<Long, String> groupAndSortAnswerCards(List<Map<String, Object>> answerCardFileList) {
        // 按 studentId 分组
        Map<Long, List<Map<String, Object>>> groupedByStudent = answerCardFileList.stream()
                .collect(Collectors.groupingBy(card -> Long.valueOf(card.get("studentId").toString())));

        // 对每个学生的数据处理
        Map<Long, String> studentAnswerCardIdsMap = new HashMap<>();
        for (Map.Entry<Long, List<Map<String, Object>>> entry : groupedByStudent.entrySet()) {
            Long studentId = entry.getKey();
            List<Map<String, Object>> cards = entry.getValue();

            // 按 answerCardId 排序
            List<Long> sortedAnswerCardIds = cards.stream()
                    .map(card -> Long.valueOf(card.get("answerCardId").toString()))
                    .sorted()
                    .collect(Collectors.toList());

            // 拼接为字符串（逗号分隔）
            String answerCardIdsStr = sortedAnswerCardIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            studentAnswerCardIdsMap.put(studentId, answerCardIdsStr);
        }

        return studentAnswerCardIdsMap;
    }


    public static void main(String[] args) {

//        String item2="\\\\172.18.5.60\\hzy\\third\\3339934_6664\\files\\LO822BNK_3_DN1030000003\\cardContent-151-1730266563688-1-D.png";
        String itemPath = "D:\\dongnigithub\\alldnrecognitiontestcase\\thirdreco\\objItemError-20241117\\files\\M1ZVN6VC_3_DN1113000051\\cardContent-5801-1731414370637.png";
        String answerCardPath = getAnswerCardFileByItemPath(itemPath);
        System.out.println(answerCardPath);

        String examInfo = RecognitionDebugTool.getFileName(RecognitionDebugTool.getParentFilePath(itemPath, 3));
        System.out.println("examInfo: " + examInfo);

        String answerCardPath2="D:\\dongnigithub\\alldnrecognitiontestcase\\thirdreco\\objItemError-20241117\\files\\M1ZVN6VC_3_DN1113000051.png";
        System.out.println(getParentFilePath(itemPath, 4));


//        String dirPath = "D:\\dongnigithub\\alldnrecognitiontestcase\\system\\redPenScoreCheck-20221130\\temp";
//        String paperId = "1341863071369333";
//        String templatePdfName = "template.pdf";
//        String env = "product";
//        downloadSystemTemplateImg(paperId, dirPath, templatePdfName,env);
//
//        String filePostfix = RecognitionDebugTool.getFilePostfix("D:/aaa/bbb.png");
//        System.out.println(filePostfix);
//
//        String testFile = RecognitionDebugTool.getParentFilePath("/data/alldnrecognitiontestcase/system/48456_9457869852/files/506318044_DN0107000003/studentNumber.png", 3);
//        System.out.println("testFile="+testFile);
//
//
//        String regex = "[\\d]+\\.png";
//        Pattern pattern = Pattern.compile(regex);
//        String f1 = "151-A.png";
//        Matcher matcher = pattern.matcher(f1);
//        if(matcher.matches()){
//            System.out.println(f1+" 匹配成功");
//        }
//        f1 = "51.png";
//        matcher = pattern.matcher(f1);
//        if(matcher.matches()){
//            System.out.println(f1+" 匹配成功");
//        }
//        f1 = "51-B,C.png";
//        matcher = pattern.matcher(f1);
//        if(matcher.matches()){
//            System.out.println(f1+" 匹配成功");
//        }

//        RecognitionDebugTool.removeChnName("D:\\appData\\WeChat Files\\evansacred\\FileStorage\\MsgAttach\\37dfa27e13e7f2d813f50f663bcce133\\File\\2022-07\\huaxue");

//        System.out.println(RecognitionDebugTool.getFileNameBody("aaa.txt"));
//
//        System.out.println(RecognitionDebugTool.getDirPath("D:/test/1.png"));
//        System.out.println(RecognitionDebugTool.getDirPath("D:/test\\2.png"));
//        String mapStr="  @String[examPaperId]:@String[187474], "+
////                "  @String[templateType]:@String[2],     "+
////                "  @String[phase]:@String[complete],     "+
////                "  @String[uploaderId]:@String[2350027], "+
//                "  @String[artsScience]:@String[0],      "+
//                "  @String[examName]:@String[南海区2022届高三摸底考  历史],   "+
//                "  @String[uploadStatus]:@String[3],     "+
//                "  @String[userName]:@String[陈恒乐],       "+
//                "  @String[userId]:@String[24148363],    "+
//                "  @String[courseName]:@String[历史],      "+
//                "  @String[answerCardPath]:@String[upload/answerCard/2021/8/30/165463/159216/],        "+
//                "  @String[uploaderName]:@String[许忠福],   "+
//                "  @String[paperName]:@String[南海区2022届高三历史摸底测试答题卡1],                      "+
//                "  @String[schoolId]:@String[478],       "+
//                "  @String[uploadType]:@String[11],      "+
//                "  @String[examId]:@String[165463],      "+
//                "  @String[correctMode]:@String[1],      "+
//                "  @String[courseId]:@String[9],         "+
//                "  @String[paperId]:@String[1341863071069178],   "+
//                "  @String[examUploaderId]:@String[159216], ";
//
//        map2json(mapStr);
    }



}
