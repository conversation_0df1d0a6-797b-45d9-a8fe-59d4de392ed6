package com.dongni.exam.answercard;

import com.dongni.config.clean.AnswerCardSignCleanConfig;
import com.dongni.exam.newcard.bean.DTO.DebugExamDTO;
import com.dongni.exam.newcard.dao.FileCleanMapper;
import com.dongni.exam.newcard.service.NewAnswerCardZipService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/9/26 10:31
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class AnswerCardCleanTest {

    @Autowired
    private AnswerCardSignCleanConfig config;

    @Autowired
    private FileCleanMapper fileCleanMapper;

    @Autowired
    private NewAnswerCardZipService newAnswerCardZipService;


    @Test
    public void clean() {
        //config.syncAnswerCard();
        config.restCleanAnswerCard(true);
    }

    @Test
    public void sel() {
        DebugExamDTO debugExamDTO = new DebugExamDTO();
        debugExamDTO.setExamUploaderId(4539595L);
        debugExamDTO.setStudentId(110654663868L);
        debugExamDTO.setTemplateLabel("手写考号");
        newAnswerCardZipService.pushTemplateLibrary(debugExamDTO);
    }

}
