package com.dongni.exam.newcard.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.dongni.FatServiceApplication;
import com.dongni.common.utils.BatchDataUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.filestorage.FileStorageTemplate;
import com.dongni.commons.filestorage.entity.FileStorageGet;
import com.dongni.exam.card.service.AnswerCardBatchService;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.newcard.service.impl.NewRecognitionTask;
import com.dongni.exam.filevacuum.service.impl.FileStorageVacuumAnswerCardCorrectService;
import com.dongni.exam.newcard.bean.VO.AnswerCardVO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.bean.dto.ExamUploaderDTO;
import com.dongni.exam.plan.service.NewPlanExamUploaderService;
import com.dongni.exam.plan.service.ScannerExamUploaderService;
import com.dongni.exam.schedule.ExamItemServiceSchedule;
import com.dongni.tiku.common.util.MapUtil;
import com.limou.answercard.AnswerCard;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024/6/17 11:22
 * @description:
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FatServiceApplication.class)
public class NewAnswerCardServiceTest {

    @Autowired
    private NewAnswerCardService newAnswerCardService;

    @Test
    public void insertAnswerCard() {
        Map<String, Object> params = getParams();
        long batchId = 0;
        if (params.containsKey("files")) {
            List<Map<String, Object>> files = MapUtil.getListMap(params, "files");
            if (CollectionUtils.isNotEmpty(files)) {
                batchId = MapUtil.getLong(files.get(0), "batchId", batchId);
            }
        }
        newAnswerCardService.incrementOrTask(params, MapUtil.of("batchId", batchId));

    }


    private Map<String, Object> getParams() {
        String data = "{\"isRecognizedStart\":true,\"templateNumber\":1,\"clientLock\":0,\"batchId\":984811,\"isFinished\":false,\"userName\":\"曾双锦\",\"deviceId\":\"LVYNAYAP\",\"userId\":********,\"accountId\":3137714,\"clientType\":4,\"batchScanCardNum\":7,\"examId\":2989808,\"uploadType\":1,\"files\":[{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000002.png\",\"fileName\":\"LVYNAYAP_1_DN0616000002.png\",\"answerCardCode\":\"11\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:46\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000003.png\",\"fileName\":\"LVYNAYAP_1_DN0616000003.png\",\"answerCardCode\":\"11\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:47\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000004.png\",\"fileName\":\"LVYNAYAP_1_DN0616000004.png\",\"answerCardCode\":\"11\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:47\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000001.png\",\"fileName\":\"LVYNAYAP_1_DN0616000001.png\",\"answerCardCode\":\"11\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:48\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000005.png\",\"fileName\":\"LVYNAYAP_1_DN0616000005.png\",\"answerCardCode\":\"22\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:48\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000006.png\",\"fileName\":\"LVYNAYAP_1_DN0616000006.png\",\"answerCardCode\":\"22\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:49\"},{\"filePath\":\"upload/answerCard/2024/6/16/2989808/3139638/LVYNAYAP_1_DN0616000007.png\",\"fileName\":\"LVYNAYAP_1_DN0616000007.png\",\"answerCardCode\":\"22\",\"errorCode\":0,\"batchId\":984811,\"createTime\":\"2024-6-16 8:30:49\"}],\"userType\":3,\"paperId\":1341863072462332,\"timeDiffML\":-190,\"examUploaderId\":9}";
        return JSONUtil.parseObj(data);
    }


    @Autowired
    private ScannerExamUploaderService scannerExamUploaderService;

    @Test
    public void insertExamUploader() {
        ExamUploaderDTO dto = new ExamUploaderDTO();
        dto.setExamId(1);
        dto.setPaperId(1);
        dto.setUploadType(3);
        dto.setSchoolId(0);
        dto.setTemplateCode(-1);
        dto.setTemplateCode(2);
        dto.setUserId(1);
        dto.setUserName("test");
        dto.setUploaderId(1);
        dto.setUploaderName("test");
        scannerExamUploaderService.getScannerExamUploader(dto);
    }

    @Autowired
    private ExamItemServiceSchedule examItemServiceSchedule;

    @Test
    public void upgradeExamItemErrorCode() {
        examItemServiceSchedule.handleExamUploaderExamItems(586989);
    }

    @Autowired
    private AnswerCardServiceV3 answerCardServiceV3;

    @Autowired
    private AnswerCardService answerCardService;

    @Test
    public void batchUpdateAnswerCard() {
        List<Long> studentIds = new ArrayList<>();
        studentIds.add(110666497031L);
        List<Map<String, Object>> cardList = answerCardServiceV3.getCardListByExamUploaderId(3370889, studentIds);
        Map<String, Object> params = new HashMap<>();
        params.put("userId", 239228762);
        params.put("userName", "范永林");
        for (Map<String, Object> card : cardList) {
            answerCardService.convertTifToPng(params, card);
        }
    }

    @Autowired
    private FileStorageVacuumAnswerCardCorrectService fileStorageVacuumAnswerCardCorrectService;


    @Test
    public void correctAnswerCards() {
       answerCardServiceV3.consumerExamUploaderIds();
    }

    private String getFilePathExamUploader(String filePath, long examUploaderId, long examId) {
        if (filePath.contains("/" + examUploaderId + "/")) {
            return String.valueOf(examUploaderId);
        }
        String b = examId + "/";
        if (!filePath.contains(b)) {
            return String.valueOf(examUploaderId);
        }
        int idx = filePath.indexOf(b);
        filePath = filePath.substring(idx + b.length());
        return filePath.substring(0, filePath.indexOf("/"));
    }

    @Autowired
    private NewRecognitionTask newRecognitionTask;
    @Test
    public void generateTaskForUploaderTest() {
        AtomicInteger idx = new AtomicInteger(1);
        newRecognitionTask.generateTaskForUploader(188118L, System.currentTimeMillis(), idx);
    }

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Autowired
    private NewExamItemService newExamItemService;

    @Test
    public void isObjectiveFinished() {
        List<Long> ids = Arrays.asList(188127L, 218111L, 38217L);
        for(Long id : ids) {
            NewExamUploaderBO examUploaderBO = newPlanExamUploaderService.getExamUploader(id);
            boolean isFinished = newExamItemService.isObjectiveFinished(examUploaderBO);
            System.out.println(isFinished);
        }
    }

    @Autowired
    private AnswerCardBatchService answerCardBatchService;
    @Test
    public void testCombine() {
        answerCardBatchService.handleBatchCardReCombine(914, 563);
    }
}
