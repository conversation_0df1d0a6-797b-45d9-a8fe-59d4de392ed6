package com.dongni.exam.newcard.service;

import com.dongni.FatServiceApplication;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.card.service.AnswerCardService;
import com.dongni.exam.newcard.service.impl.CardRecognitionService;
import com.dongni.exam.newcard.service.impl.NewRecognitionTask;
import com.dongni.exam.plan.bean.ExamUploaderBO;
import com.dongni.exam.plan.bean.bo.NewExamUploaderBO;
import com.dongni.exam.plan.service.NewPlanExamUploaderService;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 2024/6/26
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = FatServiceApplication.class)
public class ExceptionCardServiceTest {
    // 混扫 answerCard 转移.
    @Autowired
    private ExamRepository examRepository;

    public static final String BYSCHOOL = "bySchool";

    public static final String BYCLASS = "byClass";

    @Autowired
    private IExceptionCard exceptionCard;

    @Test
    public void transformCard() {
        // 必须设置正确的examId, paperIds.
        long examId = 3046317;
        List<Long> paperIds = new ArrayList<>();
        paperIds.add(1341863072491455L);
        paperIds.add(1341863072491782L);
        paperIds.add(1341863072491536L);
        paperIds.add(1341863072491826L);

        paperIds.forEach(paperId -> {
            ExamUploaderBO bo = new ExamUploaderBO();
            // 混扫，在识别考号之后
            bo.setExamId(examId);
            bo.setPaperId(paperId);
            String handleMode = BYSCHOOL;

            List<Map<String, Object>> answerCardList = examRepository.selectList("NewExceptionCardMapper.getRelativeStudentExamNumCardList", bo);
            if(CollectionUtils.isEmpty(answerCardList)) {
                System.out.println("answerCardList is empty. 识别关联的考号不正确，没有处理数据！");
                return;
            }

            List<ExamUploaderBO> examUploaderBOList = examRepository.selectList("NewExceptionCardMapper.getProcessingExamUploaderList", bo);

            // 按学校处理.
            if(BYSCHOOL.equals(handleMode)) {
                Map<Long, List<Map<String, Object>>> schoolCardListMap = answerCardList.stream().collect(groupingBy(item -> MapUtil.getLong(item, "schoolId")));
                Map<Long, List<ExamUploaderBO>> schoolExamUploaderMap = examUploaderBOList.stream().collect(groupingBy(ExamUploaderBO::getSchoolId));

                for(Map.Entry<Long, List<Map<String, Object>>> entry : schoolCardListMap.entrySet()) {
                    long schoolId = entry.getKey();
                    List<Map<String, Object>> cardList = entry.getValue();
                    if (schoolExamUploaderMap.containsKey(schoolId)) {
                        List<ExamUploaderBO> examUploaderBOS = schoolExamUploaderMap.get(schoolId);
                        exceptionCard.transformSingle(cardList, examUploaderBOS.get(0));
                    }
                }
            }
        });
    }


    @Autowired
    private NewRecognitionTask recognitionTask;
    @Test
    public void doRecognition() {
        recognitionTask.scanningAnswerCardFromRedis();
    }


    @Autowired
    private CardRecognitionService cardRecognitionService;

    @Autowired
    private NewPlanExamUploaderService newPlanExamUploaderService;

    @Autowired
    private AnswerCardService answerCardService;
    @Test
    public void productTask() {
        NewExamUploaderBO examUploaderBO = newPlanExamUploaderService.getEasyExamUploaderByExamUploaderId(188117L);
        CardRecognitionService.ExamUploaderCardInfo examUploaderCardInfo = new CardRecognitionService.ExamUploaderCardInfo();
        examUploaderCardInfo.setTemplateNumber(answerCardService.getTemplateNumber(MapUtil.of("examUploaderId", examUploaderBO.getExamUploaderId())));
        examUploaderCardInfo.setExamUploaderId(examUploaderBO.getExamUploaderId());
        examUploaderCardInfo.setScanTimeDate(new Timestamp(new Date().getTime()));
        cardRecognitionService.handleRecExamUploaderCard(examUploaderCardInfo);
    }
}
