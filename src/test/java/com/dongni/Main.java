package com.dongni;

import org.slf4j.LoggerFactory;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class Main {
	private static final AtomicInteger poolNumber = new AtomicInteger(1);
	private static ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors(),
		Runtime.getRuntime().availableProcessors() * 2, 3,
		TimeUnit.MINUTES, new LinkedBlockingDeque<>(30), new ThreadFactory() {
		@Override
		public Thread newThread(Runnable r) {
			Thread thread = new Thread(r);
			thread.setDaemon(true);
			thread.setName("myBiz-" + poolNumber.incrementAndGet());
			return thread;
		}
	}, new CallerRunsPolicy());

	public static void main(String[] args) throws InterruptedException {
		for (int i = 0; i < 20; i++) {
			executorService.submit(()->{
				try {
					Thread.sleep(3000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				System.out.println(1000);
			});
		}
		executorService.shutdown();
		executorService.awaitTermination(10,TimeUnit.DAYS);
		System.out.println("kugou");
	}

}
