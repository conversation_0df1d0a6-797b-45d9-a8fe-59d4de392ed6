package com.dongni.huaibei;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dongni.common.threadpool.MyAsyncConfigurer;
import com.dongni.exam.arrange.service.PaperReadIntelligentService;
import com.dongni.tiku.own.service.OwnExamPaperService;
import jdk.nashorn.internal.runtime.linker.LinkerCallSite;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.tools.ant.util.facade.FacadeTaskHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2020/04/30 18:07
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class IntelligentTest {

    @Autowired
    PaperReadIntelligentService paperReadIntelligentService;
    @Autowired
    private MyAsyncConfigurer myAsyncConfigurer;
    /**
     * 查询已经完成智能批改的数量
     */
    @Test
    public void getIntelligentPaperStatusTest() {
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("examId", 1569985);
        hashMap.put("schoolId", 516);
        hashMap.put("paperId", 9200939280L);
        hashMap.put("courseId", 3);
        hashMap.put("questionNumber", 1501);
        hashMap.put("structureNumber", 16);
        System.out.println(paperReadIntelligentService.getIntelligentPaperStatus(hashMap));
        //
    }

    /**
     * 插入智能阅卷表 (阅卷安排按钮)
     */
    @Test
    public void saveIntelligentReadPaperTest() {
        Map<String, Object> map = new HashMap<>();
        map.put("paperId", 1341861925069083L); //
        map.put("schoolId", 516); //
        map.put("examId", 1570064); //
        map.put("courseId", 4); // 英语
        map.put("userId", 820); //
        map.put("examPaperId", 38864);
        map.put("teacherId", 15380); //
        map.put("gradeType", 11); //
        map.put("userName", "靓仔22"); //
        map.put("clientType", 1); //
        map.put("correctCount", 1);
//        paperReadIntelligentService.saveIntelligentReadPaper(map);
        //
    }

    /**
     * 智能批改详情
     */
    @Test
    public void groupByAnswerSetScoreTest() {
//        List<Long> list = new ArrayList<>();
//        list.add(1L);
        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("paperReadIntelligenceIdSet", Arrays.asList(7, 8, 9));
//        hashMap.put("paperReadId", 93215); // 17458
        hashMap.put("paperId", 1341861925069083L); // 17458
        hashMap.put("examId", 1570064);
        hashMap.put("courseId", 4L);
        hashMap.put("schoolId", 516L);
        hashMap.put("questionNumber", 501);
        hashMap.put("structureNumber", 6);
        System.out.println(JSON.toJSONString(paperReadIntelligentService.groupByAnswerSetScore(hashMap)));
        //
    }

    /**
     * 获取题号 列表
     *
     * @return
     */
    @Test
    public void getQuestionNumberTest() {
//        List<Long> list = new ArrayList<>();
//        list.add(1L);
        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("paperReadIntelligenceIdSet", Arrays.asList(7, 8, 9));
//        hashMap.put("paperReadId", 93215); // 17458
        hashMap.put("paperId", 1341861925069083L); // 17458
        hashMap.put("examId", 1570064);
        hashMap.put("courseId", 4L);
        hashMap.put("schoolId", 516L);
        List<Map<String, Object>> questionNumber = paperReadIntelligentService.getQuestionNumber(hashMap);
        System.out.println(JSON.toJSONString(questionNumber));
        //
    }


    /**
     * 更新子库 成绩
     */
    @Test
    public void setIntelligentScoreTest() {
        HashMap<String, Object> h1 = new HashMap<>();
        h1.put("finallyScore", 999.9);
        h1.put("examItemIntelligenceIds", Arrays.asList(18, 19, 20, 21));

        HashMap<String, Object> h2 = new HashMap<>();
        h2.put("finallyScore", 999);
        h2.put("examItemIntelligenceIds", Arrays.asList(22, 23));

        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("courseId", 3);
//        hashMap.put("structureNumber", 16);
//        hashMap.put("questionNumber", 1501);

        hashMap.put("examId", 1569985);
        hashMap.put("schoolId", 516);
        hashMap.put("paperId", 9200939280L);
        hashMap.put("userId", 1);
        hashMap.put("userName", 1);
        hashMap.put("scoreIntelligentList", Arrays.asList(h1, h2));
        System.out.println(JSON.toJSONString(hashMap));
        paperReadIntelligentService.setIntelligentScore(hashMap);
        //
    }

    /**
     * 分页显示学生答案 填空题图片
     */
    @Test
    public void getUrlFillAnswerListTest() {
        String a = "130,131,132,133,134,135,137";
        Map<String, Object> map = new HashMap<>();
        map.put("ids", a);
        map.put("pageNo", 0);
        map.put("pageSize", 10);
        System.out.println(JSON.toJSONString(paperReadIntelligentService.getUrlFillAnswerList(map)));
    }


    /**
     * 分页显示学生答案 作文图片
     */
    @Test
    public void getUrlDocAnswerListTest() {
        String a = "18,19,20";
        Map<String, Object> map = new HashMap<>();
        map.put("ids", a);
        map.put("pageNo", 0);
        map.put("pageSize", 50);
        System.out.println(JSON.toJSONString(paperReadIntelligentService.getUrlDocAnswerList(map)));
    }


    @Test
    public void isIntelligentFinishTest() {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("userId", 1);
        hashMap.put("userName", 1);
        hashMap.put("examId", 1574377);
        hashMap.put("schoolId", 516);
        hashMap.put("paperId", 1341861925069083L);
        hashMap.put("courseId", 4);
        hashMap.put("questionNumber", 401);
        hashMap.put("structureNumber", 5);
        String intelligentFinish = paperReadIntelligentService.isIntelligentFinish(hashMap);
        System.out.println(intelligentFinish);

    }

    /**
     * 更新主库 成绩
     */
    @Test
    public void setExamItemCoreTest() {
//        hashMap.put("paperReadIntelligenceIdSet", Arrays.asList(7, 8, 9));
//        hashMap.put("paperReadId", 93215); // 17458
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("courseId", 4);


        hashMap.put("examId", 1570232);
        hashMap.put("schoolId", 789);
        hashMap.put("paperId", 1341861925069208L);
        hashMap.put("userId", 1);
        hashMap.put("userName", 1);
        paperReadIntelligentService.setExamItemScore(hashMap);
    }

    /**
     * 待办
     *
     */
    @Test
    public void insertIntelligentTodoTaskTest() {
//        hashMap.put("paperReadIntelligenceIdSet", Arrays.asList(7, 8, 9));
//        hashMap.put("paperReadId", 93215); // 17458
        HashMap<String, Object> map = new HashMap<>();
        map.put("paperId", 1348147475482341L); //
        map.put("schoolId", 789); //
        map.put("examId", 1570238); //
        map.put("courseId", 4); // 英语
        map.put("userId", 820); //
        map.put("examPaperId", 39145);
        map.put("teacherId", 19080); //
        map.put("gradeType", 11); //
        map.put("userName", "靓仔22"); //
        map.put("clientType", 1); //
        map.put("correctCount", 1);
//        paperReadIntelligentService.insertIntelligentTodoTask(map);
        paperReadIntelligentService.deleteIntelligentTodoTask(map);
    }

    /**
     * 对比报告
     */
    @Test
    public void manOcrComparedTest() {
        HashMap<String, Object> map = new HashMap<>();

        map.put("paperId", 1341861925069083L); //
        map.put("schoolId", 516); //
        map.put("examId", 1570064); //
        map.put("courseId", 4); // 英语
        map.put("userId", 820); //
        map.put("examPaperId", 38864);
        map.put("teacherId", 15380); //
        map.put("gradeType", 11); //
        map.put("userName", "靓仔22"); //
        map.put("clientType", 1); //
        map.put("correctCount", 1);
        map.put("correctCount", 1);
        map.put("correctCount", 1);

        System.out.println(JSON.toJSONString(paperReadIntelligentService.manOcrCompared(map)));
    }


    @Test
    public void getPaperAndCourseMessageByExamTest() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("examId", 1570226);
        map.put("paperList", "1341861925069083,1234");
        System.out.println(JSON.toJSONString(paperReadIntelligentService.getPaperAndCourseMessageByExam(map)));
    }

    @Test
    public void testM() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("paperId", 1348147475482341L); //
        map.put("schoolId", 789); //
        map.put("examId", 1570238); //
        map.put("courseId", 4); // 英语
        map.put("userId", 820); //
        map.put("examPaperId", 39145);
        map.put("teacherId", 19080); //
        map.put("gradeType", 11); //
        map.put("userName", "靓仔22"); //
        map.put("clientType", 1); //
        map.put("correctCount", 1);

//        paperReadIntelligentService.insertIntelligentTodoTask(map);

        // 先初始化 t_exam_item_intelligence
        List<Map<String, Object>> data
                = paperReadIntelligentService.selectByPaperIdMethod(map);
        String isTodo = null;
        if (CollectionUtils.isNotEmpty(data)){
            isTodo = paperReadIntelligentService.initIntelligentReadPaper(map, data);
        }
//        examMarkPreparationService.executeExamMarkPreparation(params);

        // 开始智能阅卷
        if (CollectionUtils.isNotEmpty(data)){
            String finalIsTodo = isTodo;
//            new Thread(() ->
          paperReadIntelligentService.saveIntelligentReadPaper(map, finalIsTodo, data);


//            ).start();
        }



    }




}
