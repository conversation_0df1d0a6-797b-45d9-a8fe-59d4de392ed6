package com.dongni.huaibei;

import com.alibaba.fastjson.JSON;
import com.dongni.basedata.admin.service.IBaseSchoolService;
import com.dongni.exam.arrange.service.PaperReadArrangeService;
import com.dongni.exam.arrange.service.PaperReadIntelligentService;
import com.dongni.exam.dataprocessing.service.ExamItemProcessingService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2020/04/26 17:08
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class BaseSchoolServiceTest {

    @Autowired
    IBaseSchoolService iBaseSchoolService;
    @Autowired
    PaperReadIntelligentService paperReadIntelligentService;
    @Autowired
    ExamItemProcessingService examItemProcessingService;

    @Test
    public void configSchoolIntelligentTest() {
        Map<String, Object> map = new HashMap<>();
        map.put("schoolId", "10086");
        map.put("schoolName", "10086测试学校");
        map.put("status", 1);
        map.put("userId", 1);
        map.put("userName", "超级管理员");
        iBaseSchoolService.configSchoolIntelligent(map);
        //
    }


    @Test
    public void configPaperIntelligentTest() {
        Map<String, Object> map = new HashMap<>();
        map.put("paperReadId", 92884);
        map.put("questionNumber", 1501);

        paperReadIntelligentService.isPaperIntelligent(map);
        // //
    }

    @Test
    public void getPaperIntelligentTest() {
        Map<String, Object> map = new HashMap<>();
        map.put("paperId", 1341861925069083L);
        map.put("examId", 1570064);
        map.put("courseId", 4);
        map.put("schoolId", 516); //   17458
//        map.put("questionList",  "151,1301,1401,1501,1601");
//        map.put("structureList",  "14,15,16");
        List<Map<String, Object>> paperIntelligent = paperReadIntelligentService.getPaperIntelligent(map);
        System.out.println(JSON.toJSON(paperIntelligent));
        // //
    }

    /**
     * 题目类别 设置为智能批改
     *
     * @return
     */
    @Test
    public void setPaperIntelligentTest() {
        Map<String, Object> hashMap = new HashMap<>();
        // 401 501 ************ questionNumber
        // 93209 93210 93211 93212 93215
        hashMap.put("paperReadId", 93215); // 17458
        hashMap.put("paperId", 1341861925069083L); // 17458
        hashMap.put("correctType", 1);
        hashMap.put("userName", "超级管理管");
        hashMap.put("userId", 1);
        Integer integer = paperReadIntelligentService.setPaperIntelligent(hashMap);
        System.out.println(integer);
        //
    }

    @Test
    public void getItemCountByQuestionNumberTest(){
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("examId", 1569985);
        hashMap.put("schoolId", 516);
        hashMap.put("paperId", 9200939280L);
        hashMap.put("courseId", 3);
        hashMap.put("questionNumber", 1501);
        hashMap.put("structureNumber", 16);
        System.out.println(examItemProcessingService.getItemCountByQuestionNumber(hashMap));
        //
    }

    @Test
    public void getConfigSchoolIntelligentTest(){
        Map<String, Object> map = new HashMap<>();
        map.put("schoolId", 516);
        System.out.println(JSON.toJSON(iBaseSchoolService.getConfigSchoolIntelligent(map)));
    }

}
