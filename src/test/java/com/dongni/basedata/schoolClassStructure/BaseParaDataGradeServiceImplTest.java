package com.dongni.basedata.schoolClassStructure;

import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClass;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataGrade;
import com.dongni.basedata.school.client.schoolClassStructure.bean.req.BaseDataRequest;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassService;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataGradeService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class BaseParaDataGradeServiceImplTest {
    @Autowired
    private IBaseDataGradeService baseDataGradeService;
    @Autowired
    private IBaseDataClassService baseDataClassService;

    @Test
    public void testGetActiveGrades() {
        // Arrange
        Long schoolId = 1L;
        BaseDataRequest baseDataRequest = new BaseDataRequest();
        baseDataRequest.setSchoolId(schoolId);


        // Act
        List<BaseDataGrade> actualGrades = baseDataGradeService.getActiveGrades(schoolId);
        System.out.println(actualGrades);

    }

    @Test
    public void testClassByClassIds() {
        // Act
        List<BaseDataClass> actualGrades = baseDataClassService.getBaseDataClassByClassIds(Lists.newArrayList(1L, 2L, 3L, 4L, 110L));
        System.out.println(actualGrades);

    }
}
