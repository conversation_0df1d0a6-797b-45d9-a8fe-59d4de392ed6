package com.dongni.basedata.teacher;


import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.school.classes.bean.ClassTeacherDTO;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassTeacherService;
import com.dongni.basedata.school.student.service.StudentCourseSelectionService;
import com.dongni.basedata.school.teacher.service.ITeacherService;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.vo.CourseTeacherVO;
import com.dongni.exam.common.mark.vo.PageTchVO;
import com.dongni.tiku.common.util.MapUtil;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TeacherCommonServiceTest {

    @Autowired
    private ITeacherService teacherService;
    @Autowired
    private ISchoolTeacherService schoolTeacherService;
    @Autowired
    private IBaseDataClassTeacherService classTeacherService;

    @Test
    public void lidtTeacher() {

        List<ClassTeacherDTO> classListByTeacherIds = classTeacherService.getClassListByTeacherIds(Lists.newArrayList(128276l, 128267l));
        System.out.println(classListByTeacherIds);

    }

    @Test
    public void listTeach() {

        List<CourseTeacherVO> courseTeacherVOList = schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 2l, 11);
        PageTchVO courseTeacherVOList1 = schoolTeacherService.listTch(Lists.newArrayList(192l), "", 2l, 11, 0, 10);

        schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 0l, 11);
        schoolTeacherService.listTch(Lists.newArrayList(192l), "", 0l, 11, 0, 10);

        schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 0l, 0);
        schoolTeacherService.listTch(Lists.newArrayList(192l), "", 0l, 0, 0, 10);


    }

    @Test
    public void getTeacherRoles() {
        Map<String, Object> params = buiderTeacherMap();
        Map<String, Object> teacherRoles = teacherService.getTeacherRoles(params);
        System.out.println(teacherRoles);
    }

    @Test
    public void getTeacherDetail() {
        Map<String, Object> params = buiderTeacherMap();
        Map<String, Object> teacherDetail = teacherService.getTeacherDetail(params);
        System.out.println(teacherDetail);
    }

    @Test
    public void insertTeacherRole() {
        Map<String, Object> params = buiderInsertTeacher();
        String s = teacherService.insertTeacherRole(params);
        System.out.println(s);
    }

    @Test
    public void updateTeacherRole() {
        Map<String, Object> params = buiderUpdateTeacher();
        String s = teacherService.updateTeacherRole(params);
        System.out.println(s);
    }


    private Map<String, Object> buiderTeacherMap() {
        return new HashMap<>();
    }

    private Map<String, Object> buiderInsertTeacher() {
        return new HashMap<>();
    }

    private Map<String, Object> buiderUpdateTeacher() {
        return new HashMap<>();
    }

    @Test
    public void getPhone() {
//        List<String> cellPhones = new ArrayList<>();
//        cellPhones.add("13378744756");
//        cellPhones.add("15126767054");
//        cellPhones.add("17806903006");
//        cellPhones.add("13698749847");
//        cellPhones.add("15087052114");
//        cellPhones.add("13180490480");
//        cellPhones.add("15559988727");
//        cellPhones.add("17842225173");
//        String str="17806903006,15126767054,19084231286,13378744756,13179866408,18468003221,18388009229,15008837692,13187653168,19356521812,15887465489,15812266990,15398330142,15187132064,18813140696,18721671512,17787512383,18314168093,15187105684,18287109305,14787151873,18386713298,18508867366,18087837245,18288653851,18183662252,18468073801,15758541236,19987609956,18087692919,15687479272,19169348521,17784645056,18213906584,18076903244,15287994254,18087679948,14787723327,15687476291,18883790409,18987628704,13732754712,18388612682,15187646575,13087462238,18469512184,14769269870,18160749976,13887531395,18487835957,18468272769,13122207606,18787613812,17874724849,15912093690,13208762916,18468110522,18887676887,18908766809,18787613812,13577639877,13312527027,18287109305,19218828665,18788278824,18314431747";
//        for (String cellPhone : str.split(",")) {
//            String s1 = SensitiveInfoUtil.aesEncrypt(cellPhone);
//            System.out.println("\"" +s1+"\",");
//        }
        String s3 = SensitiveInfoUtil.aesEncrypt("19188269715");
        System.out.println("\"" + s3 + "\",");
        String s = SensitiveInfoUtil.aesDecrypt("B8DzffvlG4hv7cm5hc1mCQ==");
        System.out.println("解密信息：" + s);
    }


}
