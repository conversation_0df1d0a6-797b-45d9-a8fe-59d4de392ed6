package com.dongni.basedata.vacuum;

import com.dongni.basedata.bean.BaseDataMongodb;
import com.dongni.common.mongo.IManager;
import com.dongni.commons.mongodb.MongoClientManager;
import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.Test;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gt;
import static com.mongodb.client.model.Updates.set;

/**
 *
 * <AUTHOR>
 * @date 2024/11/01
 */
public class FileVacuumTest {
    
    @Test
    public void test() {
        MongoClientManager basedataMongodb = new MongoClientManager();
        basedataMongodb.setHost("dalao-internal.dongni100.com");
        basedataMongodb.setPort("60803");
        basedataMongodb.setDatabase("base_data");
        basedataMongodb.setUserName("base_data");
        basedataMongodb.setPassword("BaseData9102@)!(");
        
        IManager fileStorageVacuumExamItemManager = new IManager(basedataMongodb, BaseDataMongodb.COLLECTION_FILE_STORAGE_VACUUM_EXAM_ITEM, "清理examItem") {
        };
        IManager fileStorageVacuumExamUploaderManager = new IManager(basedataMongodb, BaseDataMongodb.COLLECTION_FILE_STORAGE_VACUUM_EXAM_UPLOADER, "清理examUploader") {
        };
        
        System.out.println(" ----------------------------------------- examItem");
        List<Document> examItemVacuumList = fileStorageVacuumExamItemManager.getList(gt("vacuumNextDatePure", 0));
        for (Document examItemVacuum : examItemVacuumList) {
            try {
                String lastModifyDateTimeStr = MapUtil.getTrimNullable(examItemVacuum, "itemLastModifyDateTime");
                if (StringUtils.isBlank(lastModifyDateTimeStr)) {
                    lastModifyDateTimeStr = MapUtil.getTrimNullable(examItemVacuum, "examModifyDateTime");
                }
                Date date = DateUtil.parseDateTime(lastModifyDateTimeStr);
                LocalDate lastModifyLocalDate = getLocalDate(date);
                LocalDate vacuumLocalDate = lastModifyLocalDate.plusDays(186);
                int vacuumNextDatePureNew = getDatePureInt(vacuumLocalDate);
                int vacuumNextDatePureOld = MapUtil.getInt(examItemVacuum, "vacuumNextDatePure");
                LocalDate vacuumNextDatePureOldLocalDate = DateUtil.parseDatePure(String.valueOf(vacuumNextDatePureOld));
                long days = vacuumNextDatePureOldLocalDate.toEpochDay() - vacuumLocalDate.toEpochDay();
                if (vacuumNextDatePureOld != vacuumNextDatePureNew) {
                    System.out.println(examItemVacuum.get("_id").toString()
                            + " " + MapUtil.getString(examItemVacuum, "examModifyDateTime", "                   ")
                            + " " + MapUtil.getString(examItemVacuum, "itemLastModifyDateTime", "                   ")
                            + " --- " + vacuumNextDatePureOld + " - " + vacuumNextDatePureNew + " = " + days);
                    fileStorageVacuumExamItemManager.updateOne(and(
                                    eq("_id", new ObjectId(examItemVacuum.get("_id").toString())),
                                    eq("vacuumNextDatePure", vacuumNextDatePureOld)
                            ),
                            set("vacuumNextDatePure", vacuumNextDatePureNew)
                    );
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        
        System.out.println(" ----------------------------------------- examUploader");
        List<Document> examUploaderVacuumList = fileStorageVacuumExamUploaderManager.getList(gt("vacuumNextDatePure", 0));
        for (Document examUploaderVacuum : examUploaderVacuumList) {
            try {
                String examModifyDateTimeStr = MapUtil.getTrimNullable(examUploaderVacuum, "examModifyDateTime");
                String modifyDateTimeStr = MapUtil.getTrimNullable(examUploaderVacuum, "modifyDateTime");
                Date examModifyDateTime = StringUtils.isBlank(examModifyDateTimeStr) ? null : DateUtil.parseDateTime(examModifyDateTimeStr);
                Date modifyDateTime = StringUtils.isBlank(modifyDateTimeStr) ? null : DateUtil.parseDateTime(modifyDateTimeStr);
                Date date = null;
                if (examModifyDateTime != null && modifyDateTime != null) {
                    if (modifyDateTime.getTime() > examModifyDateTime.getTime()) {
                        date = modifyDateTime;
                    } else {
                        date = examModifyDateTime;
                    }
                } else if (examModifyDateTime != null) {
                    date = examModifyDateTime;
                } else if (modifyDateTime != null) {
                    date = modifyDateTime;
                }
                if (date == null) {
                    continue;
                }
                LocalDate lastModifyLocalDate = getLocalDate(date);
                LocalDate vacuumLocalDate = lastModifyLocalDate.plusDays(186);
                int vacuumNextDatePureNew = getDatePureInt(vacuumLocalDate);
                int vacuumNextDatePureOld = MapUtil.getInt(examUploaderVacuum, "vacuumNextDatePure");
                LocalDate vacuumNextDatePureOldLocalDate = DateUtil.parseDatePure(String.valueOf(vacuumNextDatePureOld));
                long days = vacuumNextDatePureOldLocalDate.toEpochDay() - vacuumLocalDate.toEpochDay();
                if (vacuumNextDatePureOld != vacuumNextDatePureNew) {
                    System.out.println(examUploaderVacuum.get("_id").toString()
                            + " " + MapUtil.getString(examUploaderVacuum, "examModifyDateTime", "                   ")
                            + " " + MapUtil.getString(examUploaderVacuum, "modifyDateTime", "                   ")
                            + " --- " + vacuumNextDatePureOld + " - " + vacuumNextDatePureNew + " = " + days);
                    fileStorageVacuumExamUploaderManager.updateOne(and(
                                    eq("_id", new ObjectId(examUploaderVacuum.get("_id").toString())),
                                    eq("vacuumNextDatePure", vacuumNextDatePureOld)
                            ),
                            set("vacuumNextDatePure", vacuumNextDatePureNew)
                    );
                }
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
    }
    
    private LocalDate getLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    
    private int getDatePureInt(LocalDate localDate) {
        String datePureStr = DateUtil.formatDatePure(localDate);
        return MapUtil.getInt(datePureStr);
    }
}
