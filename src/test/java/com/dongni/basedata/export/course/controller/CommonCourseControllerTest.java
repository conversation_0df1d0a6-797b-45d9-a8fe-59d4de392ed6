package com.dongni.basedata.export.course.controller;

import com.dongni.common.auth.DongniClient;
import com.dongni.common.auth.impl.DongniClientAuthNicezhuanyeImpl;
import com.dongni.commons.utils.JSONUtil;
import org.junit.Test;
import org.springframework.web.client.RestTemplate;

/**
 *
 * <AUTHOR>
 * @date 2023/07/06
 */
public class CommonCourseControllerTest {
    
    @Test
    public void testGetInnerCourseForNicezhuanye() {
        DongniClient dongniClient = new DongniClient();
        dongniClient.setRestTemplate(new RestTemplate());
        Object result = dongniClient.post(
                "https://dalao.dongni100.com/api/base/data/export/course/common/a28b155f-fb9f-4e64-860c-22bf5536c836/dnInnerCourse",
                null,
                null,
                new DongniClientAuthNicezhuanyeImpl());
        System.out.println(JSONUtil.toJson(result));
    }
}
