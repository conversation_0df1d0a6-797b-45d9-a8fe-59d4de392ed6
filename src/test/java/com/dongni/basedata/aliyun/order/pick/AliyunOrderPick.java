package com.dongni.basedata.aliyun.order.pick;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.DateUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/09/06
 */
public class AliyunOrderPick {
    
    private static final Logger log = LoggerFactory.getLogger(AliyunOrderPick.class);
    
    static {
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        List<ch.qos.logback.classic.Logger> loggerList = loggerContext.getLoggerList();
        loggerList.forEach(logger -> logger.setLevel(Level.INFO));
    }
    
    /**
     * 关联订单
     */
    public static void pickOrders(String projectId, String partnerName, String cookie) {
        String xsrfToken = getXsrfToken(cookie);
        if (StringUtils.isBlank(xsrfToken)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "鉴权信息(cookie)缺少xsrf-token");
        }
        
        // 鉴权用的头部
        HttpHeaders authHttpHeaders = new HttpHeaders();
        authHttpHeaders.set("Cookie", cookie);
        authHttpHeaders.set("x-xsrf-token", xsrfToken);
        
        // 订单查询参数 一个月的开始及结束时间 如 "2023-10-01" ~ "2023-10-31"
        AliyunOrderPickUtil.MonthInfo monthInfo = AliyunOrderPickUtil.getMonthInfo();
        String beginOfMonthStr = monthInfo.getBeginOfMonthStr();
        String endOfMonthStr = monthInfo.getEndOfMonthStr();
        String pickDateTimeStr = DateUtil.formatDateTime(monthInfo.getDate());
        log.info("阿里云订单关联商机开始 {}~{}: pickDateTimeStr: {}", beginOfMonthStr, endOfMonthStr, pickDateTimeStr);
        
        boolean anyError = false;
        int errorTimes = 0;
        int errorTimesMax = 3;
        
        int waitPickPageSize = 20;
        int allOrderTotalBase = Integer.MIN_VALUE;
        int allOrderPageMax = 0;
        int waitPickPageNo = 1;
        while (true) {
            // 未关联的订单信息
            Map<String, Object> allOrderListInfo = AliyunOrderPickApi.waitPickList(authHttpHeaders, waitPickPageNo, waitPickPageSize, beginOfMonthStr, endOfMonthStr);
            Map<String, Object> allOrderPagination = MapUtil.getCast(allOrderListInfo, "pagination");
            int allOrderTotal = MapUtil.getInt(allOrderPagination, "total", 0);
            if (allOrderTotal <= 0) {
                log.info("阿里云订单关联商机 {}~{} 订单数为{}，其小于等于0, 退出", beginOfMonthStr, endOfMonthStr, allOrderTotal);
                break;
            }
            
            // 基准数据未设置，则设置
            if (allOrderTotalBase == Integer.MIN_VALUE) {
                allOrderTotalBase = allOrderTotal;
                allOrderPageMax = (allOrderTotalBase / waitPickPageSize) + (allOrderTotalBase % waitPickPageSize > 0 ? 1 : 0);
                log.info("阿里云订单关联商机 {}~{} 订单数为{}, 每页{}条, 共{}页，设置基准数据 =======================================",
                        beginOfMonthStr, endOfMonthStr, allOrderTotalBase, waitPickPageSize, allOrderPageMax);
            }
            
            // 如果基准不等于新获取的值，则表示在处理的过程中又有新的订单，应该从第一页开始处理
            if (allOrderTotalBase != allOrderTotal) {
                log.info("阿里云订单关联商机 {}~{} 订单数为{}，不等于上次的订单数{}, 重新从第一页进行处理", beginOfMonthStr, endOfMonthStr, allOrderTotal, allOrderTotalBase);
                allOrderTotalBase = Integer.MIN_VALUE;
                waitPickPageNo = 1;
                continue;
            }
            
            // 未关联的订单id信息
            List<Map<String, Object>> allOrderList = MapUtil.getCast(allOrderListInfo, "list");
            if (allOrderList == null) { allOrderList = new ArrayList<>(); }
            
            log.info("====== 阿里云订单关联商机: allOrderTotalBase: {}; page:{}/{};", allOrderTotalBase, waitPickPageNo, allOrderPageMax);
            
            for (int i = 0, iLen = allOrderList.size(); i < iLen; i++) {
                Map<String, Object> waitPick = allOrderList.get(i);
                String orderId = MapUtil.getTrim(waitPick, "orderId");
                if (MapUtil.getBoolean(waitPick, "isAlreadyPick")) {
                    continue;
                }
                
                try {
                    // 检查是否可以关联
                    Map<String, Object> checkCanPickInfo = AliyunOrderPickApi.checkCanPick(authHttpHeaders, projectId, orderId);
                    String checkPartnerName = MapUtil.getTrim(checkCanPickInfo, "partnerName");
                    if (!partnerName.equals(checkPartnerName)) {
                        log.error("阿里云订单关联商机: 配置出错; 阿里云订单关联合作伙伴名称不一致; 配置partnerName:{}; 阿里返回值partnerName:{}", partnerName, checkPartnerName);
                        return;
                    }
                    // 关联订单
                    AliyunOrderPickApi.pick(authHttpHeaders, projectId, orderId);
                    log.info("★ 阿里云订单关联商机: orderId: {}; ({}/{})", orderId, i + 1, iLen);
                } catch (Exception e) {
                    if (!anyError) {
                        anyError = true;
                        errorTimes++;
                    }
                    log.error("☆ 阿里云订单关联商机: orderId: {}; ({}/{}); error: {}", orderId, i + 1, iLen, e.getMessage());
                }
            }
            
            // 还未到最后一页 则继续下一页
            if (waitPickPageNo * waitPickPageSize < allOrderTotal) {
                waitPickPageNo++;
                continue;
            }
            
            // 到最后一页了 如果没有出现错误 则可以结束了
            if (!anyError) {
                log.info("阿里云订单关联商机: 正常结束; errorTimes: {}", errorTimes);
                break;
            }
            
            // 如果出现错误了 则需要判断错误的次数
            if (errorTimes >= errorTimesMax) {
                log.error("阿里云订单关联商机: 结束; errorTimes: {}", errorTimes);
                break;
            }
            
            // 如果出现错误了 但是错误的次数还没到最大值 则需要重新从第一页开始处理
            // 原因: 有些订单是有关系的，比如订单1 订单2 订单3；订单1是不能关联的，一直会报错，而订单2订单3关联成功后，订单1才能关联或者不需要关联了其自动关联上了
            log.error("阿里云订单关联商机: 出现错误了，但是错误的次数还没到最大值，重新从第一页开始处理");
            anyError = false;
            allOrderTotalBase = Integer.MIN_VALUE;
            waitPickPageNo = 1;
        }
        
        log.info("阿里云订单关联商机结束 {}~{}: pickDateTimeStr: {}", beginOfMonthStr, endOfMonthStr, pickDateTimeStr);
    }
    
    /**
     * 获取cookie中的 XSRF-TOKEN 值
     * @param cookie cookie
     * @return XSRF-TOKEN值
     */
    private static String getXsrfToken(String cookie) {
        String[] cookieSplits = cookie.split(";");
        for (String keyValue : cookieSplits) {
            keyValue = keyValue.trim();
            String[] keyValueSplits = keyValue.split("=");
            String key = keyValueSplits[0];
            if ("XSRF-TOKEN".equalsIgnoreCase(key)) {
                return keyValueSplits[1];
            }
        }
        return null;
    }
}
