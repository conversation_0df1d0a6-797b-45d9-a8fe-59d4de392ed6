package com.dongni.basedata.aliyun.order.pick;

import org.junit.Test;

/**
 * https://scm.nicezhuanye.com/confluence/pages/viewpage.action?pageId=78813178
 * <AUTHOR>
 * @date 2023/09/08
 */
public class AliyunOrderPickTest {
    
    @Test
    public void test() {
        // 中科九洲科技股份有限公司 刘兆学 提供的合作id
        String projectId = "202307002090";
        String partnerName = "中科九洲科技股份有限公司";
        // 阿里云控制台 右上角 企业 -> 合作伙伴项目 -> F12 -> 复制cookie信息
        String cookie = "";
        AliyunOrderPick.pickOrders(projectId, partnerName, cookie);
    }
}
