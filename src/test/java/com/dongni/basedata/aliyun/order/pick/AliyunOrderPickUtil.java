package com.dongni.basedata.aliyun.order.pick;

import cn.hutool.core.date.DateUtil;
import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;

import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2023/09/06
 */
public class AliyunOrderPickUtil {
    
    private AliyunOrderPickUtil() {
        throw new CommonException(ResponseStatusEnum.FAILURE, "private");
    }
    
    public static MonthInfo getMonthInfo() {
        return new MonthInfo();
    }
    
    public static class MonthInfo {
        /** 计算的时间 */
        private final Date date;
        /** 时间的月份 1~12 */
        private final int month;
        /** 计算的时间的月份的第一天日期 */
        private final Date beginOfMonth;
        /** 计算的时间的月份的最后一天日期 */
        private final Date endOfMonth;
        
        /** 计算的时间的月份的第一天日期 yyyy-MM-dd */
        private final String beginOfMonthStr;
        /** 计算的时间的月份的最后一天日期 yyyy-MM-dd */
        private final String endOfMonthStr;
        
        public MonthInfo() {
            this(new Date());
        }
        
        public MonthInfo(Date date) {
            this.date = date;
            this.month = DateUtil.month(date);
            this.beginOfMonth = DateUtil.beginOfMonth(date);
            this.endOfMonth = DateUtil.endOfMonth(date);
            this.beginOfMonthStr = DateUtil.formatDate(this.beginOfMonth);
            this.endOfMonthStr = DateUtil.formatDate(this.endOfMonth);
        }
        
        public Date getDate() {
            return date;
        }
        
        public int getMonth() {
            return month;
        }
        
        public Date getBeginOfMonth() {
            return beginOfMonth;
        }
        
        public Date getEndOfMonth() {
            return endOfMonth;
        }
        
        public String getBeginOfMonthStr() {
            return beginOfMonthStr;
        }
        
        public String getEndOfMonthStr() {
            return endOfMonthStr;
        }
    }
}
