package com.dongni.basedata.aliyun.order.pick;

import com.dongni.basedata.log.manager.AccessLogManager;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.exam.mark.bean.vo.req.IntelliPriReq;
import com.dongni.exam.mark.constant.CommonConstant;
import com.dongni.exam.mark.constant.MarkRedisKey;
import com.yunpian.sdk.util.JsonUtil;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Filters.eq;

/**
 * <AUTHOR>
 * @date 2025/4/1
 * @desc
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class AccessLogTest {

    @Autowired
    private AccessLogManager baseDataMongodb;

    @Test
    public void test() {
        Bson query = and(eq("userId", 26755079), eq("uri", "/exam/intelligence/addSchoolPri"));

        List<Document> list = baseDataMongodb.getList(query, new String[]{"data"}, CommonConstant.BATCH_SQL_COUNT);
        String[] keys = list.stream().map(x -> x.getString("data")).map(x -> {
            IntelliPriReq intelliPriReq = JsonUtil.fromJson(x, IntelliPriReq.class);
            return MarkRedisKey.getSchoolIntelligenceValue(intelliPriReq.getSchoolId(), intelliPriReq.getCourseId(), intelliPriReq.getUnitType());
        }).toArray(String[]::new);
        Long execute = JedisTemplate.execute(jedis -> jedis.sadd(MarkRedisKey.INTELLIGENCE_KEY, keys));
        System.out.println(execute);
    }
}
