package com.dongni.basedata.aliyun.order.pick;

import com.dongni.commons.enumeration.ResponseStatusEnum;
import com.dongni.commons.exception.CommonException;
import com.dongni.commons.utils.HttpUtil;
import com.dongni.commons.utils.JSONUtil;
import com.dongni.tiku.common.util.MapUtil;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2023/09/05
 */
public class AliyunOrderPickApi {
    
    private static final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 获取待关联订单信息 实际上是所有订单信息 需要通过字段区分是否已经关联
     * @param authHttpHeaders 鉴权用 需提供 cookie: login_aliyunid_ticket=xxx
     * @param pageNo          页码
     * @param pageSize        每页数量
     * @param startTime       开始时间 "2023-09-01"
     * @param endTime         结束时间 "2023-09-09"
     * @return {
     *             "list": [
     *                 {
     *                     "orderId": "226657450910967",
     *                     "projectId": null,         // 关联过的会有合作的id
     *                     "isAlreadyPick": false,    // 是否已经关联过了
     *                     ...
     *                 }
     *             ],
     *             ],
     *             "pagination": {
     *                 "current": 1,
     *                 "pageSize": 20,
     *                 "total": 108
     *             }
     *         }
     */
    public static Map<String, Object> waitPickList(HttpHeaders authHttpHeaders,
                                                   int pageNo, int pageSize,
                                                   String startTime, String endTime) {
        String httpUrl = "https://aps.aliyun.com/api/v1/order/waitPickList";
        // timeType == 1 为 支付时间
        int timeType = 1;
        Map<String, Object> urlParams = MapUtil.of(
                "current", pageNo,
                "pageSize", pageSize,
                "timeType", timeType,
                "startTime", startTime,
                "endTime", endTime
        );
        return exchangeData(HttpMethod.GET, httpUrl, authHttpHeaders, urlParams, null);
    }
    
    /**
     * 获取已关联订单信息
     * @param authHttpHeaders 鉴权用 需提供 cookie: login_aliyunid_ticket=xxx
     * @param pageNo          页码
     * @param pageSize        每页数量
     * @param startTime       开始时间 "2023-09-01"
     * @param endTime         结束时间 "2023-09-09"
     * @return {
     *             "list": [
     *                 {
     *                     "orderId": "226657450910967",
     *                     "projectId": "202307002090",  // 关联的合作id
     *                     "isAlreadyPick": true,        // 已经关联过了
     *                     ...
     *                 }
     *             ],
     *             ],
     *             "pagination": {
     *                 "current": 5,
     *                 "pageSize": 20,
     *                 "total": 1
     *             }
     *         }
     */
    public static Map<String, Object> alreadyPickList(HttpHeaders authHttpHeaders,
                                                      int pageNo, int pageSize,
                                                      String startTime, String endTime) {
        String httpUrl = "https://aps.aliyun.com/api/v1/order/alreadyPickList";
        // timeType == 1 为 支付时间
        int timeType = 1;
        Map<String, Object> urlParams = MapUtil.of(
                "current", pageNo,
                "pageSize", pageSize,
                "timeType", timeType,
                "startTime", startTime,
                "endTime", endTime
        );
        return exchangeData(HttpMethod.GET, httpUrl, authHttpHeaders, urlParams, null);
    }
    
    /**
     * 检查是否可以关联
     * @param authHttpHeaders 鉴权用 需提供 cookie: login_aliyunid_ticket=xxx
     *                                     X-XSRF-TOKEN: xxx
     * @param projectId      合作id
     * @param orderIdListStr 关联订单信息 "123,456,789"
     * @return {
     *             "projectId": 202307002090,
     *             "partnerName": "中科九洲科技股份有限公司",
     *             "pickOrderList": [123,456,789],
     *             "canPick": true,   // 这个字段没啥用，在校验不通过时，body.message不为success
     *             "testFlag": false
     *         }
     */
    public static Map<String, Object> checkCanPick(HttpHeaders authHttpHeaders,
                                                   String projectId,
                                                   String orderIdListStr) {
        String httpUrl = "https://aps.aliyun.com/api/v1/order/checkCanPick";
        Map<String, Object> postParams = MapUtil.of(
                "projectId", projectId,
                "orderIdList", orderIdListStr
        );
        Map<String, Object> body = exchange(HttpMethod.POST, httpUrl, authHttpHeaders, null, postParams);
        String message = MapUtil.getTrim(body, "message", "");
        if (!"success".equalsIgnoreCase(message)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联接口:检查订单是否可以关联失败: " + message);
        }
        return MapUtil.getCast(body, "data");
    }
    
    /**
     * 订单关联
     * @param authHttpHeaders 鉴权用 需提供 cookie: login_aliyunid_ticket=xxx
     *                                     X-XSRF-TOKEN: xxx
     * @param projectId      合作id
     * @param orderIdListStr 关联订单信息 "123,456,789"
     * @return {
     *             "projectId": 202307002090,
     *             "pickOrderList": [123,456,789]
     *         }
     */
    public static Map<String, Object> pick(HttpHeaders authHttpHeaders,
                                           String projectId,
                                           String orderIdListStr) {
        String httpUrl = "https://aps.aliyun.com/api/v1/order/pick";
        Map<String, Object> postParams = MapUtil.of(
                "projectId", projectId,
                "orderIdList", orderIdListStr
        );
        Map<String, Object> body = exchange(HttpMethod.POST, httpUrl, authHttpHeaders, null, postParams);
        String message = MapUtil.getTrim(body, "message", "");
        if (!"success".equalsIgnoreCase(message)) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联接口:订单关联失败: " + message);
        }
        return MapUtil.getCast(body, "data");
    }
    
    // ------------------------------------------------------------------------ exchange
    
    /**
     * 调用阿里云订单关联商机接口
     * @param httpMethod      请求方法
     * @param httpUrl         http url
     * @param authHttpHeaders 校验请求头
     * @param urlParams       url参数
     * @param bodyParams      body参数
     * @return 响应信息的data数据
     */
    private static <T> T exchangeData(HttpMethod httpMethod, String httpUrl, HttpHeaders authHttpHeaders, Map<String, Object> urlParams, Map<String, Object> bodyParams) {
        Map<String, Object> body = exchange(httpMethod, httpUrl, authHttpHeaders, urlParams, bodyParams);
        return MapUtil.getCast(body, "data");
    }
    
    /**
     * 调用阿里云订单关联商机接口
     * @param httpMethod      请求方法
     * @param httpUrl         http url
     * @param authHttpHeaders 校验请求头
     * @param urlParams       url参数
     * @param bodyParams      body参数
     * @return 响应信息
     */
    private static Map<String, Object> exchange(HttpMethod httpMethod, String httpUrl, HttpHeaders authHttpHeaders, Map<String, Object> urlParams, Map<String, Object> bodyParams) {
        URI uri = HttpUtil.getUri(httpUrl, true, urlParams, null);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(bodyParams, authHttpHeaders);
        ResponseEntity<String> response = restTemplate.exchange(uri, httpMethod, httpEntity, String.class);
        String bodyString = response.getBody();
        HttpStatus statusCode = response.getStatusCode();
        if (statusCode != HttpStatus.OK) {
            throw new CommonException(ResponseStatusEnum.FAILURE, "阿里云订单关联调用异常: (HttpStatus:" + statusCode + ")" + httpUrl + "; response:" + bodyString);
        }
        Map<String, Object> body;
        try {
            body = JSONUtil.parseToMap(bodyString);
        } catch (Exception e) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联接口返回数据异常(json解析失败): " + httpUrl + "; response:" + bodyString);
        }
        Integer code = MapUtil.getIntNullable(body, "code");
        if (code == null) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联接口返回数据异常(code为null): " + httpUrl + "; response:" + bodyString);
        }
        if (code == 1001) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联登录态无效: " + httpUrl + "; body:" + bodyString);
        }
        if (code != 200) {
            throw new CommonException(ResponseStatusEnum.DATA_ERROR, "阿里云订单关联接口返回状态异常: (code!=200): " + httpUrl + "; response:" + bodyString);
        }
        return body;
    }
    
}
