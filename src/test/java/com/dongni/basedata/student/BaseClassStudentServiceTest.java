package com.dongni.basedata.student;


import com.dongni.basedata.bean.BaseDataMongodb;
import com.dongni.basedata.bean.BaseDataRepository;
import com.dongni.basedata.export.grade.service.CommonGradeService;
import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataClass;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassService;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataClassStudentService;
import com.dongni.basedata.school.grade.service.impl.GradeServiceImpl;
import com.dongni.basedata.school.student.service.StudentCourseSelectionService;
import com.dongni.commons.redis.template.JedisTemplate;
import com.dongni.commons.utils.DateUtil;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.commons.utils.verify.ObjectUtil;
import com.dongni.exam.bean.ExamRepository;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.vo.CourseTeacherVO;
import com.dongni.exam.common.mark.vo.PageTchVO;
import com.dongni.tiku.common.util.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;
import java.util.stream.Collectors;

import static com.mongodb.client.model.Filters.eq;
import static java.util.stream.Collectors.toMap;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class BaseClassStudentServiceTest {

    @Autowired
    private IBaseDataClassStudentService baseDataClassStudentService;
    @Autowired
    ExamRepository commonRepository;
    @Autowired
    private BaseDataRepository baseDataRepository;
    @Autowired
    private CommonGradeService commonGradeService;
    @Autowired
    private BaseDataMongodb mongodb;
    @Autowired
    private GradeServiceImpl gradeService;
    @Autowired
    private IBaseDataClassService baseDataClassService;


    @Test
    public void udat() {
        List<Long> gradeIdList = baseDataRepository.selectList("GradeMapper.getAllGradeId");
        for (Long gradeId : gradeIdList) {
            List<BaseDataClass> baseDataClassList = baseDataClassService.getBaseDataClassByGradeId(gradeId);
            baseDataClassList.sort((o1, o2) -> o1.getClassType().compareTo(o2.getClassType()));
            Map<String, Object> params = new HashMap<>();
            params.put("currentTime", DateUtil.getCurrentDateTime());
            params.put("userId", "1");
            params.put("userName", "admin");
            List<Long> classIdList = baseDataClassList.stream().map(BaseDataClass::getClassId).collect(Collectors.toList());
            for (int i = 0; i < classIdList.size(); i++) {
                Map<String, Object> updateParams = new HashMap<>(params);
                updateParams.put("classId", classIdList.get(i));
                updateParams.put("classSort", i + 1);
                baseDataRepository.update("ClassesMapper.updateClassesSort", updateParams);
            }
            log.info("更新班级排序完成：{}", gradeId);
        }
    }

    @Test
    public void updateGradeConfig() {
        List<Map<String, Object>> courseList = baseDataRepository.selectList("CourseMapper.getCourse");
        Map<Long, Map<String, Object>> courseMap = courseList.stream().collect(toMap(c -> Long.parseLong(c.get("courseId").toString()), c -> c));
        List<Map<String, Object>> gradeList = baseDataRepository.selectList("GradeMapper.getAllGradeId");
        for (Map<String, Object> gradeDetail : gradeList) {
            gradeDetail.put("stage", 2);
            Document gradeConfig = commonGradeService.getGradeConfig(gradeDetail);
            if (gradeConfig != null) {
                Document pValue = (Document) gradeConfig.get("pValue");
                List<Document> documents = (List<Document>) pValue.get("course");
                Boolean init = false;
                for (Document document : documents) {
                    long courseId = Long.parseLong(document.get("courseId").toString());
                    Map<String, Object> course = courseMap.get(courseId);
                    if (course != null) {
                        if (!ObjectUtil.isValueEquals(course.get("stage"), 2)) {
                            log.info("课程：{}", document);
                            init = true;
                            break;
                        }
                    }
                }
                if (init) {
                    mongodb.getMongoDatabase().getCollection("gradeConfig").deleteOne(eq("gradeId", Long.valueOf(gradeDetail.get("gradeId").toString())));
                    gradeService.initConfig(gradeDetail);
                }
            } else {
                gradeService.initConfig(gradeDetail);
            }
        }
    }


    @Test
    public void getDuplicateClassCourseStudent() {
        List<Long> adminClassIdsByStudentIds = baseDataClassStudentService.getAdminClassIdsByStudentIds(Lists.newArrayList(10L));
        System.out.println(adminClassIdsByStudentIds);


    }


    private String schoolSql = "INSERT INTO `t_school_correct` (`school_id`, `stage`, `course_id`, `unit_type`, `correct_type`, `creator_id`, `creator_name`, `create_date_time`, `modifier_id`, `modifier_name`, `modify_date_time`) VALUES (%s, %s, %s, %s, 1, 1, '管理员', now(), 1, '管理员', now());";
    private String examSql = "INSERT INTO `t_exam_correct` (`exam_name`, `exam_id`, `course_id`, `unit_type`, `correct_type`, `status`, `creator_id`, `creator_name`, `create_date_time`, `modifier_id`, `modifier_name`, `modify_date_time`) VALUES ('%s', %s, %s, %s, 1, 1, 1, '管理员', now(), 1, '管理员', now());";

    @Test
    public void jedis() {
        Set<String> execute = JedisTemplate.execute(jedis -> {
            return jedis.smembers("EXAM:INTELLIGENCE");
        });
        List<Map<String, String>> schoolList = new ArrayList<>();
        List<Map<String, String>> examList = new ArrayList<>();
        Set<Long> courseIdSet = new HashSet<>();
        for (String s : execute) {
//            System.out.println(s);
            String[] split = s.split(":");
            if (split[0].equals("S")) {
                //学校授权
                String schoolId = split[1];
                String courseId = split[2];
                courseIdSet.add(Long.parseLong(courseId));
                String unitType = split[3];
                schoolList.add(MapUtil.of("schoolId", schoolId, "courseId", courseId, "unitType", unitType));
            } else if (split[0].equals("E")) {
                String examId = split[1];
                String courseId = split[2];
                String unitType = split[3];
                examList.add(MapUtil.of("examId", examId, "courseId", courseId, "unitType", unitType));
                Map<String, Object> exam = commonRepository.selectOne("ExamMapper.getExam", MapUtil.of("examId", examId));
                if (exam != null) {
                    String examName = MapUtil.getString(exam, "examName");
                    String examTempSql = String.format(examSql, examName, examId, courseId, unitType);
                    System.out.println(examTempSql);
                } else {
//                    System.out.println("examId:" + examId);
                }
            }
        }
        List<Map<String, Object>> courseList = baseDataRepository.selectList("CommonCourseMapper.getCourseByIds", MapUtil.of("courseIds", new ArrayList(courseIdSet)));
        Map<String, Map<String, Object>> courseMap = courseList.stream().collect(toMap(c -> c.get("courseId").toString(), c -> c));
        for (Map<String, String> school : schoolList) {
            String courseId = school.get("courseId");
            String schoolId = school.get("schoolId");
            String unitType = school.get("unitType");
            Map<String, Object> course = courseMap.get(courseId);
            if (course != null) {
                String stage = MapUtil.getString(course, "stage");
                String tempSql = String.format(schoolSql, schoolId, stage, courseId, unitType);
                System.out.println(tempSql);
            } else {
//                System.out.println("courseId:" + courseId);
            }
        }
    }


}
