package com.dongni.basedata.student;


import com.dongni.basedata.export.student.service.CommonStudentService;
import com.dongni.basedata.school.client.schoolClassStructure.bean.BaseDataCourse;
import com.dongni.basedata.school.client.schoolClassStructure.serivce.IBaseDataCourseService;
import com.dongni.basedata.school.student.service.StudentCourseSelectionService;
import com.dongni.commons.utils.SensitiveInfoUtil;
import com.dongni.exam.common.mark.serivice.basedata.ISchoolTeacherService;
import com.dongni.exam.common.mark.vo.CourseTeacherVO;
import com.dongni.exam.common.mark.vo.PageTchVO;
import com.dongni.tiku.common.util.MapUtil;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class StudentCourseSelectionCommonServiceTest {

    @Autowired
    private StudentCourseSelectionService studentCourseSelectionService;
    @Autowired
    private ISchoolTeacherService schoolTeacherService;
    @Autowired
    private CommonStudentService commonStudentService;
    @Autowired
    private IBaseDataCourseService baseDataCourseService;

    @Test
    public void getDuplicateClassCourseStudent1() {
        List<BaseDataCourse> comprehensiveCourseInfoByCourseId = baseDataCourseService.getComprehensiveCourseInfoByCourseId(2L, 3L);
        System.out.println(comprehensiveCourseInfoByCourseId);
    }

    @Test
    public void getDuplicateClassCourseStudent() {
        String classId = "693111, 693131, 693151, 693171, 693191, 692641, 722601, 722621, 722641, 722661, 722681, 693091, 722701, 722721," +
                "                                 722741, 722761, 692661, 722781, 722801, 722821, 722841, 722861, 693071, 722881, 722901, 722921, 722941, 692681," +
                "                                 722961, 722981, 723001, 723021, 693051, 692701, 693031, 723041, 692721, 693011, 692741, 692991, 692761, 692971," +
                "                                 692781, 692951, 692801, 692941, 692821, 692921, 692841, 692901, 692631, 692861, 692881, 692632, 692882, 692862," +
                "                                 692902, 692842, 692922, 692822, 692942, 692802, 692952, 692782, 692972, 692762, 723042, 692992, 692742, 693012," +
                "                                 692722, 693032, 692702, 693052, 723022, 723002, 722982, 722962, 692682, 722942, 722922, 722902, 722882, 722862," +
                "                                 693072, 722842, 722822, 722802, 722782, 692662, 722762, 722742, 722722, 722702, 722682, 693092, 722662,62606";
        List<Map<String, Object>> classStudent0 = commonStudentService.getDuplicateClassCourseStudent(MapUtil.of("classIdList", Arrays.asList(classId.split(","))
                , "courseIdList", Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
        ));
        System.out.println(classStudent0);


    }

    @Test
    public void classStudent() {
        String classId = "693111, 693131, 693151, 693171, 693191, 692641, 722601, 722621, 722641, 722661, 722681, 693091, 722701, 722721," +
                "                                 722741, 722761, 692661, 722781, 722801, 722821, 722841, 722861, 693071, 722881, 722901, 722921, 722941, 692681," +
                "                                 722961, 722981, 723001, 723021, 693051, 692701, 693031, 723041, 692721, 693011, 692741, 692991, 692761, 692971," +
                "                                 692781, 692951, 692801, 692941, 692821, 692921, 692841, 692901, 692631, 692861, 692881, 692632, 692882, 692862," +
                "                                 692902, 692842, 692922, 692822, 692942, 692802, 692952, 692782, 692972, 692762, 723042, 692992, 692742, 693012," +
                "                                 692722, 693032, 692702, 693052, 723022, 723002, 722982, 722962, 692682, 722942, 722922, 722902, 722882, 722862," +
                "                                 693072, 722842, 722822, 722802, 722782, 692662, 722762, 722742, 722722, 722702, 722682, 693092, 722662,62606";
        List<Map<String, Object>> classStudent0 = commonStudentService.getClassStudent(MapUtil.of("classIds", classId
                , "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudent0);
        List<Map<String, Object>> classStudent = commonStudentService.getClassStudent(MapUtil.of("classIds", classId
                , "tagId", 1, "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudent);
        List<Map<String, Object>> classStudent1 = commonStudentService.getClassStudent(MapUtil.of("classIds", classId
                , "tagIds", Lists.newArrayList(1, 2), "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudent1);


        Map<String, Object> classStudentMap0 = commonStudentService.getClassStudentMap(MapUtil.of("classIds", classId
                , "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudentMap0);

        Map<String, Object> classStudentMap = commonStudentService.getClassStudentMap(MapUtil.of("classIds", classId
                , "tagId", 1, "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudentMap);
        Map<String, Object> classStudentMap1 = commonStudentService.getClassStudentMap(MapUtil.of("classIds", classId
                , "tagIds", Lists.newArrayList(1, 2), "currentIndex", 0, "pageSize", 5
        ));
        System.out.println(classStudentMap1);


    }

    @Test
    public void listTeach() {

        List<CourseTeacherVO> courseTeacherVOList = schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 2l, 11);
        PageTchVO courseTeacherVOList1 = schoolTeacherService.listTch(Lists.newArrayList(192l), "", 2l, 11, 0, 10);

        schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 0l, 11);
        schoolTeacherService.listTch(Lists.newArrayList(192l), "", 0l, 11, 0, 10);

        schoolTeacherService.listTch(Lists.newArrayList(128276l, 128267l), "", 0l, 0);
        schoolTeacherService.listTch(Lists.newArrayList(192l), "", 0l, 0, 0, 10);


    }

    @Test
    public void saveStudentCourseSelection4NullForeignCourseId() {
        Map<String, Object> studentParams = new HashMap<>();

        studentParams.put("studentId", 110656595363L);
        studentParams.put("courseSelectionGroupId", 4L);
        studentParams.put("foreignCourseId", null);
        studentParams.put("userId", 1L);
        studentParams.put("userName", "admin");

        studentCourseSelectionService.saveStudentCourseSelection(studentParams);
    }

    @Test
    public void saveStudentCourseSelection4Null() {
        Map<String, Object> studentParams = new HashMap<>();

        studentParams.put("studentId", 110656595363L);
        studentParams.put("courseSelectionGroupId", null);
        studentParams.put("foreignCourseId", null);
        studentParams.put("userId", 1L);
        studentParams.put("userName", "admin");

        studentCourseSelectionService.saveStudentCourseSelection(studentParams);
    }

    @Test
    public void saveStudentCourseSelection() {
        Map<String, Object> studentParams = new HashMap<>();

        studentParams.put("studentId", 110656595363L);
        studentParams.put("courseSelectionGroupId", 4L);
        studentParams.put("foreignCourseId", 4L);
        studentParams.put("userId", 1L);
        studentParams.put("userName", "admin");

        studentCourseSelectionService.saveStudentCourseSelection(studentParams);
    }

    @Test
    public void updateStudentCourseSelection4NullForeignCourseId() {
        Map<String, Object> studentParams = new HashMap<>();

        studentParams.put("studentId", 110656595363L);
        studentParams.put("courseSelectionGroupId", 4L);
        studentParams.put("foreignCourseId", 4L);
        studentParams.put("userId", 1L);
        studentParams.put("userName", "admin");

        studentCourseSelectionService.updateStudentCourseSelection(studentParams);
    }

    @Test
    public void getPhone() {
//        List<String> cellPhones = new ArrayList<>();
//        cellPhones.add("13378744756");
//        cellPhones.add("15126767054");
//        cellPhones.add("17806903006");
//        cellPhones.add("13698749847");
//        cellPhones.add("15087052114");
//        cellPhones.add("13180490480");
//        cellPhones.add("15559988727");
//        cellPhones.add("17842225173");
//        String str="17806903006,15126767054,19084231286,13378744756,13179866408,18468003221,18388009229,15008837692,13187653168,19356521812,15887465489,15812266990,15398330142,15187132064,18813140696,18721671512,17787512383,18314168093,15187105684,18287109305,14787151873,18386713298,18508867366,18087837245,18288653851,18183662252,18468073801,15758541236,19987609956,18087692919,15687479272,19169348521,17784645056,18213906584,18076903244,15287994254,18087679948,14787723327,15687476291,18883790409,18987628704,13732754712,18388612682,15187646575,13087462238,18469512184,14769269870,18160749976,13887531395,18487835957,18468272769,13122207606,18787613812,17874724849,15912093690,13208762916,18468110522,18887676887,18908766809,18787613812,13577639877,13312527027,18287109305,19218828665,18788278824,18314431747";
//        for (String cellPhone : str.split(",")) {
//            String s1 = SensitiveInfoUtil.aesEncrypt(cellPhone);
//            System.out.println("\"" +s1+"\",");
//        }
        String s3 = SensitiveInfoUtil.aesEncrypt("19188269715");
        System.out.println("\"" + s3 + "\",");
        String s = SensitiveInfoUtil.aesDecrypt("B8DzffvlG4hv7cm5hc1mCQ==");
        System.out.println("解密信息：" + s);
    }

}
