package com.dongni.basedata.website;

import com.alibaba.fastjson.JSON;
import com.dongni.basedata.dn.service.DNWebsiteService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2020/05/14 10:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class DNWebsiteServiceTest {

    @Autowired
    DNWebsiteService dnWebsiteService;

    @Test
    public void dnWebsiteServiceTest() {
        Map<String, Object> hashMap = new HashMap<>();

        hashMap.put("name", "马化腾");
        hashMap.put("mobile", "18888888888");
        hashMap.put("companyName", "腾讯");
        hashMap.put("province", "广东省");
        hashMap.put("city", "深圳市");
//        hashMap.put("message", "合作信息");
        System.out.println(JSON.toJSONString(hashMap));
        dnWebsiteService.saveMessage(hashMap);
    }

    @Test
    public void queryMessageTest() {
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("name", "马");
        hashMap.put("mobile", "188");
        hashMap.put("companyName", "腾讯");
        hashMap.put("province", "广东");
        hashMap.put("city", "深圳");
        hashMap.put("message", "合作信息");
        System.out.println(JSON.toJSONString(hashMap));
        System.out.println(JSON.toJSONString(dnWebsiteService.queryMessage(hashMap)));
    }


}
