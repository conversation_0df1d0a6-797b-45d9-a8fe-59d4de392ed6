package com.dongni.third.putian.service;

import com.dongni.third.base.service.ThirdNotificationService;
import com.dongni.third.putian.service.notification.PuTianNotificationService;
import com.dongni.tiku.common.util.MapUtil;
import org.bson.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR> <br/>
 * @date 2020/03/03 <br/>
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class PuTianNotificationServiceTest {
    
    private final static Logger log = LoggerFactory.getLogger(PuTianNotificationServiceTest.class);
    
    @Autowired
    private PuTianNotificationService puTianNotificationService;
    @Autowired
    private ThirdNotificationService thirdNotificationService;
    
    @Test
    public void insert() {
        puTianNotificationService.receiveNotification(MapUtil.of(
                "hello", "world",
                "德玛西亚", "啦啦啦"
        ));
    }
    
    @Test
    public void select() {
        List<Document> select = thirdNotificationService.select(MapUtil.of(
                "thirdPartyId", "1",
                "status", "1"
        ));
        select.forEach(System.out::println);
    }
    
//    @Test
//    public void syncDataByNotification() {
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 1,  "thirdSchoolIdList", Arrays.asList(0L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 2,  "thirdSchoolIdList", Arrays.asList(0L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 3,  "thirdSchoolIdList", Arrays.asList(1L,2L),    "parseError", true));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 4,  "thirdSchoolIdList", Arrays.asList(1L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 5,  "thirdSchoolIdList", Arrays.asList(0L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 6,  "thirdSchoolIdList", Arrays.asList(null,2L),  "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 7,  "thirdSchoolIdList", Arrays.asList(1L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 8,  "thirdSchoolIdList", Arrays.asList(1L,2L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 9,  "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 10, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", true));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 11, "thirdSchoolIdList", Arrays.asList(null,4L),  "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 12, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 13, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 14, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 15, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 16, "thirdSchoolIdList", Arrays.asList(3L,4L),    "parseError", true));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 17, "thirdSchoolIdList", Arrays.asList(5L,6L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 18, "thirdSchoolIdList", Arrays.asList(5L,6L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 19, "thirdSchoolIdList", Arrays.asList(0L),    "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 20, "thirdSchoolIdList", Arrays.asList(5L,6L,7L), "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 21, "thirdSchoolIdList", Arrays.asList(5L,6L,7L), "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 22, "thirdSchoolIdList", Arrays.asList(5L,6L,7L), "parseError", false));
//        puTianNotificationService.receiveNotification(MapUtil.of("testId", 23, "thirdSchoolIdList", Arrays.asList(0L), "parseError", false));
//
//
//        Long thirdPartyId = PutianDataUtil.getThirdPartyId();
//        // 1. 从mongo获取需要推送的消息 status = 1
//        List<Document> notificationInfoList = thirdNotificationService.select(
//                MapUtil.of("status", 1, "thirdPartyId", thirdPartyId)
//        );
//        if (notificationInfoList.isEmpty()) {
//            return;
//        }
//
//        List<String> successIdList = new ArrayList<>();
//        // 2. 解析通知 失败标记 status = 2
//        List<Map<String, Object>> allSyncParamsList = new ArrayList<>();
//        for (Document notificationInfo : notificationInfoList) {
//            String id = notificationInfo.get("_id").toString();
//            Document notification = notificationInfo.get("notification", Document.class);
//            try {
//                List<Long> thirdSchoolIdList = notification.get("thirdSchoolIdList", List.class);
//                for (Long thirdSchoolId : thirdSchoolIdList) {
//                    allSyncParamsList.add(MapUtil.of("_id", id, "thirdSchoolId", thirdSchoolId));
//                }
//                if (ObjectUtil.isValueEquals(notification.get("parseError"), true)) {
//                    int i = 1 / 0;
//                }
//                successIdList.add(id);
//            } catch (Exception e) {
//                log.warn("通知解析异常: _id: {}", id, e);
//                try {
//                    thirdNotificationService.updateStatusParseFailed(MapUtil.of(
//                            "_id", id, "cause", Collections.singletonList(ThirdTaskManager.getStackTrace(e)),
//                            "userId", 1, "userName", "admin"
//                    ));
//                } catch (Exception e1) {
//                    log.warn("更新通知解析失败状态时异常: _id: {}", id, e1);
//                }
//            }
//        }
//
//        // 分组，为了记录失败信息   thirdSchoolId -> _idList
//        Map<Long, Set<String>> thirdSchoolIdMap = allSyncParamsList.stream()
//                .collect(groupingBy(
//                        o -> (Long) Optional.ofNullable(o.get("thirdSchoolId")).orElse(0L),
//                        mapping(o -> o.get("_id").toString(), toSet())
//                ));
//
//        // 同步失败原因  [{_id, cause}, {}, ...]
//        List<Map<String, String>> causeList = new ArrayList<>();
//
//        // 通用的参数
//        Integer thirdTaskTypeNotification = DictUtil.getDictValue("thirdTaskType", "notification");
//        Map<String, Object> syncCommonParams = MapUtil.of(
//                "thirdPartyId", thirdPartyId, "thirdTaskType", thirdTaskTypeNotification,
//                "userId", 1, "userName", "admin"
//        );
//
//        // 3.1 学校同步 /third/task/sync/school
//        if (thirdSchoolIdMap.containsKey(0L)) {
//            try {
//                Map<String, Object> syncParams = new HashMap<>(syncCommonParams);
//                int i = 1 / 0;
//            } catch (Exception e) {
//                // 失败涉及的通知id
//                Set<String> syncFailedIdSet = thirdSchoolIdMap.get(0L);
//                log.warn("学校同步异常: thirdPartyId: {}, syncFailedIdSet: {}", thirdPartyId, syncFailedIdSet);
//
//                // 处理异常信息
//                String stackTrace = ThirdTaskManager.getStackTrace(e);
//                for (String id : syncFailedIdSet) {
//                    Map<String, String> cause = new HashMap<>();
//                    cause.put("_id", id);
//                    cause.put("cause", stackTrace);
//                    causeList.add(cause);
//                }
//                successIdList.removeAll(syncFailedIdSet);
//            }
//            thirdSchoolIdMap.remove(0L);
//        }
//
//        // 3.2 全量同步 /third/task/sync/school/all
//        if (!thirdSchoolIdMap.isEmpty()) {
//            String thirdSchoolIds = StringUtils.join(thirdSchoolIdMap.keySet(), ",");
//            try {
//                Map<String, Object> syncParams = new HashMap<>(syncCommonParams);
//                syncParams.put("thirdSchoolIds", thirdSchoolIds);
//                System.out.println("------------------------thirdSchoolIds: " + thirdSchoolIds);
//                int i  = 1 / 0;
//            } catch (Exception e) {
//                // 失败涉及的通知id
//                Set<String> syncFailedIdSet = new HashSet<>();
//                for (Set<String> idList : thirdSchoolIdMap.values()) {
//                    if (idList != null) {
//                        syncFailedIdSet.addAll(idList);
//                    }
//                }
//                log.warn("全量同步异常: thirdPartyId: {}, syncFailedIdSet: {}, thirdSchoolIds: {}",
//                        thirdPartyId, syncFailedIdSet, thirdSchoolIds);
//
//                // 处理异常信息
//                String stackTrace = ThirdTaskManager.getStackTrace(e);
//                for (String id : syncFailedIdSet) {
//                    Map<String, String> cause = new HashMap<>();
//                    cause.put("_id", id);
//                    cause.put("cause", stackTrace);
//                    causeList.add(cause);
//                }
//                successIdList.removeAll(syncFailedIdSet);
//            }
//        }
//
//        // 4. 更新解析成功的状态
//        if (!successIdList.isEmpty()) {
//            try {
//                thirdNotificationService.updateStatusSuccess(
//                        MapUtil.of("_idList", successIdList, "userId", 1, "userName", "admin")
//                );
//            } catch (Exception e) {
//                log.warn("更新通知成功时异常: successIdList: {}", successIdList, e);
//            }
//        }
//
//        // 5. 更新同步失败的异常信息
//        if (!causeList.isEmpty()) {
//            Map<String, List<String>> idMapCause = causeList.stream()
//                    .collect(groupingBy(o -> o.get("_id"), mapping(o -> o.get("cause"), toList())));
//            for (Map.Entry<String, List<String>> entry : idMapCause.entrySet()) {
//                try {
//                    thirdNotificationService.updateStatusSyncFailed(MapUtil.of(
//                            "_id", entry.getKey(), "cause", entry.getValue(),
//                            "userId", 1, "userName", "admin"
//                    ));
//                } catch (Exception e) {
//                    log.warn("更新通知成功时异常: successIdList: {}", successIdList, e);
//                }
//            }
//        }
//    }
}
