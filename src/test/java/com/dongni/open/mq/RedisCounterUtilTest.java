package com.dongni.open.mq;

import com.dongni.open.mq.utils.RedisCounterUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <br/>
 * @date 2020/02/25 <br/>
 *
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RedisCounterUtilTest {
    
    String key = "TEST:REDIS:COUNTER:SAPLUK";
    
    private void printCount() {
        System.out.println(" count : " + RedisCounterUtil.getCount(RedisCounterUtil.getRedisCounterKey(key)));
    }
    
    @Test
    public void test1() {
        printCount();
    }
    
    @Test
    public void test2() {
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600);
        printCount();
    
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600, 5);
        printCount();
    
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600);
        printCount();
    }
    
    @Test
    public void test3() {
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600, false);
        printCount();
        
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600, 4, false);
        printCount();
        
        RedisCounterUtil.increase(RedisCounterUtil.getRedisCounterKey(key), 3600, false);
        printCount();
    }
}
