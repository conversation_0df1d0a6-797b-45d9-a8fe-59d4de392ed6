package com.dongni.processing.tiku;

import com.dongni.tiku.third.jyeoo.service.JyeooDataSyncService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <br/>
 * @date 2020/10/15 <br/>
 * 同步菁优的书籍课程知识点信息
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class JyeooBookCategoryKnowledgeSyncTest {
    
    @Autowired
    private JyeooDataSyncService jyeooDataSyncService;
    
    @Test
    public void update() {
        jyeooDataSyncService.updateBookCategoryKnowledge();

    }
}
