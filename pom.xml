<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dongni</groupId>
    <artifactId>fat-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>fat-service</name>
    <description>Demo project for Spring Boot</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.18.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <sso.env>none</sso.env>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version> <!-- 使用最新版本 -->
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>bi-api</artifactId>
            <version>3.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>bi-resolver</artifactId>
            <version>3.8-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>hqjl-common</artifactId>
            <version>4.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
            <version>2.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.csp</groupId>
            <artifactId>sentinel-datasource-redis</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>hqjl-analytics</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>aicenter-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.dongni</groupId>
            <artifactId>common-starter</artifactId>
            <version>1.5.162-dev-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dongni</groupId>
                    <artifactId>examclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.2</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.dongni</groupId>
            <artifactId>examclient</artifactId>
            <version>1.13.9-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.dongni</groupId>
            <artifactId>exammarkserv</artifactId>
            <version>1.13.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>examclient</artifactId>
                    <groupId>com.dongni</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dongni</groupId>
            <artifactId>examitemserv</artifactId>
            <version>1.13.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>examclient</artifactId>
                    <groupId>com.dongni</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.24</version>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>
        <!--esay excel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.3.2</version>
        </dependency>
        <!-- 汉字转拼音 -->
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>
        <!-- kafka -->
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <!-- 重要！转换tif -> png需要导入的jar包 -->
        <dependency>
            <groupId>com.github.jai-imageio</groupId>
            <artifactId>jai-imageio-core</artifactId>
            <version>1.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <!-- 扫元注解专用-->
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <version>0.9.11</version>
        </dependency>

        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>5.2.4</version>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingding</artifactId>
            <version>1.1</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.8.73.ALL</version>
        </dependency>

        <!--微信Java框架-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>3.7.0</version>
        </dependency>

        <dependency>
            <groupId>com.github.jedis-lock</groupId>
            <artifactId>jedis-lock</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- Latex -->
        <!--<dependency>-->
            <!--<groupId>uk.ac.ed.ph.snuggletex</groupId>-->
            <!--<artifactId>snuggletex-core</artifactId>-->
            <!--<version>1.2.2</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>uk.ac.ed.ph.snuggletex</groupId>-->
            <!--<artifactId>snuggletex-upconversion</artifactId>-->
            <!--<version>1.2.2</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
            <!--<groupId>uk.ac.ed.ph.snuggletex</groupId>-->
            <!--<artifactId>snuggletex-jeuclid</artifactId>-->
            <!--<version>1.3.0</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.44</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-lang3</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <version>${parent.version}</version>
        </dependency>

        <dependency>
            <groupId>net.sf.jett</groupId>
            <artifactId>jett-core</artifactId>
            <version>0.11.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-jcl</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.5.6</version>
        </dependency>
        <!--中文支持-->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <!--java cas接入-->
        <dependency>
            <groupId>org.jasig.cas.client</groupId>
            <artifactId>cas-client-core</artifactId>
            <version>3.1.12</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-tools</artifactId>
            <version>3.0.0-RC1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>3.0.0-RC1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.2.0</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.skywalking/apm-toolkit-trace -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>8.2.0</version>
        </dependency>


        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.18</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.12.13</version>
        </dependency>
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>hqjl-redis</artifactId>
            <version>3.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>ESSlib</artifactId>
            <version>2.3-SNAPSHOT</version>
        </dependency>

        <!-- monitor -->
        <dependency>
            <groupId>HQJL</groupId>
            <artifactId>monitor-client</artifactId>
            <version>1.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_pushgateway</artifactId>
            <version>0.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-ocr</artifactId>
            <version>3.1.1131</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.5.0</version>
        </dependency>
        <dependency>
            <groupId>io.github.java-diff-utils</groupId>
            <artifactId>java-diff-utils</artifactId>
            <version>4.16</version>
        </dependency>

        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-jpeg</artifactId>
            <version>3.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.twelvemonkeys.imageio</groupId>
            <artifactId>imageio-tiff</artifactId>
            <version>3.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.aspose</groupId>
            <artifactId>aspose-words</artifactId>
            <version>24.12</version>
            <classifier>jdk17</classifier>
        </dependency>

        <dependency>
            <groupId>com.google.re2j</groupId>
            <artifactId>re2j</artifactId>
            <version>1.6</version>
        </dependency>

    </dependencies>

    <profiles>
        <!-- -local 结尾的有特殊含义 不会启动定时任务等 -->
        <profile>
            <id>aaaaaaaaaaaaaaaaaaaaaaaaa________________deprecated_down</id>    <!-- 准备废弃 占位符 -->
        </profile>
        <!--生产环境-->
        <profile>
            <id>product</id>
            <properties>
                <maven.profile>product</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>localisation-jimei</id>
            <properties>
                <maven.profile>product-jimei</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>lishu</id>
            <properties>
                <maven.profile>product-lishu</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>localisation-putian</id>
            <properties>
                <maven.profile>product-putian</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>localisation-siming</id>
            <properties>
                <maven.profile>product-siming</maven.profile>
            </properties>
        </profile>
        <!-- 同安教育局 -->
        <profile>
            <id>localisation-tongan</id>
            <properties>
                <maven.profile>product-tongan</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>localisation-huaibei</id>
            <properties>
                <maven.profile>product-huaibei</maven.profile>
            </properties>
        </profile>

        <profile>
            <id>product-aaaaaaaaaaaaaaaaa________________deprecated_up</id>    <!-- 准备废弃 占位符 -->
        </profile>

        <!-- 测试环境 -->
        <profile>
            <id>dongni-test</id>
            <properties>
                <maven.profile>dongni-test</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>dongni-test2</id>
            <properties>
                <maven.profile>dongni-test2</maven.profile>
                <wechat.env>dongni-test</wechat.env>
                <alipay.env>dongni-test</alipay.env>
                <sensitive.env>dongni-test</sensitive.env>
            </properties>
        </profile>
        <profile>
            <id>dongni-test4</id>
            <properties>
                <maven.profile>dongni-test4</maven.profile>
                <wechat.env>dongni-test</wechat.env>
                <alipay.env>dongni-test</alipay.env>
                <sensitive.env>dongni-test</sensitive.env>
            </properties>
        </profile>
         <profile>
            <id>dongni-test5</id>
            <properties>
                <maven.profile>dongni-test5</maven.profile>
                <wechat.env>dongni-test</wechat.env>
                <alipay.env>dongni-test</alipay.env>
                <sensitive.env>dongni-test</sensitive.env>
            </properties>
        </profile>
        <profile>
            <id>dongni-test2-local</id>
            <properties>
                <maven.profile>dongni-test2-local</maven.profile>
            </properties>
        </profile>

        <!-- 测试环境 - 本地调试 -->
        <profile>
            <id>dongni-test-local</id>
            <properties>
                <maven.profile>dongni-test-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-aliyun2</id>
            <properties>
                <maven.profile>product-aliyun2</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-aliyun2-local</id>
            <properties>
                <maven.profile>product-aliyun2-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>dongni-test3-local</id>
            <properties>
                <maven.profile>dongni-test3-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>dongni-test4-local</id>
            <properties>
                <maven.profile>dongni-test4-local</maven.profile>
            </properties>
        </profile>

        <profile>
            <id>dongni-test5-local</id>
            <properties>
                <maven.profile>dongni-test5-local</maven.profile>
            </properties>
        </profile>
        <!-- product -->
        <!-- -local 结尾的有特殊含义 不会启动定时任务等 -->

        <!-- 生产环境 -->
        <profile>
            <id>product-aliyun</id>
            <properties>
                <maven.profile>product-aliyun</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-aliyun-local</id>
            <properties>
                <maven.profile>product-aliyun-local</maven.profile>
            </properties>
        </profile>

        <!-- 西边东关 -->
        <profile>
            <id>product-dongguan</id>
            <properties>
                <maven.profile>product-dongguan</maven.profile>
            </properties>
        </profile>

        <!-- 海口 -->
        <profile>
            <id>product-haikou</id>
            <properties>
                <maven.profile>product-haikou</maven.profile>
            </properties>
        </profile>

         <profile>
            <id>product-haikou-local</id>
            <properties>
                <maven.profile>product-haikou-local</maven.profile>
            </properties>
        </profile>
     <!-- 渭南 -->
        <profile>
            <id>product-weinan</id>
            <properties>
                <maven.profile>product-weinan</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-weinan-local</id>
            <properties>
                <maven.profile>product-weinan-local</maven.profile>
            </properties>
        </profile>
        <!-- 渭南 -->
        <!-- 陆丰 -->
        <profile>
            <id>product-lufeng</id>
            <properties>
                <maven.profile>product-lufeng</maven.profile>
            </properties>
        </profile>
        <!-- 陆丰 -->
        <profile>
            <id>product-ningxia</id>
            <properties>
                <maven.profile>product-ningxia</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-minda</id>
            <properties>
                <maven.profile>product-minda</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-jiaozuo</id>
            <properties>
                <maven.profile>product-jiaozuo</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-jiaozuo-local</id>
            <properties>
                <maven.profile>product-jiaozuo-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-dancheng</id>
            <properties>
                <maven.profile>product-dancheng</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-xiangshan</id>
            <properties>
                <maven.profile>product-xiangshan</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-jinzhong</id>
            <properties>
                <maven.profile>product-jinzhong</maven.profile>
            </properties>
        </profile>
        <!-- 双流 -->
        <profile>
            <id>product-shuangliu</id>
            <properties>
                <maven.profile>product-shuangliu</maven.profile>
            </properties>
        </profile>
        <!-- 鼓楼 -->
        <profile>
            <id>product-gulou</id>
            <properties>
                <maven.profile>product-gulou</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-wutongqiao</id>
            <properties>
                <maven.profile>product-wutongqiao</maven.profile>
            </properties>
        </profile>
         <profile>
            <id>dongni-init</id>
            <properties>
                <maven.profile>dongni-init</maven.profile>
            </properties>
        </profile>


        <!-- 淮北 -->
        <profile>
            <id>product-huaibei</id>
            <properties>
                <maven.profile>product-huaibei</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-huaibei-local</id>
            <properties>
                <maven.profile>product-huaibei-local</maven.profile>
            </properties>
        </profile>

        <!-- 集美 -->
        <profile>
            <id>product-jimei</id>
            <properties>
                <maven.profile>product-jimei</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-jimei-local</id>
            <properties>
                <maven.profile>product-jimei-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>standard-local</id>
            <properties>
                <maven.profile>standard-local</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>standard</id>
            <properties>
                <maven.profile>standard</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>standard-oss</id>
            <properties>
                <maven.profile>standard-oss</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>standard-oss-local</id>
            <properties>
                <maven.profile>standard-oss-local</maven.profile>
            </properties>
        </profile>
        <!-- 辽宁 -->
        <profile>
            <id>product-liaoning</id>
            <properties>
                <maven.profile>product-liaoning</maven.profile>
            </properties>
        </profile>

        <!-- 立数 -->
        <profile>
            <id>product-lishu</id>
            <properties>
                <maven.profile>product-lishu</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-lishu-local</id>
            <properties>
                <maven.profile>product-lishu-local</maven.profile>
            </properties>
        </profile>

        <!-- 普天 东台 -->
        <profile>
            <id>product-putian</id>
            <properties>
                <maven.profile>product-putian</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-putian-local</id>
            <properties>
                <maven.profile>product-putian-local</maven.profile>
            </properties>
        </profile>

        <!-- 思明 -->
        <profile>
            <id>product-siming</id>
            <properties>
                <maven.profile>product-siming</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-siming-local</id>
            <properties>
                <maven.profile>product-siming-local</maven.profile>
            </properties>
        </profile>

        <!-- 腾讯云 -->
        <profile>
            <id>product-tencent</id>
            <properties>
                <maven.profile>product-tencent</maven.profile>
            </properties>
        </profile>

        <!-- 同安 -->
        <profile>
            <id>product-tongan</id>
            <properties>
                <maven.profile>product-tongan</maven.profile>
            </properties>
        </profile>
        <profile>
            <id>product-tongan-local</id>
            <properties>
                <maven.profile>product-tongan-local</maven.profile>
            </properties>
        </profile>

        <profile>
            <id>dongni-k8s-local</id>
            <properties>
                <maven.profile>dongni-k8s-local</maven.profile>
            </properties>
        </profile>


        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>HQJL</groupId>
                    <artifactId>dnr_3rd_win</artifactId>
                    <version>1.0-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>HQJL</groupId>
                    <artifactId>dnRecognitionLib</artifactId>
                    <version>preonline320-win-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>linux</id>
            <activation>
                <os>
                    <family>unix</family>
                </os>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>HQJL</groupId>
                    <artifactId>dnr_3rd_linux_lib</artifactId>
                    <version>1.0-vipsFixDamagedPicTYL-SNAPSHOT</version>
                </dependency>
                <dependency>
                    <groupId>HQJL</groupId>
                    <artifactId>dnRecognitionLib</artifactId>
                    <version>master-linux-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>

    </profiles>

    <build>
        <finalName>${artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.4</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                    <prefix>git</prefix>
                    <verbose>false</verbose>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/static/${artifactId}/version.txt</generateGitPropertiesFilename>
                    <format>txt</format>
                    <gitDescribe>
                        <skip>false</skip>
                        <always>true</always>
                        <dirty>-dirty</dirty>
                    </gitDescribe>
                </configuration>
            </plugin>
        </plugins>
        <!-- 指定打包内容 -->
        <resources>
            <!-- 打包时需要替换占位符的文件列表 springboot将占位符由原本的 ${xxx} 替换为 @xxx@ 了-->
            <resource>
                <filtering>true</filtering>
                <directory>${basedir}/src/main/resources</directory>
                <includes>
                    <include>**/application.yml</include>
                    <include>**/application.yaml</include>
                    <include>**/application.properties</include>
                    <include>**/application-${maven.profile}.yml</include>
                    <include>**/application-${maven.profile}.yaml</include>
                    <include>**/application-${maven.profile}.properties</include>
                    <include>configs/**/*.properties</include>
                    <include>mybatis/**/*.xml</include>
                </includes>
            </resource>
            <!-- 打包时排除以下文件，但不包括includes列表中的文件 -->
            <resource>
                <directory>${basedir}/src/main/resources</directory>
                <excludes>
                    <exclude>**/application*.yml</exclude>
                    <exclude>**/application*.yaml</exclude>
                    <exclude>**/application*.properties</exclude>
                    <exclude>configs-filter/**/*.*</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>dongni</id>
            <url>https://dalao.dongni100.com/nexus/content/groups/dongni/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
