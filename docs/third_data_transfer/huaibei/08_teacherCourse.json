{
  "请求时间": "2021-02-25 17-00-00",
  "接口": "http://live.lead-reading.cn/oData/swagger/index.html#!/ODataService47Zxjz0702s/pageList",
  "参数(来自第三方提供的文档)": {
    "teaId": "String 教师号",
    "rkkchId": "long 任课课程号"
  },
  "请求结果示例(返回结果List示例仅取一个展示)": {
    "请求": {
    },
    "返回": {
      "isnow": null,                                // 仅取1
      "id": 13878,
      "teaId": "306fa4f214ef483580c41fbfc7602a36",  // 老师id
      "skbj": 1263,                                 // 授课班级,
      "sknj": 256,                                  // 授课年级
      "rkkchId": 4428,                              // 任课课程id
      "bzr": "0",                                   // 是不是班主任 1班主任

      "sysid": "79226d36-83f5-4ac8-81c0-e5f5f9dc6df7",
      "rkqsny": null,
      "rkzzny": null,
      "rkzxs": null,
      "rkxdm": null,
      "rkjsm": null,
      "skrs": null,
      "bzrdj": null,
      "syncId": null
    }
  }
}
