{
  "请求时间": "2021-02-25 17-00-00",
  "接口": "http://live.lead-reading.cn/oData/swagger/index.html#!/ODataService47Zxjx0100s/pageList",
  "参数(来自第三方提供的文档)": {
    "xqhId": "integer 校区编码",
    "kcmc": "String 课程名称",
    "kch": "String 课程号",
    "kcbm": "String 课程别名"
  },
  "请求结果示例(返回结果List示例仅取一个展示)": {
    "请求": {
      "xqhId": 11675
    },
    "返回": {
      "id": 4605,             // 课程id
      "kcmc": "数学",         // 课程名称
      "kch": "cg002",        // 课程号
      "xqhid": 0,            // 校区编号 如果为0表示公用

      "sysid": "79226d36-83f5-4ac8-81c0-e5f5f9dc6df7",
      "defaultxf": 0.0,
      "pid": 0,
      "syncId": "2",

      "kcm": null,
      "kcdjm": null,
      "kcbm": null,
      "zxs": null,
      "zhxs": null,
      "zxxs": null,
      "skfsm": null,
      "jcbm": null,
      "subjecttype": null,
      "kcmcbm": null,
      "kcjj": null,
      "kcyq": null,
      "cksm": null,
      "schId": null
    }
  }
}
