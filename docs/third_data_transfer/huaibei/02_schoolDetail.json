{
  "请求时间": "2021-02-25 17-00-00",
  "接口": "http://live.lead-reading.cn/oData/swagger/index.html#!/ODataService47Jcxx0102s/pageList",
  "参数(来自第三方提供的文档)": {
    "schId": "long 学校号",
    "xqmc": "String 校区名称",
    "xqh": "String 校区号"
  },
  "请求结果示例(返回结果List示例仅取一个展示)": {
    "请求": {
      "schid": 1549
    },
    "返回": {
      "level": 2,       // 仅取2的数据
      "id": 11675,      // ==xqhId 校区编码
      "xqmc": "本部",   // 校区名称
      "xqdz": null,    // 校区地址
      "xqlxdh": null,  // 校区联系电话

      "schId": 1549,
      "sysid": "79226d36-83f5-4ac8-81c0-e5f5f9dc6df7",
      "xqh": "本部",
      "idParent": 11674,

      "xqyzbm": null,
      "xqczdh": null,
      "xqfzrh": null,
      "defaultadmin": null,
      "syncId": null
    }
  }
}
