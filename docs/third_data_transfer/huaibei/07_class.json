{
  "请求时间": "2021-02-25 17-00-00",
  "接口": "http://live.lead-reading.cn/oData/swagger/index.html#!/ODataService47Zxxx0300s/pageList",
  "参数(来自第三方提供的文档)": {
    "xqhId": "long 校区编码",
    "classcode": "String 班级码",
    "graId": "long 年级编码",
    "bh": "Integer 班号",
    "bj": "String 班级"
  },
  "请求结果示例(返回结果List示例仅取一个展示)": {
    "请求": {
      "xqhId": 11675
    },
    "返回": {
      "isnow": "1",                // 是否是当前学期 仅取1
      "id": 10500,                 // 班级id
      "xqhId": 11675,              // 校区编号
      "graId": 3457,               // 年级编号
      "bj": "2019#2019-（2）班",    // 班级名称 可能为空 如果为空 使用下面那个
      "bh": 2,                     // 班号 需要拼接"班"

                                   // 班级需要班主任 通过teacherCourse获取

      "sysid": "79226d36-83f5-4ac8-81c0-e5f5f9dc6df7",
      "bjlxm": "0",
      "byrq": "0",
      "classcode": "70220477900",
      "semesterId": 1150,

      "graUpgradeName": null,
      "jbny": null,
      "bzrgh": null,
      "fbzrgh": null,
      "jxbzrgh": null,
      "bzxh": null,
      "bjrych": null,
      "xz": null,
      "wllx": null,
      "sfssmzsyjxb": null,
      "syjxmsm": null,
      "edrs": null,
      "classroomid": null,
      "jcxx0102": null,
      "zxxx0200": null,
      "syncId": null
    }
  }
}
