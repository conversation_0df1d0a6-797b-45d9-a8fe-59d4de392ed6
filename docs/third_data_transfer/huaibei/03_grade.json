{
  "请求时间": "2021-02-25 17-00-00",
  "接口": "http://live.lead-reading.cn/oData/swagger/index.html#!/ODataService47Zxxx0200s/pageList",
  "参数(来自第三方提供的文档)": {
    "xqhId": "long 校区编码",
    "semesterId": "integer 学期编码",
    "gradename": "String 年级名称",
    "isNow": "String 是否是当前学期，1是，0否"
  },
  "请求结果示例(返回结果List示例仅取一个展示)": {
    "请求": {
      "xqhId": 11675,
      "isNow": 1
    },
    "返回": {
      "id": 3456,           // 年级id
      "isnow": "1",         // 仅取1
      "nj": "J1",           // 年级 P1~P6 小学  J1~J3 初中  H1~H3 高中
      "xqhId": 11675,       // 校区编号
      "gradename": "七年级", // 年级名称
      "stuJb": null,        // 级别 入学年份

      "sysid": "79226d36-83f5-4ac8-81c0-e5f5f9dc6df7",
      "semesterId": 1150,
      "term": null,
      "syncId": null
    }
  }
}
