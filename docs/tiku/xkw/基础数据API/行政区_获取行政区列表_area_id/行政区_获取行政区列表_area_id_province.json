{"平台说明文档": "https://open.xkw.com/基础数据API/行政区/getAllAreasUsingGET", "获取课程列表": "GET /xopqbm/areas/all", "数据获取时间": "20220722", "一些备注信息": ["程序过滤 只到省级的信息"], "只是个分隔符": "____________________________________________________________", "data": [{"id": "1", "name": "全国", "code": "1", "level": "PROVINCE", "ordinal": 1, "createTime": "2019-01-18T11:32:34", "parentId": "0", "shortName": "全国"}, {"id": "110000", "name": "北京市", "code": "110000", "level": "PROVINCE", "ordinal": 11, "createTime": "2019-01-21T16:47:27", "parentId": "0", "shortName": "北京"}, {"id": "120000", "name": "天津市", "code": "120000", "level": "PROVINCE", "ordinal": 12, "createTime": "2019-01-21T16:47:27", "parentId": "0", "shortName": "天津"}, {"id": "130000", "name": "河北省", "code": "130000", "level": "PROVINCE", "ordinal": 13, "createTime": "2019-01-21T16:47:27", "parentId": "0", "shortName": "河北"}, {"id": "140000", "name": "山西省", "code": "140000", "level": "PROVINCE", "ordinal": 14, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "山西"}, {"id": "150000", "name": "内蒙古自治区", "code": "150000", "level": "PROVINCE", "ordinal": 15, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "内蒙古"}, {"id": "210000", "name": "辽宁省", "code": "210000", "level": "PROVINCE", "ordinal": 21, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "辽宁"}, {"id": "220000", "name": "吉林省", "code": "220000", "level": "PROVINCE", "ordinal": 22, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "吉林"}, {"id": "230000", "name": "黑龙江省", "code": "230000", "level": "PROVINCE", "ordinal": 23, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "黑龙江"}, {"id": "310000", "name": "上海市", "code": "310000", "level": "PROVINCE", "ordinal": 31, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "上海"}, {"id": "320000", "name": "江苏省", "code": "320000", "level": "PROVINCE", "ordinal": 32, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "江苏"}, {"id": "330000", "name": "浙江省", "code": "330000", "level": "PROVINCE", "ordinal": 33, "createTime": "2019-01-21T16:47:28", "parentId": "0", "shortName": "浙江"}, {"id": "340000", "name": "安徽省", "code": "340000", "level": "PROVINCE", "ordinal": 34, "createTime": "2019-01-21T16:47:29", "parentId": "0", "shortName": "安徽"}, {"id": "350000", "name": "福建省", "code": "350000", "level": "PROVINCE", "ordinal": 35, "createTime": "2019-01-21T16:47:29", "parentId": "0", "shortName": "福建"}, {"id": "360000", "name": "江西省", "code": "360000", "level": "PROVINCE", "ordinal": 36, "createTime": "2019-01-21T16:47:29", "parentId": "0", "shortName": "江西"}, {"id": "370000", "name": "山东省", "code": "370000", "level": "PROVINCE", "ordinal": 37, "createTime": "2019-01-21T16:47:29", "parentId": "0", "shortName": "山东"}, {"id": "410000", "name": "河南省", "code": "410000", "level": "PROVINCE", "ordinal": 41, "createTime": "2019-01-21T16:47:30", "parentId": "0", "shortName": "河南"}, {"id": "420000", "name": "湖北省", "code": "420000", "level": "PROVINCE", "ordinal": 42, "createTime": "2019-01-21T16:47:30", "parentId": "0", "shortName": "湖北"}, {"id": "430000", "name": "湖南省", "code": "430000", "level": "PROVINCE", "ordinal": 43, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "湖南"}, {"id": "440000", "name": "广东省", "code": "440000", "level": "PROVINCE", "ordinal": 44, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "广东"}, {"id": "450000", "name": "广西壮族自治区", "code": "450000", "level": "PROVINCE", "ordinal": 45, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "广西"}, {"id": "460000", "name": "海南省", "code": "460000", "level": "PROVINCE", "ordinal": 46, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "海南"}, {"id": "500000", "name": "重庆市", "code": "500000", "level": "PROVINCE", "ordinal": 50, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "重庆"}, {"id": "510000", "name": "四川省", "code": "510000", "level": "PROVINCE", "ordinal": 51, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "四川"}, {"id": "520000", "name": "贵州省", "code": "520000", "level": "PROVINCE", "ordinal": 52, "createTime": "2019-01-21T16:47:31", "parentId": "0", "shortName": "贵州"}, {"id": "530000", "name": "云南省", "code": "530000", "level": "PROVINCE", "ordinal": 53, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "云南"}, {"id": "540000", "name": "西藏自治区", "code": "540000", "level": "PROVINCE", "ordinal": 54, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "西藏"}, {"id": "610000", "name": "陕西省", "code": "610000", "level": "PROVINCE", "ordinal": 61, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "陕西"}, {"id": "620000", "name": "甘肃省", "code": "620000", "level": "PROVINCE", "ordinal": 62, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "甘肃"}, {"id": "630000", "name": "青海省", "code": "630000", "level": "PROVINCE", "ordinal": 63, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "青海"}, {"id": "640000", "name": "宁夏回族自治区", "code": "640000", "level": "PROVINCE", "ordinal": 64, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "宁夏"}, {"id": "650000", "name": "新疆维吾尔自治区", "code": "650000", "level": "PROVINCE", "ordinal": 65, "createTime": "2019-01-21T16:47:32", "parentId": "0", "shortName": "新疆"}, {"id": "710000", "name": "台湾", "code": "710000", "level": "PROVINCE", "ordinal": 34, "createTime": "2019-01-18T11:32:34", "parentId": "0", "shortName": "台湾"}, {"id": "810000", "name": "香港特别行政区", "code": "810000", "level": "PROVINCE", "ordinal": 32, "createTime": "2019-01-18T11:32:34", "parentId": "0", "shortName": "香港"}, {"id": "820000", "name": "澳门特别行政区", "code": "820000", "level": "PROVINCE", "ordinal": 33, "createTime": "2019-01-18T11:32:34", "parentId": "0", "shortName": "澳门"}]}