<mxfile host="drawio-plugin" modified="2023-04-17T03:53:28.375Z" agent="5.0 (Android 6.0)" etag="zQpWghHUorLCAEcBryF5" version="20.5.3" type="embed"><diagram id="dsabfPXMKpS1W9JP9M7z" name="第 1 页"><mxGraphModel dx="1158" dy="698" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0"><root><mxCell id="0"/><mxCell id="1" parent="0"/><mxCell id="2" value="JyeooHandlerService#updateCourse&lt;br&gt;&lt;br&gt;base_data.t_course 基础数据课程表，里面包含了菁优网的课程数据&lt;br&gt;&lt;br&gt;course_name&amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程名称 course_cn_name截掉小学/初中/高中&lt;br&gt;course_code&amp;nbsp; &amp;nbsp; &amp;nbsp;接口返回 菁优的courseId 后续有些接口请求要用到&lt;br&gt;course_en_name&amp;nbsp; 接口返回&lt;br&gt;course_cn_name&amp;nbsp; 接口返回&lt;br&gt;&lt;br&gt;获取所有懂你的内置课程&lt;br&gt;对于course_name == course_cn_name去掉小学/初中/高中的&lt;br&gt;如果course_code/course_cn_name/course_en_name改变了，&lt;br&gt;&amp;nbsp; &amp;nbsp; 则更新这三个字段&lt;br&gt;对于匹配不上的，将那三个字段设为null" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="40" width="560" height="280" as="geometry"/></mxCell><mxCell id="7" value="course_cn_name&lt;br&gt;小学 stage=1&lt;br&gt;初中 stage=2&lt;br&gt;高中 stage=3&lt;br&gt;&lt;br&gt;course_name&lt;br&gt;为course_cn_name&lt;br&gt;去掉小学" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=16;fontFamily=Courier New;" parent="1" source="4" target="2" edge="1"><mxGeometry relative="1" as="geometry"><mxPoint x="680" y="150" as="targetPoint"/></mxGeometry></mxCell><mxCell id="4" value="API 三 获取学科&lt;br&gt;GET http://api.jyeoo.com/v1/subject&lt;br&gt;获取系统当前支持的学科&lt;br&gt;参数: 无&lt;br&gt;返回: [[String],[],...]&lt;br&gt;&lt;pre style=&quot;background-color: rgb(255 , 255 , 255) ; font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;pre style=&quot;font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  [&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    &lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;10&quot;&lt;/span&gt;,       // course_code&lt;br&gt;    &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;math3&quot;&lt;/span&gt;,    // course_en_name&lt;br&gt;    &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;nsimsun&amp;#34;&quot;&gt;小学数学&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;, // course_cn_name&lt;br&gt;    &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;40&quot;&lt;/span&gt;        // 不详&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;  &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;]&lt;/span&gt;&lt;/pre&gt;&lt;/pre&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="880" y="40" width="520" height="280" as="geometry"/></mxCell><mxCell id="8" value="tiku.t_jyeoo_question_type&lt;br&gt;菁优题型&lt;br&gt;jyeoo_question_type_id&amp;nbsp; &amp;nbsp; t_question_type_relation&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; .relative_id&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; belong_type = 2菁优&lt;br&gt;[UK]jyeoo_question_type&amp;nbsp; &amp;nbsp;int Key标识&lt;br&gt;jyeoo_question_type_name&amp;nbsp; String Value名称&lt;br&gt;[UK]course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程id&lt;br&gt;course_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程名称&lt;br&gt;&lt;br&gt;新增&lt;br&gt;更新jyeoo_question_type_name/course_name&lt;br&gt;删除" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="360" width="560" height="280" as="geometry"/></mxCell><mxCell id="10" value="可能会出现新的题型" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;fontFamily=Courier New;fontSize=16;" parent="1" source="9" target="8" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="9" value="API 七 获取试题题型&lt;br&gt;GET http://api.jyeoo.com/v1/{subject}/common?tp=1&lt;br&gt;获取所选学科试题题型&lt;br&gt;参数: subject 即 course_en_name&lt;br&gt;返回: [{},{},...]&lt;br&gt;&lt;pre style=&quot;background-color: rgb(255 , 255 , 255) ; font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;pre style=&quot;font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    &lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;Key&quot;:&lt;/span&gt; 1,         // 标识&lt;br&gt;    &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;Value&quot;:&lt;/span&gt; &quot;填空题&quot;  // 名称&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;  &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;}&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;]&lt;/span&gt;&lt;/pre&gt;&lt;/pre&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="880" y="360" width="520" height="280" as="geometry"/></mxCell><mxCell id="12" value="获取所有tiku.t_question_type&lt;br&gt;匹配中文名称&lt;br&gt;返回的Value如果在t_question_type.question_type_name不存在，则为新增的题型&lt;br&gt;&lt;br&gt;对于新增的 question_type = max(question_type)++&lt;br&gt;&lt;br&gt;tiku.t_question_type 只有新增&lt;br&gt;&amp;nbsp; &amp;nbsp; question_type&lt;br&gt;&amp;nbsp; &amp;nbsp; question_type_name=Value=jyeoo_quetsion_type_name&lt;br&gt;&amp;nbsp; &amp;nbsp; read_type = dict.readType.subjective&lt;br&gt;&amp;nbsp; &amp;nbsp; part_type = dict.paperPartType.subjective&lt;br&gt;&amp;nbsp; &amp;nbsp; creation_type = 0 系统题型&lt;br&gt;&lt;br&gt;tiku.t_question_unit_relation 只有新增&lt;br&gt;&amp;nbsp; &amp;nbsp; question_type&amp;nbsp;&lt;br&gt;&amp;nbsp; &amp;nbsp; question_type_name=Value=jyeoo_quetsion_type_name&lt;br&gt;&amp;nbsp; &amp;nbsp; unit_type = 5 不详&lt;br&gt;&amp;nbsp; &amp;nbsp; default = 1&amp;nbsp; &amp;nbsp;不详&lt;br&gt;&lt;br&gt;tiku.t_question_type_relation&lt;br&gt;新增 belong_type&amp;nbsp;= 2 &lt;br&gt;&amp;nbsp; &amp;nbsp;t_jyeoo_question_type.jyeoo_question_type_id 不存在的&lt;br&gt;更新 belong_type = 2 and jyeoo_question_type_id&lt;br&gt;&amp;nbsp; &amp;nbsp;更新 question_type/question_type_name&lt;br&gt;删除 清理垃圾数据&lt;br&gt;&amp;nbsp;&amp;nbsp;&lt;span&gt;DELETE tqtr&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;FROM t_question_type_relation tqtr&lt;/span&gt;&lt;span&gt;&lt;br&gt;&lt;/span&gt;&lt;div&gt;&amp;nbsp; LEFT JOIN t_jyeoo_question_type tjqt&amp;nbsp;&lt;/div&gt;&lt;div&gt;&amp;nbsp; &amp;nbsp; ON tqtr.relative_id = tjqt.jyeoo_question_type_id&lt;/div&gt;&lt;div&gt;&amp;nbsp; WHERE belong_type = 2&lt;/div&gt;&lt;div&gt;&amp;nbsp; AND tjqt.jyeoo_question_type_id IS NULL&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="80" y="660" width="550" height="710" as="geometry"/></mxCell><mxCell id="26" value="" style="group;fillColor=#d5e8d4;strokeColor=#82b366;" parent="1" vertex="1" connectable="0"><mxGeometry x="630" y="660" width="1180" height="710" as="geometry"/></mxCell><mxCell id="25" value="" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;" parent="26" vertex="1"><mxGeometry width="1180" height="710" as="geometry"/></mxCell><mxCell id="11" value="tiku.t_question_type&lt;br&gt;校本题型表&lt;br&gt;&lt;br&gt;question_type_id&amp;nbsp; &amp;nbsp; 题型id&amp;nbsp;&amp;nbsp;&lt;br&gt;question_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;题型枚举&lt;br&gt;question_type_name&amp;nbsp; 题型名称&lt;br&gt;read_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;阅卷类型 1客观 2主观[默]&lt;br&gt;part_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;所属卷 2[默]&lt;br&gt;creation_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;类型 0系统[默] 1自定义" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#d5e8d4;strokeColor=#82b366;" parent="26" vertex="1"><mxGeometry x="10" y="10" width="400" height="200" as="geometry"/></mxCell><mxCell id="13" value="tiku.t_question_unit_relation&lt;br&gt;题型所属单元表&lt;br&gt;&lt;br&gt;question_unit_relation_id 所属单元表id&amp;nbsp;&amp;nbsp;&lt;br&gt;question_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;题型枚举&lt;br&gt;question_type_name&amp;nbsp; 题型名称&lt;br&gt;unit_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;单元题型 5[默]&lt;br&gt;default&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;是否为默认妹纸&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 0非默认 1默认[默]" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#d5e8d4;strokeColor=#82b366;" parent="26" vertex="1"><mxGeometry x="10" y="290" width="400" height="200" as="geometry"/></mxCell><mxCell id="14" value="question_type" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontFamily=Courier New;fontSize=16;endArrow=none;endFill=0;" parent="26" source="11" target="13" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="15" value="tiku.t_question_type_relation&lt;br&gt;题型映射表(第三方)&lt;br&gt;question_type_relation_id 所属单元表id&amp;nbsp;&amp;nbsp;&lt;br&gt;question_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;题型枚举&lt;br&gt;question_type_name&amp;nbsp; 题型名称&lt;br&gt;belong_type&amp;nbsp; 所属 &lt;strike&gt;0懂你&lt;/strike&gt; 1一起 2菁优&lt;br&gt;relative_id&amp;nbsp; 关联id&amp;nbsp;&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;belong_type=1 tiku.t_yiqi_question_type.yiqi_question_type_id&lt;br&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;belong_type=2 tiku.t_jyeoo_question_type.jyeoo_question_type_id" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#d5e8d4;strokeColor=#82b366;" parent="26" vertex="1"><mxGeometry x="450" y="290" width="720" height="200" as="geometry"/></mxCell><mxCell id="21" value="分支-第三方&lt;br&gt;question_type" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;endArrow=none;endFill=0;fontFamily=Courier New;fontSize=16;entryX=0.25;entryY=0;entryDx=0;entryDy=0;" parent="26" source="11" target="15" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="16" value="tiku.t_jyeoo_question_type&lt;br&gt;菁优题型&lt;br&gt;jyeoo_question_type_id&amp;nbsp; &amp;nbsp; id&lt;br&gt;[UK]jyeoo_question_type&amp;nbsp; &amp;nbsp;Key标识&lt;br&gt;jyeoo_question_type_name&amp;nbsp; Value名称&lt;br&gt;[UK]course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程id&lt;br&gt;course_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程名称" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#d5e8d4;strokeColor=#82b366;" parent="26" vertex="1"><mxGeometry x="450" y="530" width="400" height="170" as="geometry"/></mxCell><mxCell id="19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.25;exitY=1;exitDx=0;exitDy=0;endArrow=none;endFill=0;fontFamily=Courier New;fontSize=16;" parent="26" source="15" target="16" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="17" value="tiku.t_yiqi_question_type&lt;br&gt;一起题型&lt;br&gt;yiqi_question_type_id&amp;nbsp; &amp;nbsp; id&lt;br&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="26" vertex="1"><mxGeometry x="890" y="530" width="280" height="170" as="geometry"/></mxCell><mxCell id="20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;endArrow=none;endFill=0;fontFamily=Courier New;fontSize=16;" parent="26" source="15" target="17" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="23" value="tiku.t_question_type_course&lt;br&gt;题型课程关系表&lt;br&gt;&lt;br&gt;question_type_course_id&amp;nbsp; id&lt;br&gt;question_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 题型枚举&lt;br&gt;question_type_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;题型名称&lt;br&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 课程id&lt;br&gt;course_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 课程名称" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;fillColor=#d5e8d4;strokeColor=#82b366;" parent="26" vertex="1"><mxGeometry x="770" y="10" width="400" height="160" as="geometry"/></mxCell><mxCell id="24" value="分支-懂你&lt;br&gt;question_type" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;endFill=0;fontFamily=Courier New;fontSize=16;" parent="26" source="11" target="23" edge="1"><mxGeometry relative="1" as="geometry"/></mxCell><mxCell id="27" value="tiku.t_jyeoo_book&lt;br&gt;菁优教材表&lt;br&gt;&lt;br&gt;jyeoo_book_id 菁优教材表id&lt;br&gt;[UK]book_code 菁优bookId&amp;nbsp; UUID&lt;br&gt;book_name&amp;nbsp; &amp;nbsp; &amp;nbsp;教材名称&amp;nbsp; &amp;nbsp; &amp;nbsp;三年级上&lt;br&gt;book_desc&amp;nbsp; &amp;nbsp; &amp;nbsp;教材描述&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;br&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp;懂你课程id&amp;nbsp; &amp;nbsp;23&lt;br&gt;course_code&amp;nbsp; &amp;nbsp;菁优课程id&amp;nbsp; &amp;nbsp;10&lt;br&gt;grade_type&amp;nbsp; &amp;nbsp; 年级1-12&amp;nbsp; &amp;nbsp; 3&lt;br&gt;term_type&amp;nbsp; &amp;nbsp; &amp;nbsp;上下学期1-2&amp;nbsp; 1&lt;br&gt;edition_type&amp;nbsp; 教材版本&amp;nbsp; &amp;nbsp; &amp;nbsp;1&lt;br&gt;grade_name&amp;nbsp; &amp;nbsp; 年级中文&amp;nbsp; &amp;nbsp; &amp;nbsp;三年级&lt;br&gt;term_name&amp;nbsp; &amp;nbsp; &amp;nbsp;上下学期中文 上学期&lt;br&gt;edition_name&amp;nbsp; 教材版本&amp;nbsp; &amp;nbsp; 人教版&lt;br&gt;book_cover&amp;nbsp; &amp;nbsp; 教材封面&amp;nbsp; &amp;nbsp; http://img.jyeoo.cn/books/..png&lt;br&gt;book_type&amp;nbsp; &amp;nbsp; &amp;nbsp;未知&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;br&gt;book_status&amp;nbsp; &amp;nbsp;未知" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="1640" width="560" height="360" as="geometry"/></mxCell><mxCell id="28" value="tiku.t_jyeoo_category&lt;br&gt;菁优章节表&lt;br&gt;&lt;br&gt;jyeoo_category_id&amp;nbsp; 菁优章节id&lt;br&gt;[UK]category_code&amp;nbsp; 菁优categoryId UUID&lt;br&gt;jyeoo_book_id&amp;nbsp; &amp;nbsp; &amp;nbsp; 教材表外键 t_jyeoo_book.jyeoo_book_id&lt;br&gt;book_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 菁优bookId&amp;nbsp; &amp;nbsp; &amp;nbsp;UUID&lt;br&gt;category_name&amp;nbsp; &amp;nbsp; &amp;nbsp; 章节名称&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 1 测量&lt;br&gt;category_desc&amp;nbsp; &amp;nbsp; &amp;nbsp; 章节描述&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;br&gt;category_seq&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;章节排序号&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;1&lt;br&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 懂你课程id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;23&lt;br&gt;course_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 菁优课程id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;10&lt;br&gt;node_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 节点id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;br&gt;parent_node_id&amp;nbsp; &amp;nbsp; &amp;nbsp;父节点id&lt;br&gt;node_sort&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 节点排序&lt;br&gt;node_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 节点类型 0根节点 1枝节点 2叶节点&lt;br&gt;node_level&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;章节点目录结构层级&lt;br&gt;dot_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;未知&lt;br&gt;category_pk&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 未知" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="2040" width="560" height="400" as="geometry"/></mxCell><mxCell id="29" value="tiku.t_jyeoo_knowledge&lt;br&gt;菁优知识点表&lt;br&gt;&lt;br&gt;jyeoo_knowledge_id&amp;nbsp; &amp;nbsp;菁优知识点表id&lt;br&gt;knowledge_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;知识点名称&lt;br&gt;knowledge_level&amp;nbsp; &amp;nbsp; &amp;nbsp; 知识点级别&lt;br&gt;knowledge_describle&amp;nbsp; 知识点描述&lt;br&gt;knowledge_version_id 知识点版本id&lt;br&gt;knowledge_node_id&amp;nbsp; &amp;nbsp; 知识点节点id&lt;br&gt;knowledge_parent_node_id 知识点父节点id&lt;br&gt;knowledge_sort&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;知识点排序&lt;br&gt;node_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 节点类型 0根节点 1枝节点 2叶节点&lt;br&gt;knowledge_no&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;jyeoo编号&lt;br&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 懂你课程id&lt;br&gt;course_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; 菁优课程id&lt;br&gt;knowledge_status&amp;nbsp; &amp;nbsp; &amp;nbsp;知识点状态 1可用 0禁用" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="2960" width="560" height="360" as="geometry"/></mxCell><mxCell id="30" value="API 九 获取教材&lt;br&gt;&lt;strike&gt;GET http://api.jyeoo.com/v1/{subject}/book2&lt;/strike&gt;&lt;br&gt;&lt;strike&gt;&amp;nbsp; &amp;nbsp; 新接口少了一些字段&amp;nbsp;&lt;/strike&gt;&lt;br&gt;使用旧的接口&lt;br&gt;GET http://api.jyeoo.com/v1/{subject}/book?e=&amp;amp;g=&amp;amp;t=&lt;br&gt;&lt;br&gt;获取所选学科的教材(包括教材章节)&lt;br&gt;参数: subject 即 course_en_name&lt;br&gt;返回: [{Book},...]&lt;br&gt;&lt;br&gt;&lt;pre style=&quot;background-color: rgb(255 , 255 , 255) ; font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;pre style=&quot;font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;说明&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;这一层是&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;Book&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;，&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;children&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;那一层是&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;Category&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;ID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;a2cbf627-b801-4bc1-8eb3-8cd0694403f5&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;GradeID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;8&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;TermID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;1&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;EditionID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;1&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;八年级上&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Cover&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Type&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;0&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Status&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;2&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCountJson&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;应该是题目数量的统计信息，这里不需要&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Subject&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;21&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Term&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;上学期&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Grade&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;八年级&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Edition&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;人教版&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;TypeName&quot;&lt;/span&gt;: &lt;span style=&quot;color: #000080 ; font-weight: bold&quot;&gt;null&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;      {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;        &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;ID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;b8e7a01f-f1cd-40c5-b117-f462d5427a27&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #000080 ; font-weight: bold&quot;&gt;null&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;BookID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;a2cbf627-b801-4bc1-8eb3-8cd0694403f5&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;第&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;1&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;章 机械运动&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Seq&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;1&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCountJson&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;DotName&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;第&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;1&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;章 机械运动&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PointNo&quot;&lt;/span&gt;: &lt;span style=&quot;color: #000080 ; font-weight: bold&quot;&gt;null&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PK&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;a2cbf627-b801-4bc1-8eb3-8cd0694403f5~b8e7a01f-f1cd-40c5-b117-f462d5427a27~&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Subject&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;21&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;          {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;            &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;ID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;5a79e6c4-eea4-4fd7-adbd-b5a8fbc3172e&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;b8e7a01f-f1cd-40c5-b117-f462d5427a27&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;BookID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;a2cbf627-b801-4bc1-8eb3-8cd0694403f5&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;1.1 &lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;长度和时间的测量&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Seq&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;1&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCountJson&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;DotName&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;1.1 &lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;长度和时间的测量&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PointNo&quot;&lt;/span&gt;: &lt;span style=&quot;color: #000080 ; font-weight: bold&quot;&gt;null&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PK&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;a2cbf627-b801-4bc1-8eb3-8cd0694403f5~5a79e6c4-eea4-4fd7-adbd-b5a8fbc3172e~&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Subject&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;21&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Points&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;              {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;                &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Key&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;61&quot;&lt;/span&gt;,&lt;br&gt;                &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Value&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;时间的估测&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;              &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;}&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;            ]&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;CategoryPoints&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;              {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;                &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;CategoryID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;5a79e6c4-eea4-4fd7-adbd-b5a8fbc3172e&quot;&lt;/span&gt;,&lt;br&gt;                &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PointNo&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;61&quot;&lt;/span&gt;,&lt;br&gt;                &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCountJson&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;                &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Subject&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;0&lt;/span&gt;,&lt;br&gt;                &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;PointName&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;时间的估测&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;              &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;}&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;            ]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;          }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;        ]&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Points&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;CategoryPoints&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;      }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    ]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;]&lt;/span&gt;&lt;/pre&gt;&lt;/pre&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="880" y="1400" width="920" height="1520" as="geometry"/></mxCell><mxCell id="31" value="API 五 获取考点&lt;br&gt;GET http://api.jyeoo.com/v1/{subject}/point2&lt;br&gt;获取所选学科的考点&lt;br&gt;参数: subject 即 course_en_name&lt;br&gt;返回: [{Point},...]&lt;br&gt;&lt;br&gt;&lt;pre style=&quot;background-color: rgb(255 , 255 , 255) ; font-family: &amp;#34;consolas&amp;#34; ; font-size: 12pt&quot;&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;比&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;point&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;多了&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;Desc&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;跟&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;QuesCount&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;但是数据量大很多&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;No&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;1&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;物质&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;      {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;        &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;No&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;1&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;物态变化&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&quot;&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;          {&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;            &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;No&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;11&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Name&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;物质的物理特征&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Desc&quot;&lt;/span&gt;: &lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold ; font-family: &amp;#34;新宋体&amp;#34;&quot;&gt;【知识点的认识】物质不需要经过...&lt;/span&gt;&lt;span style=&quot;color: #008000 ; font-weight: bold&quot;&gt;&quot;&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Children&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;/span&gt;,&lt;br&gt;            &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCount&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;{&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;              &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;DegreeID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;3&lt;/span&gt;,&lt;br&gt;              &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;CateID&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;9&lt;/span&gt;,&lt;br&gt;              &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;Count&quot;&lt;/span&gt;: &lt;span style=&quot;color: #0000ff&quot;&gt;17&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #0000ff&quot;&gt;            &lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;}&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;          }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;        ]&lt;/span&gt;,&lt;br&gt;        &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCount&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;      }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;    ]&lt;/span&gt;,&lt;br&gt;    &lt;span style=&quot;color: #660e7a ; font-weight: bold&quot;&gt;&quot;QuesCount&quot;&lt;/span&gt;: &lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;[]&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;  }&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;color: #660e7a ; font-weight: bold ; font-style: italic&quot;&gt;]&lt;/span&gt;&lt;/pre&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="880" y="2960" width="920" height="720" as="geometry"/></mxCell><mxCell id="32" value="tiku.t_jyeoo_book&lt;br&gt;菁优教材表&lt;br&gt;&lt;br&gt;&lt;div&gt;jyeoo_book_id&lt;/div&gt;&lt;div&gt;[UK]book_code&amp;nbsp; ID&lt;/div&gt;&lt;div&gt;book_name&amp;nbsp; &amp;nbsp; &amp;nbsp; Name&lt;/div&gt;&lt;div&gt;book_desc&amp;nbsp; &amp;nbsp; &amp;nbsp; Desc&lt;/div&gt;&lt;div&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;course_code&amp;nbsp; &amp;nbsp; Subject&lt;/div&gt;&lt;div&gt;grade_type&amp;nbsp; &amp;nbsp; &amp;nbsp;GradeID&lt;/div&gt;&lt;div&gt;term_type&amp;nbsp; &amp;nbsp; &amp;nbsp; TermId&lt;/div&gt;&lt;div&gt;edition_type&amp;nbsp; &amp;nbsp;EditionID&lt;/div&gt;&lt;div&gt;grade_name&amp;nbsp; &amp;nbsp; &amp;nbsp;Grade&lt;/div&gt;&lt;div&gt;term_name&amp;nbsp; &amp;nbsp; &amp;nbsp; Term&lt;/div&gt;&lt;div&gt;edition_name&amp;nbsp; &amp;nbsp;Edition&lt;/div&gt;&lt;div&gt;book_cover&amp;nbsp; &amp;nbsp; &amp;nbsp;Cover&lt;/div&gt;&lt;div&gt;book_type&amp;nbsp; &amp;nbsp; &amp;nbsp; Type&lt;/div&gt;&lt;div&gt;book_status&amp;nbsp; &amp;nbsp; Status&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="610" y="1640" width="260" height="360" as="geometry"/></mxCell><mxCell id="33" value="&lt;div&gt;tiku.t_jyeoo_category&lt;/div&gt;&lt;div&gt;菁优章节表&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;jyeoo_category_id&amp;nbsp;&lt;/div&gt;&lt;div&gt;[UK]category_code ID&amp;nbsp;&lt;/div&gt;&lt;div&gt;jyeoo_book_id&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;book_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;BookID&lt;/div&gt;&lt;div&gt;category_name&amp;nbsp; &amp;nbsp; &amp;nbsp;Name&lt;/div&gt;&lt;div&gt;category_desc&amp;nbsp; &amp;nbsp; &amp;nbsp;Desc&lt;/div&gt;&lt;div&gt;category_seq&amp;nbsp; &amp;nbsp; &amp;nbsp; Seq&lt;/div&gt;&lt;div&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;course_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;Subject&amp;nbsp;&lt;/div&gt;&lt;div&gt;node_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;parent_node_id&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;node_sort&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;node_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;node_level&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;dot_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; DotName&lt;/div&gt;&lt;div&gt;category_pk&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;PK&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="610" y="2040" width="260" height="400" as="geometry"/></mxCell><mxCell id="34" value="tiku.t_jyeoo_knowledge_category&lt;br&gt;菁优知识点章节关联表表&lt;br&gt;&lt;br&gt;jyeoo_knowledge_category_id&amp;nbsp; &amp;nbsp;id&lt;br&gt;jyeoo_knowledge_id&amp;nbsp; &amp;nbsp;知识点id&lt;br&gt;knowledge_no&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;jyeoo编号&lt;br&gt;knowledge_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;jyeoo名称&lt;br&gt;category_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; jyeooCategoryId&lt;br&gt;jyeoo_category_id&amp;nbsp; &amp;nbsp; 章节id" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="40" y="2450" width="560" height="190" as="geometry"/></mxCell><mxCell id="35" value="&lt;div&gt;t_jyeoo_knowledge_category&lt;/div&gt;&lt;div&gt;菁优知识点章节关联表表&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;jyeoo_knowledge_id&amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_no&amp;nbsp; &amp;nbsp; Key&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_name&amp;nbsp; Value&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;category_code&amp;nbsp; &amp;nbsp;ID&lt;/div&gt;&lt;div&gt;jyeoo_category_id&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="610" y="2450" width="260" height="190" as="geometry"/></mxCell><mxCell id="36" value="&lt;div&gt;tiku.t_jyeoo_knowledge&lt;/div&gt;&lt;div&gt;菁优知识点表&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;jyeoo_knowledge_id&amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_name&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;Name&lt;/div&gt;&lt;div&gt;knowledge_level&amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_describle&amp;nbsp; Desc&lt;/div&gt;&lt;div&gt;knowledge_version_id&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_node_id&amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_parent_node_id&lt;/div&gt;&lt;div&gt;knowledge_sort&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;&lt;div&gt;node_type&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_no&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;No&lt;/div&gt;&lt;div&gt;course_id&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;course_code&amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp; &amp;nbsp;&amp;nbsp;&lt;/div&gt;&lt;div&gt;knowledge_status&amp;nbsp; &amp;nbsp; &amp;nbsp;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontFamily=Courier New;align=left;verticalAlign=top;fontSize=16;" parent="1" vertex="1"><mxGeometry x="610" y="2960" width="260" height="360" as="geometry"/></mxCell></root></mxGraphModel></diagram></mxfile>