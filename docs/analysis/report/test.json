{"scoreLineOverview": {"memberCount": -15086639.805745512, "stuCount": -10602228.783048287, "absentCount": -15653547.456990898, "maxScore": 74192058.3588062, "avgScore": 81606494.95005864, "scoreLineDistribute": [{"line": "ullamco", "name": "non aliqua", "stuCount": 7948196.238977537, "score": 18209946.81841235, "percent": 37970781.72450054}, {"line": "<PERSON><PERSON><PERSON> al<PERSON>ua", "name": "laborum dolore anim dolore", "stuCount": -44151924.58393999, "score": 5033542.902075052, "percent": -35211192.83268392}, {"line": "laborum anim", "name": "minim ex non ut culpa", "stuCount": 92910524.95301232, "score": -19950581.176214352, "percent": 19741535.021146238}, {"line": "culpa in irure cupidatat", "name": "do", "stuCount": -89971453.17731969, "score": 74453199.39680201, "percent": -66293103.945871845}]}, "detail": {"total": {"courseScoreLevelStats": {"header": [{"name": "tempor veniam"}, {"name": "proident <PERSON>eur"}], "data": [["in culpa exercitation dolor", "ea id aliquip", "officia mollit"], ["cupidatat pariatur cillum"], ["irure dolor consectetur"], ["ad ullamco et do", "irure mollit", "esse mollit dolor", "nostrud ex occaecat", "nulla id"], ["ul", "deserunt dolore", "Excepteur", "elit consectetur dolore ex dolore"]]}, "memberScoreLevelsDiagram": {"header": [{"name": "elit cillum qui proident"}, {"name": "eu minim"}, {"name": "tempor esse ipsum ad"}, {"name": "adipisicing exercitation mollit"}], "data": [["ea proident", "exercitation Lorem aute est incididunt", "non dolor sit"], ["exercitation", "enim in dolore sit consec", "occa<PERSON>t", "in consequat pariatur laboris", "velit"]]}, "memberScoreLevels": {"header": [{"name": "ipsum aliqua Duis nulla", "groupKey": "incididunt et"}, {"name": "in minim ut", "groupKey": "velit"}, {"name": "enim velit dolore voluptate", "groupKey": "labore reprehenderit sunt fugiat"}], "data": [["laboris officia dolore consectetur", "<PERSON>is proident commodo", "voluptate"], ["minim proident et commodo", "culpa"], ["aliqua", "ipsum dol"], ["mollit aute id", "voluptate deserunt dolor"]], "scoreLevels": [{"levelOrder": 41574842.80000645, "levelName": "non sit ullamco", "scope": "est mollit"}, {"levelOrder": 56344586.34799367, "levelName": "occaecat aliquip nostrud aute", "scope": "mollit repre"}]}, "memberAvgStats": {"header": [{"name": "dolor quis", "groupKey": "dolore Lorem exercitation mollit veniam"}, {"name": "e<PERSON><PERSON>d elit deserunt", "groupKey": "sed do"}], "data": [["est qui", "aliqua velit", "enim"], ["in consectetur", "ea aliqua eu veniam labore", "sed adipisicing sunt", "fugiat in est"], ["ex", "laboris labore aute amet", "consectetur"], ["ut culpa quis magna", "nostrud in eiusmod", "laborum Ut"]], "aboveAvgMembers": ["irure elit adipisicing", "culpa ut Lorem minim"], "highestMembers": ["qui", "velit magna deserunt est", "nulla nostrud sunt dolor culpa"], "belowAvgMembers": ["id officia nulla elit ullamco", "non proident adipisicing consectetur", "commodo aliquip consectetur ullamco", "sint Duis ad", "ut"], "lowestMembers": ["veniam dolore", "culpa in laboris quis", "e<PERSON><PERSON><PERSON> aliqua sint", "Excepteur in"]}, "memberCourseRanks": {"header": [{"name": "ex Excepteur magna ", "groupKey": "exercitation mollit"}], "data": [["sit officia reprehend"], ["in ex ipsum", "laboris id ut magna"], ["do cupidatat in exercitation", "aliquip reprehenderit qui"]], "courseTopSchools": [{"courseName": "qui sit amet est sed", "schools": ["est", "occaecat velit sit", "adipisicing exercitation sunt"]}, {"courseName": "ex", "schools": ["cupidatat officia est dolore", "dolor"]}, {"courseName": "ea in", "schools": ["sint", "eiusmod tempor anim", "nostr"]}, {"courseName": "non velit", "schools": ["laborum", "Ut deserunt", "ad dolore amet"]}, {"courseName": "occaecat labore", "schools": ["fugiat"]}]}, "memberCourseEvaluates": {"header": [{"name": "ullamco dolor ad esse magna"}, {"name": "laborum non laboris anim"}, {"name": "veniam eiusmod nulla exercitation"}, {"name": "aliquip dolore esse anim"}, {"name": "in elit dolor aliqua <PERSON>is"}], "data": [["laboris ipsum", "aute laboris fugiat"], ["<PERSON><PERSON> laboris", "culpa", "magna reprehenderit"], ["velit in culpa", "ad", "non", "fugiat voluptate", "anim ul<PERSON> sit"], ["culpa", "irure est ad", "do sed officia sunt", "dolor"], ["dolore incididunt", "in tempor ad non", "veniam id in"]]}, "memberPassStats": {"header": [{"name": "elit velit pariatur laboris ex"}, {"name": "veniam occaecat in mollit"}, {"name": "reprehenderit consectetur minim"}, {"name": "qui consequat amet"}], "data": [["incididunt id Excepteur officia consequat", "<PERSON>rem sint non", "laborum id in"], ["ea ut amet", "cillum ut", "sed ea cillum deserunt", "aliquip Lorem pariatur dolor veniam", "qui Ut"], ["elit", "commodo Ut est"], ["tempor in", "laborum", "culpa tempor in deserunt", "non anim", "sed consequat eiusmod eu"], ["aute elit", "id ipsum laboris non"]], "highestMembers": ["ex ut laborum", "exercitation dolore dolore", "sunt elit minim mollit"]}, "scoreRangeStat": {"stuCount": 58089139.213318855, "detail": [{"rangeName": "sunt aliqua", "count": 98200871.93613178, "accCount": -51116238.751165, "percent": 95039284.80181071}, {"rangeName": "tempor nostrud voluptate ut", "count": 6652194.952649593, "accCount": 6665613.681554243, "percent": 40660388.96238688}, {"rangeName": "in mollit sunt est dolor", "count": -53501226.730657555, "accCount": -38448381.66962381, "percent": 70890983.856392}, {"rangeName": "ad consectetur Ut", "count": 67893654.84669071, "accCount": 67040336.335323006, "percent": -65123422.026647985}]}, "memberScoreRangeStat": {"header": [{"name": "in cupidatat"}, {"name": "iru"}], "data": [["ut cupidatat", "in aliqua"], ["et vol"], ["", "ullamco ex aute mollit", "sit dolore et"]]}, "memberRankLevelStat": {"header": [{"name": "exercitation laborum elit cillum"}, {"name": "occaecat aliquip sunt"}], "data": [["ut dolore cillum pariatur"], ["ut et"], ["quis eu proident", "culpa id", "in proident"], ["est fugiat dolor", "mollit deserunt laboris <PERSON>"], ["ea quis", "consequat magna Lorem aliquip"]]}, "memberExcellentStuStat": {"header": [{"name": "tempor i<PERSON>re Ut consectetur"}, {"name": "deserunt quis do"}, {"name": "irure"}, {"name": "pariatur quis nisi dolor"}], "data": [["consequat dolore aliquip"]], "beforeSchools": [{"beforeRank": "in aliquip", "schools": ["eu non"]}, {"beforeRank": "iru", "schools": ["veniam incididunt ex Excepteur aliqua"]}, {"beforeRank": "qui dolor Excepteur aute", "schools": ["sed"]}, {"beforeRank": "esse ullamco cillum", "schools": ["Excepteur aliqua nisi minim", "aliquip cillum proident", "ex do nisi", "labore"]}]}, "memberBoxPlotStat": {"header": [{"name": "no<PERSON><PERSON> officia sit l"}, {"name": "nisi ea ullamco consequat"}, {"name": "proident Ut cillum "}, {"name": "sint dolore id"}], "data": [["sed culpa Ut", "adipisicing commodo"], ["dolore", "adipisicing cupidatat", "sunt"], ["laboris tempor culpa", "tempor incididunt elit", "aliqua commodo", "amet dolore"], ["ut Ut adipisicing laborum", "sit"]]}, "memberOverlineStat": {"header": [{"name": "dolore culpa"}, {"name": "dolor anim adipisicing"}, {"name": "nostrud amet voluptate"}, {"name": "dolore"}], "data": [["qui nisi"]], "scoreLineInfos": [{"line": "do", "score": -63193154.0297344, "stuCount": -42566152.3680748, "percent": 12710162.112864643, "highestMembers": ["incididunt ad cu", "Lorem ipsum cillum aute", "fugiat cillum esse", "aliqua molli"], "lowestMembers": ["Ut tempor reprehend<PERSON>t", "Duis velit esse dolore", "Lorem ut amet ea qui"]}, {"line": "nisi aliquip", "score": 78607705.31062105, "stuCount": -44980357.88909136, "percent": -64573687.23125141, "highestMembers": ["reprehend<PERSON>t sed", "consectetur id", "cupidatat ea nulla"], "lowestMembers": ["culpa dolore", "magna elit", "occaecat do"]}]}, "memberAroundBoundStat": {"header": [{"name": "in esse"}, {"name": "proident aliquip in"}, {"name": "consectetur ea nulla aliqua"}, {"name": "deserunt esse est reprehenderit"}, {"name": "nulla"}], "data": [["dolor nulla <PERSON> non", "ut aute", "occaecat pariatur dol", "eius"], ["in ci", "tempor proident labore in conse", "est Ut ut sit", "officia ad aliqua aute", "in consequat anim elit cillum"], ["Ut adipisicing et", "nisi sint", "amet est dolore"], ["dolore nostrud dolore", "aute consectetur veniam ullamc", "eiusmod est et magna"], ["nostru"]]}, "memberOverLineRateStat": [{"lineName": "et cillum in", "header": [{"name": "deserunt"}, {"name": "in Excepteur adipisicing quis"}, {"name": "pariatur ut velit veniam"}, {"name": "dolor culpa cillum"}, {"name": "mollit ipsum occaecat deserunt"}], "data": [["cillum anim"], ["tempor officia elit nostrud nulla"], ["et proident", "magna", "ex ut", "c"], ["culpa nulla ullamco elit", "sed enim pariatur", "velit Ut minim"]]}, {"lineName": "aliquip et culpa enim", "header": [{"name": "dolore aliquip"}, {"name": "sunt nulla officia"}], "data": [["ut qui ullamco", "in dolore", "veniam ut sunt Excepteur elit", "dolore magna eli"], ["aute voluptate exercitation Lorem id", "fugiat minim nisi", "sit fugiat", "consectetur officia quis aute", "sit exercitation"]]}, {"lineName": "irure sunt", "header": [{"name": "aliquip non Ut"}, {"name": "amet consectetur commodo occaecat"}], "data": [["quis enim non", "occa<PERSON>t nostrud dolor"], ["labore id est", "culpa nostrud tempor mollit Excepteur", "Ut voluptate eiusmod ut fugiat", "pariatur exercitation Ut est"], ["incididunt", "ipsum Ut si", "ni"]]}, {"lineName": "occaecat cillum fugiat magna proident", "header": [{"name": "<PERSON><PERSON>"}, {"name": "commodo anim <PERSON> minim"}], "data": [["nulla dolor ipsum ea", "Ut in"]]}, {"lineName": "Lorem adipisicing laboris", "header": [{"name": "magna incididunt id"}], "data": [["proident Ut amet laborum", "consectetur id aliqua"], ["culpa est"], ["irure qui", "Ut consequat", "dolore aliqua <PERSON> aliquip", "enim laboris"]]}], "memberContributeStat": [{"lineName": "nostrud incididunt laboris", "header": [{"name": "dolor culpa do laborum officia"}, {"name": "velit do cupidatat aliqua"}], "data": [["laboris sed tempor", "ea", "laborum proident magna incididunt sint"], ["veniam adipisicing", "ut dolor pariatur"]]}, {"lineName": "dolor", "header": [{"name": "proident amet voluptate laboris"}, {"name": "cupi"}, {"name": "est aute id"}], "data": [["voluptate incididunt <PERSON>", "dolore deserunt cupidatat", "pariatur irure anim consectetur ullamco"], ["dolor eu reprehenderit nulla", "elit consequat aliqua in Excepteur", "enim magna temp", "<PERSON><PERSON> nost<PERSON> dolore e<PERSON>", "ut occaecat fugiat"], ["officia veniam sed", "ipsum sed proident adi", "in ex minim ea enim"], ["ex elit dolore", "nisi <PERSON>eur esse sunt", "laboris"]]}, {"lineName": "ut labor", "header": [{"name": "non dolore ut cupidatat"}, {"name": "ut officia"}, {"name": "minim cupidatat"}], "data": [["elit non incididunt", "i<PERSON>re enim culpa Ut in", "est"], ["deserunt commodo et dolore", "Excepteur deserunt dolore mollit"], ["id adipisicing", "dolor quis", "amet", "sit nisi ullamco"], ["eius<PERSON><PERSON> des", "", "ea dolor", "occaecat ea ullamco <PERSON>", "cupidat"]]}, {"lineName": "dolore ul<PERSON> se", "header": [{"name": "ea reprehenderit Lo<PERSON> sed"}, {"name": "magna quis ven"}], "data": [["nisi", "dolore dolor"], ["commodo enim", "ullamco pariatur in sunt", "aute enim id quis"], ["reprehenderit nisi do", "sunt Excepteur aliqua i<PERSON>re", "aute exercitation elit cillum"], ["irure no"]]}], "paperQuality": {"header": [{"name": "qui occaecat"}, {"name": "aute quis occaecat labore"}], "data": [["ea elit in", "aute Excepteur in", "et", "ut id elit officia"], ["id elit cillum et", "veniam id commod"], ["Duis ipsum", "eu <PERSON>t", "sed n"]]}}, "courses": [{"courseId": 68804381.27615297, "courseName": "est non cillum", "overview": {"maxScore": 88209989.6447404, "fullScore": -59471664.16619107, "avgScore": 24910540.542958826, "difficulty": -29041753.249690577, "reliability": 43960687.40076381, "discrimination": 81635359.98291907}, "courseScoreLevelStats": {"header": [{"name": "et ex Excepteur elit non"}, {"name": "dolor exercitation commodo pariatur"}], "data": [[], [], [], [], []]}, "memberScoreLevelsDiagram": {"header": [{"name": "est culpa in"}, {"name": "amet aliquip"}, {"name": "nisi cupidatat in nostrud"}, {"name": "id se"}, {"name": "ea <PERSON>t"}], "data": [["ut irure sit", "culpa sunt velit veniam"], ["commodo ut officia", "et ad anim", "incididunt non esse anim"]]}, "memberScoreLevelsAnalysis": {"header": [{"name": "aliquip esse"}, {"name": "nulla su"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "data": [["elit adipisicing", "Excepteur et magna dolor amet"], ["dolore veniam", "occaecat consectetur magna", "nostrud occaecat ut ut reprehenderit", "tempor anim cupidatat velit", "dolor dolore"], ["Excepteur do voluptate dolor", "Duis pariatur aute eiusmod elit", "sit"]]}, "memberScoreLevels": {"header": [{"name": "enim", "groupKey": "quis adipisicing Lorem voluptate"}, {"name": "veniam aliqua ad", "groupKey": "eu consequat in proident"}, {"name": "velit in consectetur", "groupKey": "veniam nisi sit adipisicing"}, {"name": "in esse anim laborum", "groupKey": "commodo dolore proident"}], "data": [["non voluptate velit Ut", "ullamco e", "velit Ut labore"], ["consectetur cillum ut tempor", "consectetur exercitation in"]], "scoreLevels": [{"levelOrder": 84594442.45841515, "levelName": "officia irure est", "scope": "<PERSON>"}, {"levelOrder": 43171067.437520295, "levelName": "sed sunt occaecat exercitation", "scope": "ad laborum dolor nostrud"}, {"levelOrder": 36679950.52110919, "levelName": "exercitation ut ad", "scope": "amet minim"}, {"levelOrder": -58238505.584234715, "levelName": "eu in veniam", "scope": "minim"}]}, "memberAvgStats": {"header": [{"name": "adipisicing", "groupKey": "ut sint deserunt"}, {"name": "quis <PERSON> e<PERSON>mo", "groupKey": "dolor anim"}, {"name": "sed", "groupKey": "in do Duis"}, {"name": "eiusmod voluptate consectetur id", "groupKey": "ullamco nostrud"}, {"name": "in aliqua dolore incididunt eiusmod", "groupKey": "dolore voluptate"}], "data": [["amet in"]], "aboveAvgMembers": ["nostrud", "qui non magna ipsum dolore", "cupidatat minim", "ex et amet enim sit"], "highestMembers": ["amet do in", "<PERSON>is adipisicing Excepteur"], "belowAvgMembers": ["minim aliquip", "ea", "consectetur"], "lowestMembers": ["magna eu dolore"]}, "scoreRangeStat": {"stuCount": 66862681.26111215, "detail": [{"rangeName": "minim consequat labore", "count": 65205976.970958084, "accCount": -86391136.61158784, "percent": -99448835.3548777}, {"rangeName": "Ut cillum reprehenderit non voluptate", "count": -5660673.403546974, "accCount": 49966401.29956943, "percent": -46376161.61419991}, {"rangeName": "e", "count": -29726126.673710555, "accCount": 74119298.51656818, "percent": 91462030.82553345}, {"rangeName": "Duis incid", "count": 94024776.119993, "accCount": -85612980.4192419, "percent": -23344279.160132065}, {"rangeName": "aliqua magna dol", "count": 21773290.86699702, "accCount": -33620106.84441703, "percent": 64812288.13285002}]}, "memberScoreRangeStat": {"header": [{"name": "irure cupidatat quis"}, {"name": "laborum"}, {"name": "aute dolore aliqua mollit"}], "data": [["consequat aliquip comm", "cupidatat sit", "Excepteur <PERSON><PERSON><PERSON>"], ["dolore amet", "labore", "veniam occaeca", "voluptate in Excepteur aute"], ["cillum Lorem voluptate", "deserunt et", "dolore do"]]}, "memberRankLevelStat": {"header": [{"name": "ut dolor Ut"}, {"name": "cillum esse"}, {"name": "id irure"}, {"name": "irure consectetur ex"}], "data": [["pariatur consectetur cillum occaecat Excepteur", "enim voluptate proident ullamco", "enim dolor laboris e<PERSON>d", "dolore non est elit"]]}, "memberExcellentStuStat": {"header": [{"name": "mollit eiusmod non labore Lorem"}, {"name": "dolor incididunt"}], "data": [["dolore cupidatat in", "<PERSON><PERSON><PERSON> id", "fugiat n"], ["cupidatat amet occaecat", "do mollit commodo", "aliqua"]]}, "memberBoxPlotStat": {"header": [{"name": "sit aute"}, {"name": "id"}, {"name": "ad dolor dolore elit consequat"}, {"name": "exercitation esse labore"}, {"name": "ut nostrud amet"}], "data": [["occaecat et", "fugiat minim deserunt Ut"], ["exercitation ut", "dolor ipsum et labore"], ["laborum fugiat", "in sunt", "amet aute"]]}, "memberOverlineStat": [{"lineName": "consectetur in ea", "header": [{"name": "in elit dolore esse"}, {"name": "in deserunt consectetur dolor"}], "data": [["ex exercitation ut dolore", "magna ut Duis laborum cupidata", "<PERSON><PERSON><PERSON>"], ["cillum ad nulla", "culpa qui", "pariatur enim", "ipsum"], ["Excepteur laborum adipisicing", "dolore aute", "velit eiusmod Ut sit", "amet laborum", "pariatur magna ullamco dolore officia"], ["Ut", "eu dolore", "laboris"]]}, {"lineName": "e<PERSON><PERSON><PERSON> in", "header": [{"name": "laboris sunt aliqua"}, {"name": "consequat"}, {"name": "dolore dolore "}, {"name": "magna fugiat aliquip"}, {"name": "velit mollit fugiat"}], "data": [["elit officia nulla", "reprehend<PERSON><PERSON>", "deserunt ut adipisicing", "dolore voluptate ipsum e", "sint"], ["in officia", "exercitation ipsum anim", "commodo id in adipisicing", "culpa ad"]]}], "memberAroundBoundStat": {"header": [{"name": "velit dolor do aliqua"}, {"name": "quis esse dolor"}], "data": [["labore ea", "commodo ex dolor adipisicing in"], ["laboris sunt laborum cillum", "nulla eiusmod ", "reprehenderit exercitation nisi ex mollit"], ["deserunt eu", "Lorem sit consequat ea", "aliquip ipsum"]]}, "paperQuality": [{"paperId": -58258380.30146939, "paperName": "nulla magna sint", "reliability": {"value": -85551685.31517321, "level": "nisi officia cupidatat", "metric": "incididunt in adipisicing deserunt sed", "metricValue": 67695763.39175943}, "difficulty": {"value": 36593114.01617709, "level": "<PERSON><PERSON> al<PERSON>ua", "metric": "anim", "metricValue": 97344005.12460658}, "discrimination": {"value": 20258917.256878257, "level": "magna", "metric": "occaecat labore", "metricValue": 14780763.969699696}, "easyQn": "deserunt velit occaecat Lorem", "hardQn": "dolore anim fugiat", "lowDiscriminationQn": "ea tempor incididunt", "excellentQn": "dolore <PERSON> dolor"}, {"paperId": -28509670.686222494, "paperName": "eu elit", "reliability": {"value": 83272516.56344873, "level": "proident minim aute magna", "metric": "Ut ullamco n", "metricValue": 79363533.41128871}, "difficulty": {"value": -3443815.491745591, "level": "culpa anim consectetur reprehenderit cillum", "metric": "cupidatat occaecat quis ipsum", "metricValue": -23077886.310447365}, "discrimination": {"value": -56671914.76852946, "level": "adipisicing Ut amet laboris", "metric": "d", "metricValue": -9658711.824630111}, "easyQn": "nostrud dolore in culpa", "hardQn": "in aliquip aute", "lowDiscriminationQn": "cupidatat in", "excellentQn": "ullamco aute"}], "discrimination": [{"paperId": 4092922.4776611775, "paperName": "cupidatat reprehenderit", "header": [{"name": "occaecat minim in irure mollit"}, {"name": "sit ut sunt aute"}], "data": [["anim culpa Ut officia ipsum", "nulla", "ut consequat "], ["quis nisi esse sint", "ad quis reprehenderit ullamco"]]}, {"paperId": -41968072.02024897, "paperName": "Duis deserunt ut nostrud", "header": [{"name": "dolor"}, {"name": "ea eu"}], "data": [["amet et", "nulla sunt"], ["velit ea eu cillum", "dolore laborum aliquip", "tempor cupidatat nulla proident", "occaecat nulla"]]}, {"paperId": 90919779.00285113, "paperName": "ut", "header": [{"name": "do <PERSON>t"}, {"name": "dolore est cupidatat nisi"}, {"name": "enim tempor"}], "data": [["dolor voluptate sint", "consectetur fugiat ut", "Ut exercitation", "sint deserunt"], ["culpa ea", "dolore"], ["sunt adipisicing nostrud non", "commodo tempor ipsum", "in ad deserunt"], ["culpa nulla magna sint", "sunt dolor occaecat"]]}, {"paperId": -85015555.92465277, "paperName": "sit", "header": [{"name": "esse velit Ut minim laboris"}, {"name": "pariatur Lo<PERSON> eu"}, {"name": "ea dolor sunt officia amet"}], "data": [["culpa elit magna laboris", "aute est exercitation"], ["anim <PERSON> nisi", "fugiat in", "voluptate eu"], ["Ut sit amet commodo eu", "non Excepteur aute proident irure"], ["elit fugiat", "consectetur ei"]]}], "difficulty": [{"paperId": -45742034.83964707, "paperName": "minim nulla", "header": [{"name": "consequat commodo aliquip d"}], "data": [["eu ut", "sint sed magna id amet"]]}, {"paperId": -41933825.272142045, "paperName": "ad <PERSON>is", "header": [{"name": "deserunt fugiat laboris culpa veniam"}, {"name": "qui eiusmod ad ipsum"}, {"name": "sunt"}, {"name": "occaecat ut adipisicing"}], "data": [["ipsum aute Ut in", "nulla esse ut"], ["fugiat aliquip Excepteur anim", "commodo et dolore", "sint e", "aliqua <PERSON> s"]]}, {"paperId": 26692676.196300536, "paperName": "elit sunt commodo velit consectetur", "header": [{"name": "dolor sed reprehenderit"}, {"name": "do esse proide"}, {"name": "mollit sunt tempor id"}, {"name": "dolor"}], "data": [["deserunt", "in Ut amet", "quis dolor eu"], ["elit", "in tempor dolor volup", "dolore"], ["fugiat e<PERSON>d", "nulla proident mollit labore", "deserunt eiusmod tempor Excepteur", "aute nostr", "m"], ["<PERSON><PERSON> ex", "aliqua", "occaecat ullamco voluptate", "ea culpa voluptate nulla", "ea dolore Excepteur elit"]]}, {"paperId": -81745458.13450868, "paperName": "est voluptate cillum nis", "header": [{"name": "in culpa pariatur"}, {"name": "elit et deserunt"}], "data": [["ut proident Lorem magna"], ["cupidatat consectetur labore nostrud", "et", "ea"], ["velit esse proident reprehend", "nul", "adipisicing magna velit officia enim", "culpa ipsum"], ["cupidatat ut", "deserunt laboris aliquip <PERSON>"]]}], "questionTypeStat": [{"paperId": 7155648.143874109, "paperName": "tempor eiusmod magna", "header": [{"name": "dolor aliquip"}, {"name": "est consequat elit"}, {"name": "cillum labore"}, {"name": "incididunt id magna non sunt"}], "data": [["fugiat commodo null", "et sunt commodo", "adipisicing aliquip ea"], ["aute <PERSON>", "Duis voluptate"], ["magna ut", "ea aliqua ad anim", "magna exercitation dolore amet", "laborum culpa", "Excepteur consequat elit v"], ["dolore occa<PERSON>t", "qui non et sint", "do in consectetur"]]}, {"paperId": 80928841.81543168, "paperName": "minim elit nulla esse", "header": [{"name": "laborum"}], "data": [["laborum eiusmod laboris tempor", "aliquip nostrud quis est mollit", "quis <PERSON>", "tempor dolore consectetur"], ["pariatur esse", "aute commodo sit ea", "et id", "i<PERSON>re fugiat"], ["incididunt ut occaecat Lorem", "tempor commodo nostrud "], ["elit proident ipsum adipisicing", "qui occaecat"], ["dolore", "<PERSON><PERSON><PERSON><PERSON> o<PERSON>"]]}, {"paperId": 8158057.094342962, "paperName": "d", "header": [{"name": "Duis voluptate veniam aliqua"}, {"name": "culpa et veniam dolore"}, {"name": "occaecat officia elit"}], "data": [["deserunt", "occaecat ex sit aliquip", "in eu adipisicing"], ["consequat", "pariatur ea aute cupidatat nostrud"], ["voluptate qui sit", "irure commodo"], ["<PERSON><PERSON> esse ", "<PERSON>is minim", "mollit laboris proident", "labore enim"], ["sed incididunt", "mollit cillum Excepteur et commodo"]]}, {"paperId": 25588989.63605371, "paperName": "pariatur laborum", "header": [{"name": "ut dolore Excepteur"}, {"name": "cil"}, {"name": "sint deserunt magna exercitation adipisicing"}], "data": [["Excepteur culpa voluptate ven", "voluptate anim ea occaecat", "laboris e<PERSON>", "velit commodo dolore sunt ad"], ["consequat eu eius<PERSON>d <PERSON>", "et commodo sed Excepteur adipisicing"]]}], "questionStat": [{"paperId": 7567615.808476314, "paperName": "cillum eu do", "header": [{"name": "al<PERSON><PERSON><PERSON>"}, {"name": "amet exercitation elit"}, {"name": "sint sed est"}], "data": [["non nisi fugiat", "irure velit", "elit", "ea velit laboris ut mollit"], ["laborum aute ut fugiat velit", "ullamco proident in Ut mollit", "cupidatat occaecat", "anim non in Lorem nulla"]]}], "knowledgeStat": [{"paperId": 91217587.55365023, "paperName": "id reprehenderit ea enim laborum", "header": [{"name": "in nostrud incididunt commodo nulla"}, {"name": "proident voluptate dolore non"}], "data": [["in in laboris"], ["tempor"], ["Ut non eu", "enim Duis ipsum"], ["minim eiusmod incididunt", "labore deserunt sunt", "Excepteur i<PERSON>re", "deserunt in sit cons"]]}, {"paperId": -44276960.01768893, "paperName": "commodo in con", "header": [{"name": "tempor occaecat consectetur eiusmod et"}, {"name": "Duis elit ex sed"}], "data": [["ut <PERSON>is", "aliqua labo", "dolo", "do dolor"], ["ut enim", "ea qui dolor velit", "non nisi in", "<PERSON><PERSON>", "velit deserunt ut id"]]}, {"paperId": -76482325.11348878, "paperName": "proident aliquip minim fugiat", "header": [{"name": "mollit"}, {"name": "dolor"}], "data": [["elit", "ex et ea", "occaecat quis"], ["adipisicing occaecat in", "in", "elit tempor in", "dolore in aliqua"], ["elit reprehenderit in id est", "laborum"]]}, {"paperId": -34263557.744299255, "paperName": "nulla", "header": [{"name": "enim"}, {"name": "sunt magna nisi adipisicing occaecat"}], "data": [["in officia", "est mollit m", "ullamco esse"], ["ut id minim", "dolore in", "ad mollit", "dolor e<PERSON>"], ["irure veniam dolore <PERSON>eur", "fugiat et", "mollit in pariatur non"]]}]}, {"courseId": -39139665.08004694, "courseName": "ull", "overview": {"maxScore": 94317588.55286741, "fullScore": 63964377.91103265, "avgScore": -22757526.15400389, "difficulty": -68841296.85679707, "reliability": 63770377.14421338, "discrimination": -373186.0891248286}, "courseScoreLevelStats": {"header": [{"name": "qui sed"}, {"name": "officia labore exercitation ut"}, {"name": "sint pariatur"}], "data": [[], []]}, "memberScoreLevelsDiagram": {"header": [{"name": "consequat exercitation commodo"}, {"name": "tempor"}, {"name": "et Lorem consequat cupidatat officia"}], "data": [["est ut in", "deserunt laborum pariatur culpa dolore", "Lo"]]}, "memberScoreLevelsAnalysis": {"header": [{"name": "ut sint fugia"}, {"name": "sed dolor aliqua eiusmod"}, {"name": "n"}, {"name": "Duis cupidatat sunt"}], "data": [["ex", "dolor eu amet", "esse elit Ex", "ut deserunt"]]}, "memberScoreLevels": {"header": [{"name": "esse e<PERSON><PERSON> et aliquip <PERSON>eur", "groupKey": "aliqua anim"}, {"name": "dolore amet", "groupKey": "laborum eiusmod"}, {"name": "Excepteur eu", "groupKey": "dolor dolor proident consectetur"}], "data": [["dolore pariatur", "et aute in", "laborum ex velit"], ["Duis amet qui"], ["sunt mollit sit"], ["magna officia non ullamco", "exercitation do", "laborum commodo", "esse in"], ["est", "esse cillum Ut ullamco", "esse anim", "ea quis amet", "eu ad"]], "scoreLevels": [{"levelOrder": 10070374.432968393, "levelName": "<PERSON><PERSON> mollit", "scope": "velit quis laboris"}, {"levelOrder": -82976895.23982376, "levelName": "et esse", "scope": "et nulla dolor mollit"}]}, "memberAvgStats": {"header": [{"name": "cillum sit Duis consequat", "groupKey": "enim exercitation laboris pariatur"}, {"name": "veniam occaecat ut incididunt", "groupKey": "sint nostrud veniam culpa"}, {"name": "eu Ut cupidatat", "groupKey": "aute amet enim ex"}], "data": [["mollit Lorem", "ut id", "enim id officia Lorem", "reprehende"], ["culpa", "nulla <PERSON>", "ea"], ["deserunt in", "dolore magna", "cupidatat magna consectetur"], ["labore adipisicing minim ullamco", "ea", "enim in", "eu"], ["est incididunt", "<PERSON><PERSON><PERSON> deserunt labore"]], "aboveAvgMembers": ["consectetur officia <PERSON>", "sit", "aute adipisicing dolore ul<PERSON>co", "exercitation"], "highestMembers": ["laboris", "e<PERSON><PERSON><PERSON> consequat aliquip", "pariatur Excepteur labore veniam"], "belowAvgMembers": ["pariatur ad est", "mi", "dolor quis dolore nostrud magna", "consequat est", "anim officia Excepteur culpa"], "lowestMembers": ["<PERSON><PERSON><PERSON>", "nostrud dolor consectetur amet", "esse aliqua laboris voluptate", "commodo deserunt adipisicing ex laboris"]}, "scoreRangeStat": {"stuCount": -6638519.696360052, "detail": [{"rangeName": "labore velit ut ipsum", "count": 3493823.324331835, "accCount": -53907889.00322661, "percent": 33720470.868674204}, {"rangeName": "non laboris", "count": -66350190.5007601, "accCount": -32159913.23396121, "percent": 17860627.171963066}, {"rangeName": "enim", "count": -12958848.610648215, "accCount": 37352287.54862088, "percent": -60064712.29584496}, {"rangeName": "non", "count": 40589594.52575278, "accCount": -81458294.07192484, "percent": -8204438.166015387}]}, "memberScoreRangeStat": {"header": [{"name": "occaecat deserunt nulla enim"}, {"name": "in dolo"}, {"name": "dolore consectetur culpa"}], "data": [["enim eiusmod ex exercitation", "sed <PERSON><PERSON>", "dolore reprehenderit", "dolore ex"], ["ex officia consequat", "non id laborum"], ["esse qui est pariatur sit", "exercitation dolore", "qui nulla reprehenderit"]]}, "memberRankLevelStat": {"header": [{"name": "ut"}, {"name": "elit"}], "data": [["paria", "ipsum eu in", "aute et", "proident consectetur et fugiat aute"], ["tempor"], ["in minim do", "pariatur enim eiusmod veniam"]]}, "memberExcellentStuStat": {"header": [{"name": "dolore dolore amet"}, {"name": "esse eiusmod minim"}, {"name": "fugiat aliquip"}, {"name": "anim occaecat exercitation ex adipisicing"}, {"name": "veniam "}], "data": [["in", "Excepteur minim in veniam enim", "et ex"], ["magna eu", "cillum Excepteur consectetur nulla labore", "laboris et sint mollit", "ea aliqua id incididunt sed"]]}, "memberBoxPlotStat": {"header": [{"name": "anim ipsum adipisicing"}, {"name": "veniam"}, {"name": "commodo incididunt"}], "data": [["consectetur sed", "deserunt mollit magn"], ["eius<PERSON>d dolore culpa", "nisi minim ex consectetur ullamco"], ["proident non occaecat", "non in deserunt", "reprehenderit esse non consectetur"], ["veniam ut", "aliquip in fugiat pariatur", "cillum ullamco pariatur fugiat"], ["ut ", "consequat culpa"]]}, "memberOverlineStat": [{"lineName": "dolor", "header": [{"name": "ullamco sed in"}, {"name": "in"}, {"name": "eu aute Duis tempor nisi"}, {"name": "amet eu inci"}], "data": [["et pariatur eiusmod consectetur Duis"], ["elit anim incididunt sit Excepteur", "amet non enim", "ut"], ["laborum pariatur dolore ea occae", "enim in Ut qui", "incidi", "nostrud"]]}, {"lineName": "in velit ut nostrud veniam", "header": [{"name": "in adipisicing"}, {"name": "non"}, {"name": "nisi"}, {"name": "ut sint cillum ex"}, {"name": "nulla consequat ut deserunt"}], "data": [["si", "ex au", "labore do quis", "consec"], ["mollit aliquip anim ipsum"], ["in Duis", "nulla ea in", "Excepteur veniam deserunt"], ["magna dolor sunt laboris", "aliqua veniam in", "nulla"]]}, {"lineName": "<PERSON><PERSON> ani", "header": [{"name": "laboris incididunt exercitation Excepteur amet"}, {"name": "nulla aliqua <PERSON>"}, {"name": "eu aute in"}, {"name": "officia consectetur velit nostrud cupidatat"}], "data": [["tempor eu anim", "in et dolor"], ["amet Excepteur laborum ullamco", "enim minim deserunt", "ipsum cupidatat", "dolore incididunt nostrud", "sunt consequat "]]}], "memberAroundBoundStat": {"header": [{"name": "magna elit"}, {"name": "nulla magna consectetur enim"}, {"name": "tempor"}], "data": [["ad sunt nostrud", "velit mollit exercitation", "dolor"], ["<PERSON><PERSON><PERSON><PERSON> aliquip", "amet sit", "veniam Excepteur", "labore elit aliquip"], ["magna sed aute Duis", "non", "Excepteur elit proident", "fugiat quis labore non", "cupidatat ex ea ut"]]}, "paperQuality": [{"paperId": -49947983.16783746, "paperName": "in eiusmod in", "reliability": {"value": 13704924.137857959, "level": "au", "metric": "eiusmod eu aliqua nulla", "metricValue": -33527281.444752835}, "difficulty": {"value": 11106443.991723359, "level": "occa<PERSON>t", "metric": "Excepteur quis qui nulla", "metricValue": -21128924.639183432}, "discrimination": {"value": 31415602.728023633, "level": "adipisicing exercitation qui reprehenderit", "metric": "dolor amet q", "metricValue": 80288000.35595182}, "easyQn": "irure fugiat in", "hardQn": "in", "lowDiscriminationQn": "amet ipsum culpa Duis", "excellentQn": "aliqua incididunt reprehenderit esse"}, {"paperId": -21746532.048028573, "paperName": "", "reliability": {"value": 46625717.104730636, "level": "aute", "metric": "irure est aute", "metricValue": 27355625.78657347}, "difficulty": {"value": -25721209.561655685, "level": "incididunt consequat", "metric": "esse proident", "metricValue": -35862508.36688372}, "discrimination": {"value": 38261915.38125369, "level": "elit ea", "metric": "laborum n", "metricValue": 90913610.48382044}, "easyQn": "fugiat", "hardQn": "eiusmod incididunt dolor non in", "lowDiscriminationQn": "incididunt irure aliqua tempor", "excellentQn": "minim qui"}], "discrimination": [{"paperId": 69927371.73650923, "paperName": "sint ad", "header": [{"name": "proident"}, {"name": "dolore"}], "data": [["nostrud dolor do laborum adipisicing", "aute", "labore do", "aute"]]}, {"paperId": 56533722.2702429, "paperName": "elit nisi aute Duis", "header": [{"name": "anim quis cillum ut"}, {"name": "labore cupidatat fugiat Exce"}], "data": [["Duis sunt commodo et culpa", "Duis cupidatat mollit in eiusmod", "cupidatat Excepteur nostrud aute ut", "veniam do"], ["et quis", "cupidatat sunt Duis sint culpa", "exercitation", "pariatur veniam", "ad com"], ["nisi in laboris", "voluptate in i", "sunt est qui"], ["Excepteur enim culpa aliqua ut"]]}], "difficulty": [{"paperId": -15724832.561891183, "paperName": "veniam", "header": [{"name": "eu veniam sint ea"}, {"name": "do ut aute pariatur"}], "data": [["a", "minim in eiusmod"], ["dolore in velit Duis", "eu", "pariatur", "esse deserunt aliqua"]]}, {"paperId": -46520960.241077214, "paperName": "adipisicing voluptate velit sint sit", "header": [{"name": "<PERSON><PERSON> an<PERSON>"}, {"name": "aliquip sed exercitation culpa"}, {"name": "ea <PERSON>rem o"}], "data": [["velit esse est laboris", "aliqua enim"], ["commodo culpa do in dolor", "dolore nostrud culpa enim", "magna occaecat cupidatat"], ["fugiat", "proident do aliquip"], ["labore ex exercitation"]]}, {"paperId": 42411107.27191764, "paperName": "voluptate dolor quis", "header": [{"name": "dolore incididunt laborum"}, {"name": "id est fugiat"}, {"name": "consectetur sint velit"}], "data": [["aute", "Lorem sed consectetur"], ["dolor in do"], ["ut Excepteur velit elit Duis", "fugiat aute", "ad qui aute dolor"]]}, {"paperId": -68148005.17138147, "paperName": "esse ad", "header": [{"name": "dolore occaecat commodo consequat sit"}, {"name": "nostrud"}], "data": [["Lorem dolore veniam", "pariatur e<PERSON>", "eiusmod exercitation", "do"], ["et aliquip", "sed aute Ut officia"]]}], "questionTypeStat": [{"paperId": -15954070.988674477, "paperName": "cons", "header": [{"name": "sint incididunt"}, {"name": "<PERSON><PERSON> velit"}, {"name": "id est dolore"}], "data": [["deserunt ", "irure pariatur aute voluptate", "dolor minim", "dolore culpa", "veniam minim"]]}, {"paperId": 58516344.157432795, "paperName": "pr", "header": [{"name": "velit quis aliqua Lorem veniam"}, {"name": "nulla sint pariatur cillum"}], "data": [["tempor ul<PERSON><PERSON> m", "ad exercitation", "mollit ullamco"], ["aliquip labore nulla nisi laboris"], ["n"]]}, {"paperId": 42252344.437167495, "paperName": "incididunt aute fugiat sit in", "header": [{"name": "ullamco est"}, {"name": "nisi pariatur"}, {"name": "Ut ipsu"}, {"name": "pariatur ad commodo proident mollit"}, {"name": "minim culpa dolore"}], "data": [["dolore Ut officia aliqua", "dolore laboris"]]}], "questionStat": [{"paperId": 72296046.64755982, "paperName": "<PERSON><PERSON> aliqua sed in aute", "header": [{"name": "esse consectetur id magna"}, {"name": "irure nulla ut amet"}, {"name": "ut sed"}, {"name": "ea amet culpa qui laborum"}], "data": [["veniam exercitation occaeca"], ["ul", "non nulla", "elit pariatur <PERSON>t", "laborum sint"], ["deserunt ad quis voluptate", "amet dolor", "proi", "pariatur sunt eu in"], ["mollit", "eiusmod minim"], ["ut", "sunt eu cupidatat", "tempor ullamco ipsum"]]}, {"paperId": 86729181.39035928, "paperName": "fugiat culpa", "header": [{"name": "com"}, {"name": "laborum aliqua"}, {"name": "enim veniam tempor ul<PERSON><PERSON> sint"}], "data": [["amet velit dolor ad", "aliquip cillum veniam culpa irure", "sit mollit est cupidatat"], ["in", "fugiat commodo"], ["culpa Lorem ut", "minim laborum sint aliqua eiusmod"]]}, {"paperId": -41471871.81816845, "paperName": "consecte", "header": [{"name": "consectetur"}, {"name": "sint nostrud aliqua ad Ut"}, {"name": "cillum enim officia"}], "data": [["sed in Ut in", "nulla culpa Excepteur est incididunt", "ul<PERSON><PERSON> o<PERSON>", "aliquip commodo ut"], ["veniam ", "sunt"], ["magna in aute ex dolore", "officia sint Duis ut"]]}], "knowledgeStat": [{"paperId": -87434127.11953646, "paperName": "ut tempor qui eiusm", "header": [{"name": "tempor"}, {"name": "veniam quis esse culpa voluptate"}, {"name": "pariatur"}], "data": [["commodo labore", "quis", "anim Excepteur"], ["minim sit quis", "dolor ea "], ["cillum pari", "proident fugiat qui ut", "qui fugiat magna reprehenderit sit", "id vel"], ["qui velit nulla non", "ut aliquip"], ["fugiat ulla", "ut voluptate", "dolore Lorem officia", "est exerc"]]}, {"paperId": -26839564.074365586, "paperName": "in quis", "header": [{"name": "sunt incididunt consectetur laborum"}], "data": [["amet velit magna est inci", "labore ut sed", "reprehend<PERSON>t o<PERSON>t", "proident "], ["aliquip deserunt sunt sed", "esse", "aliquip commodo officia anim est"], ["<PERSON><PERSON> aliquip", "si", "deserunt"]]}, {"paperId": 70872311.88931292, "paperName": "incididunt", "header": [{"name": "consequat <PERSON>eur sed"}, {"name": "sunt dolor exercitation qui"}], "data": [["enim sunt officia nostrud veniam", "dolor cupidatat", "ad", "amet consectetur Ut in reprehenderit"], ["cillum Lorem occaecat", "dolore sed reprehenderit veniam"], ["do"], ["exercitation", "ea sed", "consectetur quis", "pariatur do culpa", "laborum magna"], ["labore"]]}]}]}}